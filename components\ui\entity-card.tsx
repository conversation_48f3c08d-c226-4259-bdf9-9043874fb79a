/**
 * Reusable Entity Card Component
 *
 * A flexible card component for displaying entity information with:
 * - Avatar/icon support
 * - Title and subtitle
 * - Status badges
 * - Action buttons
 * - Responsive design
 * - Hover effects
 * - Customizable layout
 */

"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface EntityCardField {
  label: string;
  value: string | ReactNode;
  className?: string;
}

interface EntityCardAction {
  label: string;
  onClick: () => void;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  disabled?: boolean;
  icon?: ReactNode;
}

interface EntityCardProps {
  // Header
  title: string;
  subtitle?: string;
  description?: string;

  // Avatar/Icon
  avatar?: {
    src?: string;
    fallback: string;
    className?: string;
  };
  icon?: ReactNode;

  // Status
  status?: {
    label: string;
    variant?: "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info";
  };

  // Fields
  fields?: EntityCardField[];

  // Actions
  actions?: EntityCardAction[];

  // Styling
  className?: string;
  hoverable?: boolean;

  // Events
  onClick?: () => void;
}

export function EntityCard({
  title,
  subtitle,
  description,
  avatar,
  icon,
  status,
  fields = [],
  actions = [],
  className,
  hoverable = true,
  onClick,
}: EntityCardProps) {
  return (
    <Card
      className={cn(
        "transition-all duration-200",
        hoverable && "hover:shadow-md cursor-pointer",
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start space-x-3">
          {/* Avatar or Icon */}
          {avatar && (
            <Avatar className={avatar.className}>
              <AvatarImage src={avatar.src} />
              <AvatarFallback>{avatar.fallback}</AvatarFallback>
            </Avatar>
          )}

          {icon && !avatar && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}

          {/* Title and Status */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-base truncate">{title}</CardTitle>
                {subtitle && (
                  <CardDescription className="truncate">{subtitle}</CardDescription>
                )}
              </div>

              {status && (
                <Badge variant={status.variant} className="ml-2 flex-shrink-0">
                  {status.label}
                </Badge>
              )}
            </div>

            {description && (
              <CardDescription className="mt-1 line-clamp-2">
                {description}
              </CardDescription>
            )}
          </div>
        </div>
      </CardHeader>

      {/* Fields */}
      {fields.length > 0 && (
        <CardContent className="space-y-3">
          <div className="space-y-2 text-sm">
            {fields.map((field, index) => (
              <div key={index} className="flex justify-between items-start">
                <span className="text-muted-foreground flex-shrink-0">
                  {field.label}:
                </span>
                <div className={cn("font-medium text-right ml-2", field.className)}>
                  {field.value}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      )}

      {/* Actions */}
      {actions.length > 0 && (
        <CardFooter className="pt-0">
          <div className="flex gap-2 w-full">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || "outline"}
                size={action.size || "sm"}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent card click
                  action.onClick();
                }}
                disabled={action.disabled}
                className="flex-1"
              >
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </Button>
            ))}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}

// Specialized card variants
export function TeacherCard({ teacher, onView, onEdit }: {
  teacher: any;
  onView?: () => void;
  onEdit?: () => void;
}) {
  return (
    <EntityCard
      title={teacher.name}
      subtitle={teacher.subject}
      avatar={{
        src: `https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`,
        fallback: teacher.name.split(' ').map((n: string) => n[0]).join(''),
      }}
      status={{
        label: teacher.status,
        variant: teacher.status === 'ACTIVE' ? 'success' : 'destructive',
      }}
      fields={[
        { label: "Department", value: teacher.department },
        { label: "Email", value: teacher.email },
        { label: "Phone", value: teacher.phone },
      ]}
      actions={[
        ...(onView ? [{ label: "View", onClick: onView }] : []),
        ...(onEdit ? [{ label: "Edit", onClick: onEdit }] : []),
      ]}
    />
  );
}

export function StudentCard({ student, onView, onEdit }: {
  student: any;
  onView?: () => void;
  onEdit?: () => void;
}) {
  return (
    <EntityCard
      title={student.name}
      subtitle={`${student.grade} - ${student.class}`}
      avatar={{
        src: `https://api.dicebear.com/7.x/initials/svg?seed=${student.name}`,
        fallback: student.name.split(' ').map((n: string) => n[0]).join(''),
      }}
      status={{
        label: student.status,
        variant: student.status === 'ACTIVE' ? 'success' :
                student.status === 'GRADUATED' ? 'info' :
                student.status === 'TRANSFERRED' ? 'warning' : 'destructive',
      }}
      fields={[
        { label: "Email", value: student.email },
        { label: "Phone", value: student.phone },
        { label: "Parent", value: student.parent_contact },
      ]}
      actions={[
        ...(onView ? [{ label: "View", onClick: onView }] : []),
        ...(onEdit ? [{ label: "Edit", onClick: onEdit }] : []),
      ]}
    />
  );
}

export function ClassCard({ classData, onView, onEdit }: {
  classData: any;
  onView?: () => void;
  onEdit?: () => void;
}) {
  const occupancyRate = Math.round((classData.enrolled / classData.capacity) * 100);

  return (
    <EntityCard
      title={classData.name}
      subtitle={`${classData.grade} - ${classData.teacher_name}`}
      icon={
        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <span className="text-blue-600 font-bold text-lg">🏫</span>
        </div>
      }
      status={{
        label: classData.status,
        variant: classData.status === 'ACTIVE' ? 'success' : 'destructive',
      }}
      fields={[
        { label: "Room", value: classData.room },
        { label: "Capacity", value: `${classData.enrolled}/${classData.capacity}` },
        { label: "Occupancy", value: `${occupancyRate}%` },
        { label: "Schedule", value: classData.schedule },
      ]}
      actions={[
        ...(onView ? [{ label: "View", onClick: onView }] : []),
        ...(onEdit ? [{ label: "Edit", onClick: onEdit }] : []),
      ]}
    />
  );
}

export function ExamCard({ exam, onView, onEdit }: {
  exam: any;
  onView?: () => void;
  onEdit?: () => void;
}) {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'info';
      case 'SCHEDULED': return 'default';
      case 'CANCELLED': return 'destructive';
      case 'POSTPONED': return 'warning';
      default: return 'outline';
    }
  };

  return (
    <EntityCard
      title={exam.title}
      subtitle={`${exam.subject} - ${exam.grade}`}
      icon={
        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <span className="text-green-600 font-bold text-lg">📝</span>
        </div>
      }
      status={{
        label: exam.status,
        variant: getStatusVariant(exam.status),
      }}
      fields={[
        { label: "Date", value: new Date(exam.date).toLocaleDateString() },
        { label: "Time", value: `${exam.start_time} - ${exam.end_time}` },
        { label: "Duration", value: `${exam.duration} min` },
        { label: "Room", value: exam.room },
        { label: "Marks", value: `${exam.total_marks} (Pass: ${exam.passing_marks})` },
      ]}
      actions={[
        ...(onView ? [{ label: "View", onClick: onView }] : []),
        ...(onEdit ? [{ label: "Edit", onClick: onEdit }] : []),
      ]}
    />
  );
}

// Default export for lazy loading
export default EntityCard;
