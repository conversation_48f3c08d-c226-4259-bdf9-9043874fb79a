/**
 * Performance Optimization Hooks
 *
 * Collection of hooks for optimizing React component performance:
 * - Memoization strategies
 * - Re-render prevention
 * - State splitting
 * - Expensive computation optimization
 */

import {
  DependencyList,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

/**
 * Enhanced useCallback with dependency comparison
 * Prevents unnecessary re-renders by deep comparing dependencies
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: DependencyList
): T {
  const ref = useRef<T>();
  const depsRef = useRef<DependencyList>();

  // Deep compare dependencies
  const depsChanged =
    !depsRef.current ||
    deps.length !== depsRef.current.length ||
    deps.some((dep, index) => dep !== depsRef.current![index]);

  if (depsChanged) {
    ref.current = callback;
    depsRef.current = deps;
  }

  return useCallback((...args: Parameters<T>) => {
    return ref.current!(...args);
  }, []) as T;
}

/**
 * Memoized value with deep comparison
 * Useful for objects and arrays that might be recreated but have same content
 */
export function useDeepMemo<T>(factory: () => T, deps: DependencyList): T {
  const ref = useRef<{ deps: DependencyList; value: T }>();

  const depsChanged =
    !ref.current ||
    deps.length !== ref.current.deps.length ||
    deps.some((dep, index) => {
      const prevDep = ref.current!.deps[index];
      return JSON.stringify(dep) !== JSON.stringify(prevDep);
    });

  if (depsChanged) {
    ref.current = {
      deps: [...deps],
      value: factory(),
    };
  }

  return ref.current!.value;
}

/**
 * Debounced value hook
 * Prevents excessive re-renders from rapid state changes
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Throttled value hook
 * Limits the frequency of value updates
 */
export function useThrottledValue<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef<number>(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
}

/**
 * Expensive computation hook with memoization
 * Only recalculates when dependencies change
 */
export function useExpensiveComputation<T>(
  computation: () => T,
  deps: DependencyList,
  options?: {
    enabled?: boolean;
    fallback?: T;
  }
): {
  value: T | undefined;
  isComputing: boolean;
  error: Error | null;
} {
  const [state, setState] = useState<{
    value: T | undefined;
    isComputing: boolean;
    error: Error | null;
  }>({
    value: options?.fallback,
    isComputing: false,
    error: null,
  });

  const memoizedComputation = useMemo(() => {
    if (options?.enabled === false) {
      return options?.fallback;
    }

    setState(prev => ({ ...prev, isComputing: true, error: null }));

    try {
      const result = computation();
      setState(prev => ({ ...prev, value: result, isComputing: false }));
      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error as Error,
        isComputing: false,
      }));
      return options?.fallback;
    }
  }, deps);

  return {
    value: memoizedComputation,
    isComputing: state.isComputing,
    error: state.error,
  };
}

/**
 * Previous value hook
 * Useful for comparing current vs previous values to prevent unnecessary updates
 */
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();

  useEffect(() => {
    ref.current = value;
  });

  return ref.current;
}

/**
 * Changed value hook
 * Returns true if value has changed since last render
 */
export function useChanged<T>(value: T): boolean {
  const previous = usePrevious(value);
  return previous !== value;
}

/**
 * Stable reference hook
 * Maintains the same reference unless the value actually changes
 */
export function useStableReference<T>(value: T): T {
  const ref = useRef<T>(value);

  if (JSON.stringify(ref.current) !== JSON.stringify(value)) {
    ref.current = value;
  }

  return ref.current;
}

/**
 * Render count hook (development only)
 * Helps identify components that re-render too frequently
 */
export function useRenderCount(componentName?: string): number {
  const renderCount = useRef(0);

  renderCount.current += 1;

  if (process.env.NODE_ENV === 'development' && componentName) {
    console.log(`${componentName} rendered ${renderCount.current} times`);
  }

  return renderCount.current;
}

/**
 * Why did you update hook (development only)
 * Logs which props caused a component to re-render
 */
export function useWhyDidYouUpdate(name: string, props: Record<string, any>): void {
  const previousProps = useRef<Record<string, any>>();

  useEffect(() => {
    if (previousProps.current && process.env.NODE_ENV === 'development') {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, { from: any; to: any }> = {};

      allKeys.forEach(key => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          };
        }
      });

      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }

    previousProps.current = props;
  });
}

/**
 * Intersection observer hook for lazy loading
 * Optimizes performance by only rendering visible components
 */
export function useIntersectionObserver(
  ref: RefObject<Element>,
  options?: IntersectionObserverInit
): {
  isIntersecting: boolean;
  entry: IntersectionObserverEntry | null;
} {
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => setEntry(entry || null), {
      threshold: 0.1,
      rootMargin: '50px',
      ...options,
    });

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, options]);

  return {
    isIntersecting: entry?.isIntersecting ?? false,
    entry,
  };
}

/**
 * Virtual list hook for large datasets
 * Optimizes rendering of large lists by only rendering visible items
 */
export function useVirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}): {
  virtualItems: Array<{
    index: number;
    item: T;
    offsetTop: number;
  }>;
  totalHeight: number;
  scrollToIndex: (index: number) => void;
} {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLElement>();

  const totalHeight = items.length * itemHeight;

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);

  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const virtualItems = useMemo(() => {
    const result: Array<{
      index: number;
      item: T;
      offsetTop: number;
    }> = [];
    for (let i = startIndex; i <= endIndex; i++) {
      if (i < items.length && items[i] !== undefined) {
        result.push({
          index: i,
          item: items[i] as T,
          offsetTop: i * itemHeight,
        });
      }
    }
    return result;
  }, [items, startIndex, endIndex, itemHeight]);

  const scrollToIndex = useCallback(
    (index: number) => {
      if (scrollElementRef.current) {
        scrollElementRef.current.scrollTop = index * itemHeight;
      }
    },
    [itemHeight]
  );

  return {
    virtualItems,
    totalHeight,
    scrollToIndex,
  };
}

/**
 * Batch state updates hook
 * Batches multiple state updates to prevent excessive re-renders
 */
export function useBatchedState<T>(
  initialState: T
): [T, (updates: Partial<T>) => void, () => void] {
  const [state, setState] = useState<T>(initialState);
  const pendingUpdates = useRef<Partial<T>>({});
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((updates: Partial<T>) => {
    pendingUpdates.current = { ...pendingUpdates.current, ...updates };

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => ({ ...prevState, ...pendingUpdates.current }));
      pendingUpdates.current = {};
    }, 0);
  }, []);

  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setState(prevState => ({ ...prevState, ...pendingUpdates.current }));
    pendingUpdates.current = {};
  }, []);

  return [state, batchUpdate, flushUpdates];
}
