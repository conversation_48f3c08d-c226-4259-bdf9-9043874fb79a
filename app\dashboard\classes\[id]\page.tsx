'use client';

/**
 * Class Detail Page - Comprehensive Class View
 *
 * Features:
 * - Complete class information display
 * - Student enrollment list
 * - Teacher information
 * - Schedule and assignments
 * - Edit and delete actions
 * - Responsive design
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  BookOpen,
  Calendar,
  Clock,
  Edit,
  GraduationCap,
  MapPin,
  Trash2,
  User,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock class data
const mockClassDetail = {
  id: '1',
  name: 'Advanced Mathematics',
  code: 'MATH-101',
  subject: 'Mathematics',
  grade: 'Grade 10',
  teacher: {
    id: '1',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
  },
  capacity: 30,
  enrolled: 28,
  room: 'Room 101',
  schedule: 'Mon, Wed, Fri 9:00-10:00 AM',
  description: 'Advanced mathematics course covering algebra, geometry, and calculus fundamentals.',
  status: 'Active',
  startDate: '2024-01-15',
  endDate: '2024-06-15',
};

const mockEnrolledStudents = [
  { id: '1', name: 'Alice Johnson', email: '<EMAIL>', grade: 'A', attendance: '95%' },
  { id: '2', name: 'Bob Smith', email: '<EMAIL>', grade: 'B+', attendance: '88%' },
  { id: '3', name: 'Carol Davis', email: '<EMAIL>', grade: 'A-', attendance: '92%' },
  { id: '4', name: 'David Wilson', email: '<EMAIL>', grade: 'B', attendance: '85%' },
  { id: '5', name: 'Eva Brown', email: '<EMAIL>', grade: 'A+', attendance: '98%' },
];

const mockAssignments = [
  {
    id: '1',
    title: 'Algebra Quiz 1',
    type: 'Quiz',
    dueDate: '2024-02-15',
    status: 'Completed',
    submissions: 25,
  },
  {
    id: '2',
    title: 'Geometry Project',
    type: 'Project',
    dueDate: '2024-03-01',
    status: 'Active',
    submissions: 18,
  },
  {
    id: '3',
    title: 'Midterm Exam',
    type: 'Exam',
    dueDate: '2024-03-15',
    status: 'Upcoming',
    submissions: 0,
  },
];

export default function ClassDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [classData, setClassData] = useState(mockClassDetail);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadClassData = async () => {
      setIsLoading(true);
      try {
        // TODO: Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setClassData(mockClassDetail);
      } catch (err) {
        setError('Failed to load class data');
      } finally {
        setIsLoading(false);
      }
    };

    loadClassData();
  }, [params.id]);

  const handleEdit = () => {
    router.push(`/dashboard/classes/${params.id}/edit`);
  };

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this class?')) {
      try {
        // TODO: Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        router.push('/dashboard/classes');
      } catch (error) {
        console.error('Error deleting class:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='animate-pulse space-y-6'>
          <div className='h-8 bg-gray-200 rounded w-1/3'></div>
          <div className='h-64 bg-gray-200 rounded'></div>
          <div className='h-32 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='text-center py-12'>
          <p className='text-red-600 mb-4'>{error}</p>
          <Link href='/dashboard/classes'>
            <Button variant='outline'>Back to Classes</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/classes'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Classes
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <BookOpen className='w-8 h-8 text-blue-600' />
              {classData.name}
            </h1>
            <p className='text-gray-600 mt-1'>{classData.code} • {classData.subject}</p>
          </div>
        </div>
        <div className='flex gap-2'>
          <Button onClick={handleEdit} variant='outline'>
            <Edit className='w-4 h-4 mr-2' />
            Edit
          </Button>
          <Button onClick={handleDelete} variant='destructive'>
            <Trash2 className='w-4 h-4 mr-2' />
            Delete
          </Button>
        </div>
      </div>

      {/* Class Overview */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle>Class Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <p className='text-sm font-medium text-gray-500'>Grade Level</p>
                  <p className='text-lg'>{classData.grade}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-500'>Status</p>
                  <Badge variant={classData.status === 'Active' ? 'default' : 'secondary'}>
                    {classData.status}
                  </Badge>
                </div>
              </div>
              <Separator />
              <div className='grid grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <MapPin className='w-4 h-4 text-gray-500' />
                  <span>{classData.room}</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='w-4 h-4 text-gray-500' />
                  <span>{classData.schedule}</span>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <Users className='w-4 h-4 text-gray-500' />
                <span>{classData.enrolled}/{classData.capacity} students enrolled</span>
              </div>
              {classData.description && (
                <>
                  <Separator />
                  <div>
                    <p className='text-sm font-medium text-gray-500 mb-2'>Description</p>
                    <p className='text-gray-700'>{classData.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Teacher Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex items-center gap-3'>
                <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center'>
                  <User className='w-6 h-6 text-blue-600' />
                </div>
                <div>
                  <p className='font-medium'>{classData.teacher.name}</p>
                  <p className='text-sm text-gray-500'>{classData.subject} Teacher</p>
                </div>
              </div>
              <Separator />
              <div className='space-y-2'>
                <p className='text-sm'>
                  <span className='font-medium'>Email:</span> {classData.teacher.email}
                </p>
                <p className='text-sm'>
                  <span className='font-medium'>Phone:</span> {classData.teacher.phone}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue='students' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='students'>Students ({mockEnrolledStudents.length})</TabsTrigger>
          <TabsTrigger value='assignments'>Assignments ({mockAssignments.length})</TabsTrigger>
          <TabsTrigger value='schedule'>Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value='students'>
          <Card>
            <CardHeader>
              <CardTitle>Enrolled Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockEnrolledStudents.map(student => (
                  <div key={student.id} className='flex items-center justify-between p-4 border rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <div className='w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center'>
                        <GraduationCap className='w-5 h-5 text-gray-600' />
                      </div>
                      <div>
                        <p className='font-medium'>{student.name}</p>
                        <p className='text-sm text-gray-500'>{student.email}</p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='font-medium'>Grade: {student.grade}</p>
                      <p className='text-sm text-gray-500'>Attendance: {student.attendance}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='assignments'>
          <Card>
            <CardHeader>
              <CardTitle>Class Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockAssignments.map(assignment => (
                  <div key={assignment.id} className='flex items-center justify-between p-4 border rounded-lg'>
                    <div>
                      <p className='font-medium'>{assignment.title}</p>
                      <p className='text-sm text-gray-500'>{assignment.type} • Due: {assignment.dueDate}</p>
                    </div>
                    <div className='text-right'>
                      <Badge
                        variant={
                          assignment.status === 'Completed'
                            ? 'default'
                            : assignment.status === 'Active'
                            ? 'secondary'
                            : 'outline'
                        }
                      >
                        {assignment.status}
                      </Badge>
                      <p className='text-sm text-gray-500 mt-1'>
                        {assignment.submissions} submissions
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='schedule'>
          <Card>
            <CardHeader>
              <CardTitle>Class Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-5 h-5 text-blue-600' />
                  <span className='font-medium'>Regular Schedule</span>
                </div>
                <p className='text-gray-700'>{classData.schedule}</p>
                <Separator />
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <p className='text-sm font-medium text-gray-500'>Start Date</p>
                    <p>{classData.startDate}</p>
                  </div>
                  <div>
                    <p className='text-sm font-medium text-gray-500'>End Date</p>
                    <p>{classData.endDate}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
