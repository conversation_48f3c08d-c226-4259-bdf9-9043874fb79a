/**
 * Class Service
 * 
 * Handles all class-related API calls
 */

import { apiUtils } from '../apiClient';
import type { 
  Class, 
  ClassFilters, 
  ClassStats,
  PaginatedResponse 
} from '../../types';

export interface CreateClassData {
  name: string;
  grade: string;
  section: string;
  capacity: number;
  teacher_id: string;
  room: string;
  schedule: string;
  academic_year: string;
  subjects?: string[];
}

export interface UpdateClassData extends Partial<CreateClassData> {
  status?: 'ACTIVE' | 'INACTIVE';
}

export class ClassService {
  private static readonly BASE_URL = '/classes';

  static async getClasses(query: ClassFilters = {}): Promise<PaginatedResponse<Class>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<PaginatedResponse<Class>>(url);
  }

  static async getClass(id: string): Promise<Class> {
    return apiUtils.get<Class>(`${this.BASE_URL}/${id}`);
  }

  static async createClass(data: CreateClassData): Promise<Class> {
    return apiUtils.post<Class>(this.BASE_URL, data);
  }

  static async updateClass(id: string, data: UpdateClassData): Promise<Class> {
    return apiUtils.patch<Class>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteClass(id: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/${id}`);
  }

  static async getClassStats(): Promise<ClassStats> {
    return apiUtils.get<ClassStats>(`${this.BASE_URL}/stats`);
  }

  static async getClassStudents(classId: string): Promise<Array<{
    id: string;
    name: string;
    rollNumber: string;
    status: string;
  }>> {
    return apiUtils.get<Array<{
      id: string;
      name: string;
      rollNumber: string;
      status: string;
    }>>(`${this.BASE_URL}/${classId}/students`);
  }
}

export const classService = ClassService;
