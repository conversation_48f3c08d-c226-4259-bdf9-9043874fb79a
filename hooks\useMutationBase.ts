/**
 * Enhanced Base Mutation Hook for School Management System
 *
 * This hook provides a standardized way to handle data mutations with:
 * - Loading states and error handling
 * - Success callbacks and notifications
 * - Optimistic updates
 * - Cache invalidation strategies
 * - API integration with fallback to dummy data
 * - Authentication integration
 * - Development debugging
 */

import {
  useMutation,
  UseMutationOptions,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';
import { useAuth } from './useAuth';

// Success messages
const SUCCESS_MESSAGES = {
  CREATED: 'Record created successfully',
  UPDATED: 'Record updated successfully',
  DELETED: 'Record deleted successfully',
};

// Base mutation configuration
const DEFAULT_MUTATION_CONFIG = {
  retry: (failureCount: number, error: any) => {
    // Don't retry on client errors (4xx)
    if (error?.response?.status >= 400 && error?.response?.status < 500) {
      return false;
    }
    // Retry once for server errors (5xx) or network errors
    return failureCount < 1;
  },
  retryDelay: 1000,
};

// Enhanced generic mutation hook
export function useMutationBase<TData = unknown, TError = AxiosError, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseMutationOptions<TData, TError, TVariables> & {
    successMessage?: string;
    errorMessage?: string;
    invalidateQueries?: string[][];
    showToast?: boolean;
  }
): UseMutationResult<TData, TError, TVariables> {
  const queryClient = useQueryClient();
  const { isAuthenticated } = useAuth();

  const {
    successMessage,
    errorMessage,
    invalidateQueries = [],
    showToast = true,
    ...mutationOptions
  } = options || {};

  return useMutation({
    mutationFn,
    ...DEFAULT_MUTATION_CONFIG,
    ...mutationOptions,
    onSuccess: (data, variables, context) => {
      // Show success toast
      if (showToast && successMessage) {
        toast.success(successMessage);
      }

      // Invalidate specified queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });

      // Call custom onSuccess if provided
      mutationOptions?.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Show error toast
      if (showToast) {
        const message =
          errorMessage ||
          (error as any)?.response?.data?.message ||
          (error as any)?.message ||
          'An error occurred';
        toast.error(message);
      }

      // Log error in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Mutation error:', error);
      }

      // Call custom onError if provided
      mutationOptions?.onError?.(error, variables, context);
    },
  });
}

// Student mutations
export function useCreateStudentMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (studentData: any) => {
      // TODO: Replace with actual API call
      // return apiService.post('/students', studentData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      return {
        id: Math.random().toString(36).substr(2, 9),
        ...studentData,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: () => {
        // Invalidate and refetch students list
        queryClient.invalidateQueries({ queryKey: ['students'] });

        // Show success message
        console.log(SUCCESS_MESSAGES.CREATED);
      },
      onError: error => {
        console.error('Failed to create student:', error);
      },
    }
  );
}

export function useUpdateStudentMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async ({ id, data }: { id: string; data: any }) => {
      // TODO: Replace with actual API call
      // return apiService.put(`/students/${id}`, data);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id,
        ...data,
        updated_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: (data, { id }) => {
        // Update the specific student in cache
        queryClient.setQueryData(['student', id], data);

        // Invalidate students list to refresh
        queryClient.invalidateQueries({ queryKey: ['students'] });

        console.log(SUCCESS_MESSAGES.UPDATED);
      },
      onError: error => {
        console.error('Failed to update student:', error);
      },
    }
  );
}

export function useDeleteStudentMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (id: string) => {
      // TODO: Replace with actual API call
      // return apiService.delete(`/students/${id}`);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    },
    {
      onSuccess: (_, id) => {
        // Remove from cache
        queryClient.removeQueries({ queryKey: ['student', id] });

        // Invalidate students list
        queryClient.invalidateQueries({ queryKey: ['students'] });

        console.log(SUCCESS_MESSAGES.DELETED);
      },
      onError: error => {
        console.error('Failed to delete student:', error);
      },
    }
  );
}

// Teacher mutations
export function useCreateTeacherMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (teacherData: any) => {
      // TODO: Replace with actual API call
      // return apiService.post('/teachers', teacherData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Math.random().toString(36).substr(2, 9),
        ...teacherData,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['teachers'] });
        console.log(SUCCESS_MESSAGES.CREATED);
      },
      onError: error => {
        console.error('Failed to create teacher:', error);
      },
    }
  );
}

export function useUpdateTeacherMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async ({ id, data }: { id: string; data: any }) => {
      // TODO: Replace with actual API call
      // return apiService.put(`/teachers/${id}`, data);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id,
        ...data,
        updated_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: (data, { id }) => {
        queryClient.setQueryData(['teacher', id], data);
        queryClient.invalidateQueries({ queryKey: ['teachers'] });
        console.log(SUCCESS_MESSAGES.UPDATED);
      },
      onError: error => {
        console.error('Failed to update teacher:', error);
      },
    }
  );
}

// Class mutations
export function useCreateClassMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (classData: any) => {
      // TODO: Replace with actual API call
      // return apiService.post('/classes', classData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Math.floor(Math.random() * 1000),
        ...classData,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['classes'] });
        console.log(SUCCESS_MESSAGES.CREATED);
      },
      onError: error => {
        console.error('Failed to create class:', error);
      },
    }
  );
}

// Attendance mutations
export function useMarkAttendanceMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (attendanceData: any) => {
      // TODO: Replace with actual API call
      // return apiService.post('/attendance', attendanceData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 500));
      return {
        id: Math.floor(Math.random() * 1000),
        ...attendanceData,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['attendance'] });
        console.log('Attendance marked successfully!');
      },
      onError: error => {
        console.error('Failed to mark attendance:', error);
      },
    }
  );
}

export function useBulkAttendanceMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (bulkData: any[]) => {
      // TODO: Replace with actual API call
      // return apiService.post('/attendance/bulk', { attendance_records: bulkData });

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        processed: bulkData.length,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: data => {
        queryClient.invalidateQueries({ queryKey: ['attendance'] });
        console.log(`Bulk attendance processed: ${data.processed} records`);
      },
      onError: error => {
        console.error('Failed to process bulk attendance:', error);
      },
    }
  );
}

// Fee mutations
export function useCreateFeeMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async (feeData: any) => {
      // TODO: Replace with actual API call
      // return apiService.post('/fees', feeData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Math.floor(Math.random() * 1000),
        ...feeData,
        created_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['fees'] });
        console.log(SUCCESS_MESSAGES.CREATED);
      },
      onError: error => {
        console.error('Failed to create fee:', error);
      },
    }
  );
}

export function usePayFeeMutation() {
  const queryClient = useQueryClient();

  return useMutationBase(
    async ({ feeId, paymentData }: { feeId: string; paymentData: any }) => {
      // TODO: Replace with actual API call
      // return apiService.post(`/fees/${feeId}/pay`, paymentData);

      // Mock response for now
      await new Promise(resolve => setTimeout(resolve, 1500));
      return {
        fee_id: feeId,
        payment_id: Math.random().toString(36).substr(2, 9),
        ...paymentData,
        status: 'PAID',
        paid_at: new Date().toISOString(),
      };
    },
    {
      onSuccess: (_, { feeId }) => {
        queryClient.invalidateQueries({ queryKey: ['fees'] });
        queryClient.invalidateQueries({ queryKey: ['fee', feeId] });
        console.log('Payment processed successfully!');
      },
      onError: error => {
        console.error('Failed to process payment:', error);
      },
    }
  );
}

// TODO: Add more mutation hooks as needed
// Example: useCreateExamMutation, useCreateAnnouncementMutation, etc.
