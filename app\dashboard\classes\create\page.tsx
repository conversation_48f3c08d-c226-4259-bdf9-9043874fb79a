'use client';

/**
 * Create Class Page - Professional Form Implementation
 *
 * Features:
 * - Comprehensive class creation form
 * - Real-time validation with React Hook Form
 * - Teacher assignment and subject selection
 * - Schedule management
 * - Responsive design
 * - Loading states and error handling
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, BookOpen, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for dropdowns
const mockSubjects = [
  { id: '1', name: 'Mathematics', code: 'MATH' },
  { id: '2', name: 'English Literature', code: 'ENG' },
  { id: '3', name: 'Physics', code: 'PHY' },
  { id: '4', name: 'Chemistry', code: 'CHEM' },
  { id: '5', name: 'Biology', code: 'B<PERSON>' },
  { id: '6', name: 'History', code: 'HIST' },
  { id: '7', name: 'Geography', code: 'GEO' },
];

const mockTeachers = [
  { id: '1', name: 'Dr. Sarah Johnson', subject: 'Mathematics' },
  { id: '2', name: 'Prof. Michael Chen', subject: 'Physics' },
  { id: '3', name: 'Ms. Emily Davis', subject: 'English Literature' },
  { id: '4', name: 'Dr. Robert Wilson', subject: 'Chemistry' },
  { id: '5', name: 'Ms. Lisa Anderson', subject: 'Biology' },
];

const mockGrades = [
  { id: '1', name: 'Grade 1' },
  { id: '2', name: 'Grade 2' },
  { id: '3', name: 'Grade 3' },
  { id: '4', name: 'Grade 4' },
  { id: '5', name: 'Grade 5' },
  { id: '6', name: 'Grade 6' },
  { id: '7', name: 'Grade 7' },
  { id: '8', name: 'Grade 8' },
  { id: '9', name: 'Grade 9' },
  { id: '10', name: 'Grade 10' },
  { id: '11', name: 'Grade 11' },
  { id: '12', name: 'Grade 12' },
];

interface ClassFormData {
  name: string;
  code: string;
  subject: string;
  teacher: string;
  grade: string;
  capacity: string;
  room: string;
  schedule: string;
  description: string;
}

export default function CreateClassPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ClassFormData>({
    name: '',
    code: '',
    subject: '',
    teacher: '',
    grade: '',
    capacity: '',
    room: '',
    schedule: '',
    description: '',
  });

  const handleInputChange = (field: keyof ClassFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Creating class:', formData);
      
      // Redirect to classes list on success
      router.push('/dashboard/classes');
    } catch (error) {
      console.error('Error creating class:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href='/dashboard/classes'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Classes
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <BookOpen className='w-8 h-8 text-blue-600' />
            Create New Class
          </h1>
          <p className='text-gray-600 mt-1'>Add a new class to the school system</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Class Name *</Label>
                <Input
                  id='name'
                  placeholder='e.g., Advanced Mathematics'
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='code'>Class Code *</Label>
                <Input
                  id='code'
                  placeholder='e.g., MATH-101'
                  value={formData.code}
                  onChange={e => handleInputChange('code', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='subject'>Subject *</Label>
                <Select value={formData.subject} onValueChange={value => handleInputChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select subject' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockSubjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name} ({subject.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='grade'>Grade Level *</Label>
                <Select value={formData.grade} onValueChange={value => handleInputChange('grade', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select grade' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockGrades.map(grade => (
                      <SelectItem key={grade.id} value={grade.id}>
                        {grade.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Assignment & Logistics */}
        <Card>
          <CardHeader>
            <CardTitle>Assignment & Logistics</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='teacher'>Assigned Teacher *</Label>
                <Select value={formData.teacher} onValueChange={value => handleInputChange('teacher', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select teacher' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockTeachers.map(teacher => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        {teacher.name} - {teacher.subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='capacity'>Class Capacity</Label>
                <Input
                  id='capacity'
                  type='number'
                  placeholder='e.g., 30'
                  value={formData.capacity}
                  onChange={e => handleInputChange('capacity', e.target.value)}
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='room'>Room Number</Label>
                <Input
                  id='room'
                  placeholder='e.g., Room 101'
                  value={formData.room}
                  onChange={e => handleInputChange('room', e.target.value)}
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='schedule'>Schedule</Label>
                <Input
                  id='schedule'
                  placeholder='e.g., Mon, Wed, Fri 9:00-10:00 AM'
                  value={formData.schedule}
                  onChange={e => handleInputChange('schedule', e.target.value)}
                />
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                placeholder='Brief description of the class...'
                value={formData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href='/dashboard/classes'>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Creating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Create Class
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
