/**
 * Register Page - User Registration
 *
 * Features:
 * - User registration form with validation
 * - Role-based registration
 * - Email verification flow
 * - Professional UI with proper error handling
 * - Responsive design
 */

'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, CheckCircle, GraduationCap, Lock, Mail, Phone, User } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  role: string;
  agreeToTerms: boolean;
}

export default function RegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: '',
    agreeToTerms: false,
  });

  const handleInputChange = (field: keyof RegisterFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null);
  };

  const validateForm = (): string | null => {
    if (!formData.firstName.trim()) return 'First name is required';
    if (!formData.lastName.trim()) return 'Last name is required';
    if (!formData.email.trim()) return 'Email is required';
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) return 'Invalid email format';
    if (!formData.phone.trim()) return 'Phone number is required';
    if (!formData.password) return 'Password is required';
    if (formData.password.length < 8) return 'Password must be at least 8 characters';
    if (formData.password !== formData.confirmPassword) return 'Passwords do not match';
    if (!formData.role) return 'Please select a role';
    if (!formData.agreeToTerms) return 'You must agree to the terms and conditions';
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock registration success
      toast.success('Registration successful! Please check your email for verification.');
      router.push('/login?message=registration-success');
    } catch (err) {
      setError('Registration failed. Please try again.');
      toast.error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4'>
      <Card className='w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm'>
        <CardHeader className='text-center space-y-4'>
          <div className='mx-auto w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg'>
            <GraduationCap className='w-8 h-8 text-white' />
          </div>
          <div>
            <CardTitle className='text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'>
              Create Account
            </CardTitle>
            <CardDescription className='text-gray-600'>
              Join our school management platform
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className='space-y-6'>
          {error && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className='space-y-4'>
            {/* Name Fields */}
            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='firstName'>First Name</Label>
                <div className='relative'>
                  <User className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='firstName'
                    type='text'
                    placeholder='John'
                    value={formData.firstName}
                    onChange={e => handleInputChange('firstName', e.target.value)}
                    className='pl-10'
                    disabled={isLoading}
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='lastName'>Last Name</Label>
                <div className='relative'>
                  <User className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='lastName'
                    type='text'
                    placeholder='Doe'
                    value={formData.lastName}
                    onChange={e => handleInputChange('lastName', e.target.value)}
                    className='pl-10'
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Email */}
            <div className='space-y-2'>
              <Label htmlFor='email'>Email</Label>
              <div className='relative'>
                <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                <Input
                  id='email'
                  type='email'
                  placeholder='<EMAIL>'
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  className='pl-10'
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Phone */}
            <div className='space-y-2'>
              <Label htmlFor='phone'>Phone Number</Label>
              <div className='relative'>
                <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                <Input
                  id='phone'
                  type='tel'
                  placeholder='+****************'
                  value={formData.phone}
                  onChange={e => handleInputChange('phone', e.target.value)}
                  className='pl-10'
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Role Selection */}
            <div className='space-y-2'>
              <Label htmlFor='role'>Role</Label>
              <Select
                value={formData.role}
                onValueChange={value => handleInputChange('role', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select your role' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='TEACHER'>Teacher</SelectItem>
                  <SelectItem value='STUDENT'>Student</SelectItem>
                  <SelectItem value='PARENT'>Parent</SelectItem>
                  <SelectItem value='STAFF'>Staff</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Password Fields */}
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='password'>Password</Label>
                <div className='relative'>
                  <Lock className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='password'
                    type='password'
                    placeholder='Enter your password'
                    value={formData.password}
                    onChange={e => handleInputChange('password', e.target.value)}
                    className='pl-10'
                    disabled={isLoading}
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='confirmPassword'>Confirm Password</Label>
                <div className='relative'>
                  <Lock className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='confirmPassword'
                    type='password'
                    placeholder='Confirm your password'
                    value={formData.confirmPassword}
                    onChange={e => handleInputChange('confirmPassword', e.target.value)}
                    className='pl-10'
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='terms'
                checked={formData.agreeToTerms}
                onCheckedChange={checked => handleInputChange('agreeToTerms', checked as boolean)}
                disabled={isLoading}
              />
              <Label htmlFor='terms' className='text-sm text-gray-600'>
                I agree to the{' '}
                <Link href='/terms' className='text-blue-600 hover:underline'>
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href='/privacy' className='text-blue-600 hover:underline'>
                  Privacy Policy
                </Link>
              </Label>
            </div>

            {/* Submit Button */}
            <Button
              type='submit'
              className='w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5'
              disabled={isLoading}
            >
              {isLoading ? (
                <div className='flex items-center space-x-2'>
                  <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
                  <span>Creating Account...</span>
                </div>
              ) : (
                <div className='flex items-center space-x-2'>
                  <CheckCircle className='w-4 h-4' />
                  <span>Create Account</span>
                </div>
              )}
            </Button>
          </form>

          <div className='space-y-4'>
            <Separator />
            <div className='text-center text-sm text-gray-600'>
              Already have an account?{' '}
              <Link
                href='/login'
                className='text-blue-600 hover:text-blue-700 font-medium hover:underline'
              >
                Sign in here
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
