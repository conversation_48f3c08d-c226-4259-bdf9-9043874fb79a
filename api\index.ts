/**
 * API Services - Barrel Export
 * 
 * Centralized export for all API services
 */

// Base API client
export { apiClient, ApiError } from './apiClient';

// Service exports
export * from './services/authService';
export * from './services/teacherService';
export * from './services/studentService';
export * from './services/classService';
export * from './services/examService';
export * from './services/attendanceService';
export * from './services/feeService';
export * from './services/gradeService';

// Types
export type {
  ApiResponse,
  PaginatedResponse,
  ApiError as ApiErrorType,
} from '../types';
