/**
 * Enhanced Base Query Hook for School Management System
 *
 * A comprehensive query hook that provides:
 * - Standardized TanStack Query usage
 * - Proper TypeScript typing
 * - Axios error handling
 * - Authentication integration
 * - Flexible configuration
 * - Development debugging
 * - Cache management
 */

import type { ApiResponse } from '@/types/global';
import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useAuth } from './useAuth';

// Default query configuration
const DEFAULT_QUERY_CONFIG = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  retry: (failureCount: number, error: any) => {
    // Don't retry on authentication errors
    if (error?.response?.status === 401 || error?.response?.status === 403) {
      return false;
    }
    // Retry up to 3 times for other errors
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// Enhanced query hook with authentication and error handling
export const useQueryBase = <TData, TError = AxiosError>(
  key: (string | number | boolean | undefined | null)[],
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'>
): UseQueryResult<TData, TError> => {
  const { isAuthenticated } = useAuth();

  return useQuery<TData, TError>({
    queryKey: key.filter(Boolean), // Remove falsy values from key
    queryFn,
    ...DEFAULT_QUERY_CONFIG,
    ...options,
    // Disable query if not authenticated (for protected routes)
    enabled: options?.enabled !== false && (options?.enabled ?? isAuthenticated),
  });
};

// Enhanced query hook for API responses wrapped in ApiResponse<T>
export const useQueryBaseWithApiResponse = <TData, TError = AxiosError>(
  key: (string | number | boolean | undefined | null)[],
  queryFn: () => Promise<ApiResponse<TData>>,
  options?: Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'>
): UseQueryResult<TData, TError> => {
  const { isAuthenticated } = useAuth();

  return useQuery<TData, TError>({
    queryKey: key.filter(Boolean), // Remove falsy values from key
    queryFn: async () => {
      const response = await queryFn();
      return response.data;
    },
    ...DEFAULT_QUERY_CONFIG,
    ...options,
    // Disable query if not authenticated (for protected routes)
    enabled: options?.enabled !== false && (options?.enabled ?? isAuthenticated),
  });
};

// Specialized hooks for common patterns
export const useInfiniteQueryBase = <TData, TError = AxiosError>(
  key: (string | number | boolean | undefined | null)[],
  queryFn: ({ pageParam }: { pageParam?: any }) => Promise<TData>,
  options?: any
) => {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: key.filter(Boolean),
    queryFn,
    ...DEFAULT_QUERY_CONFIG,
    ...options,
    enabled: options?.enabled !== false && (options?.enabled ?? isAuthenticated),
  });
};

// Hook for queries that don't require authentication
export const usePublicQuery = <TData, TError = AxiosError>(
  key: (string | number | boolean | undefined | null)[],
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'>
): UseQueryResult<TData, TError> => {
  return useQuery<TData, TError>({
    queryKey: key.filter(Boolean),
    queryFn,
    ...DEFAULT_QUERY_CONFIG,
    ...options,
  });
};
