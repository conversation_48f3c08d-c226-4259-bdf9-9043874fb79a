'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON>pen, Loader2, Save, Users, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Schema imports - demonstrating advanced Zod patterns
import {
  ClassCreateSchema,
  ClassUpdateSchema,
  type ClassCreate,
  type ClassUpdate,
} from '@/schemas/zodSchemas';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSection,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Advanced validation schema with custom rules
const ClassFormSchema = z.object({
  class_name: z
    .string()
    .min(1, 'Class name is required')
    .max(50, 'Class name must be less than 50 characters')
    .regex(
      /^[A-Za-z0-9\s-]+$/,
      'Class name can only contain letters, numbers, spaces, and hyphens'
    ),

  grade_level: z.string().min(1, 'Grade level is required'),

  teacher_id: z.string().min(1, 'Teacher assignment is required'),

  room_number: z
    .string()
    .optional()
    .refine(val => !val || /^[A-Z]?\d{1,4}[A-Z]?$/.test(val), {
      message: 'Room number format: A123, 101, or 205B',
    }),

  max_capacity: z
    .number()
    .min(1, 'Capacity must be at least 1')
    .max(50, 'Capacity cannot exceed 50 students')
    .int('Capacity must be a whole number'),

  schedule: z.string().optional(),

  description: z
    .string()
    .optional()
    .refine(val => !val || val.length <= 500, {
      message: 'Description must be less than 500 characters',
    }),
});

// Types for the component props - strict typing
interface ClassFormProps {
  mode: 'create' | 'edit';
  initialData?: ClassUpdate;
  onSubmit: (data: ClassCreate | ClassUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  availableTeachers?: Array<{ id: string; name: string; subject: string }>;
}

// Grade levels with advanced typing
const GRADE_LEVELS = [
  { value: 'K', label: 'Kindergarten' },
  { value: '1', label: '1st Grade' },
  { value: '2', label: '2nd Grade' },
  { value: '3', label: '3rd Grade' },
  { value: '4', label: '4th Grade' },
  { value: '5', label: '5th Grade' },
  { value: '6', label: '6th Grade' },
  { value: '7', label: '7th Grade' },
  { value: '8', label: '8th Grade' },
  { value: '9', label: '9th Grade' },
  { value: '10', label: '10th Grade' },
  { value: '11', label: '11th Grade' },
  { value: '12', label: '12th Grade' },
] as const;

// Schedule options
const SCHEDULE_OPTIONS = [
  'Monday, Wednesday, Friday - 9:00 AM',
  'Tuesday, Thursday - 10:30 AM',
  'Monday through Friday - 2:00 PM',
  'Monday, Wednesday - 1:00 PM',
  'Tuesday, Thursday, Friday - 11:00 AM',
] as const;

/**
 * ClassForm Component
 *
 * Demonstrates advanced Zod validation patterns:
 * - Custom validation rules with regex
 * - Conditional validation with refine()
 * - Number validation with constraints
 * - Cross-field validation
 * - Complex error handling
 */
export function ClassForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  availableTeachers = [],
}: ClassFormProps) {
  // Use the advanced schema for better validation
  const schema = ClassFormSchema;

  // Form initialization with strict typing and advanced defaults
  const form = useForm<z.infer<typeof ClassFormSchema>>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'create'
        ? {
            class_name: '',
            grade_level: '',
            teacher_id: '',
            room_number: '',
            max_capacity: 25, // Reasonable default
            schedule: '',
            description: '',
          }
        : {
            class_name: initialData?.name || '',
            grade_level: initialData?.grade_level || '',
            teacher_id: '', // Will need to be set based on teacher_name
            room_number: initialData?.room_number || '',
            max_capacity: initialData?.capacity || 25,
            schedule: '',
            description: '',
          },
    mode: 'onChange', // Real-time validation
  });

  // Watch form values for advanced validation
  const watchedCapacity = form.watch('max_capacity');
  const watchedGradeLevel = form.watch('grade_level');

  // Type-safe form submission with advanced validation
  const handleSubmit = async (data: z.infer<typeof ClassFormSchema>) => {
    try {
      // Additional business logic validation
      if (data.max_capacity && data.max_capacity > 40 && !data.room_number) {
        form.setError('room_number', {
          message: 'Room number is required for classes with more than 40 students',
        });
        return;
      }

      // Validate data against schema before submission
      const validatedData = schema.parse(data);

      // Transform to match API schema if needed
      const submitData =
        mode === 'create'
          ? ClassCreateSchema.parse(validatedData)
          : ClassUpdateSchema.parse(validatedData);

      await onSubmit(submitData);
    } catch (error) {
      console.error('Form validation error:', error);
    }
  };

  return (
    <Card className='w-full max-w-3xl mx-auto'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <BookOpen className='w-5 h-5' />
          {mode === 'create' ? 'Create New Class' : 'Edit Class'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Set up a new class with teacher assignment and capacity limits.'
            : 'Update the class information and settings.'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            {/* Basic Class Information */}
            <FormSection title='Class Information' description='Basic details about the class'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Class Name - Advanced validation */}
                <FormField
                  control={form.control}
                  name='class_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Class Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., Advanced Mathematics A' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Grade Level - Required selection */}
                <FormField
                  control={form.control}
                  name='grade_level'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Grade Level *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select grade level' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {GRADE_LEVELS.map(grade => (
                            <SelectItem key={grade.value} value={grade.value}>
                              {grade.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Teacher Assignment - Required */}
                <FormField
                  control={form.control}
                  name='teacher_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assigned Teacher *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select teacher' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableTeachers.map(teacher => (
                            <SelectItem key={teacher.id} value={teacher.id}>
                              {teacher.name} - {teacher.subject}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Room Number - Advanced regex validation */}
                <FormField
                  control={form.control}
                  name='room_number'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Room Number
                        {watchedCapacity > 40 && <span className='text-red-500'>*</span>}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., A101, 205B, 301' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Capacity and Schedule */}
            <FormSection
              title='Capacity & Schedule'
              description='Class size limits and meeting times'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Max Capacity - Number validation with constraints */}
                <FormField
                  control={form.control}
                  name='max_capacity'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex items-center gap-2'>
                        <Users className='w-4 h-4' />
                        Maximum Capacity *
                      </FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          min='1'
                          max='50'
                          placeholder='25'
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                      {watchedCapacity > 35 && (
                        <p className='text-sm text-amber-600'>
                          ⚠️ Large class size - ensure adequate room space
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                {/* Schedule - Optional selection */}
                <FormField
                  control={form.control}
                  name='schedule'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Class Schedule</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        {...(field.value && { defaultValue: field.value })}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select schedule' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {SCHEDULE_OPTIONS.map(schedule => (
                            <SelectItem key={schedule} value={schedule}>
                              {schedule}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Description */}
            <FormSection
              title='Additional Information'
              description='Optional class description and notes'
            >
              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <textarea
                        className='flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                        placeholder='Optional class description, special requirements, or notes...'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <p className='text-xs text-gray-500'>
                      {field.value?.length || 0}/500 characters
                    </p>
                  </FormItem>
                )}
              />
            </FormSection>

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 pt-6 border-t'>
              <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                <X className='w-4 h-4 mr-2' />
                Cancel
              </Button>

              <Button type='submit' disabled={isLoading || !form.formState.isValid}>
                {isLoading ? (
                  <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                ) : (
                  <Save className='w-4 h-4 mr-2' />
                )}
                {mode === 'create' ? 'Create Class' : 'Update Class'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Export type for external usage
export type { ClassFormProps };

// Default export for lazy loading
export default ClassForm;
