"use client";

import { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  createColumnHelper,
  flexRender,
  type ColumnDef,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { useUsers, useToggleUserActivation } from '@/hooks/useUsersQuery';
import type { User, UserListParams } from '@/types/auth';

const columnHelper = createColumnHelper<User>();

export default function UsersPage() {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // Query parameters
  const queryParams: UserListParams = useMemo(() => ({
    search: globalFilter || undefined,
    page: pagination.pageIndex + 1,
    size: pagination.pageSize,
  }), [globalFilter, pagination]);

  const { data: usersData, isLoading, error } = useUsers({ params: queryParams });
  const toggleUserMutation = useToggleUserActivation();

  const handleToggleUser = async (userId: string, isActive: boolean) => {
    try {
      await toggleUserMutation.mutateAsync({ userId, isActive });
    } catch (error) {
      console.error('Failed to toggle user:', error);
    }
  };

  const columns = useMemo<ColumnDef<User>[]>(() => [
    columnHelper.accessor('username', {
      header: 'Username',
      cell: (info) => (
        <div className="font-medium text-gray-900">
          {info.getValue()}
        </div>
      ),
    }),
    columnHelper.accessor('email', {
      header: 'Email',
      cell: (info) => (
        <div className="text-gray-600">
          {info.getValue()}
        </div>
      ),
    }),
    columnHelper.accessor('first_name', {
      header: 'Name',
      cell: (info) => {
        const user = info.row.original;
        const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ');
        return (
          <div className="text-gray-900">
            {fullName || 'Not set'}
          </div>
        );
      },
    }),
    columnHelper.accessor('is_active', {
      header: 'Status',
      cell: (info) => {
        const isActive = info.getValue();
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            isActive 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {isActive ? 'Active' : 'Inactive'}
          </span>
        );
      },
    }),
    columnHelper.accessor('roles', {
      header: 'Roles',
      cell: (info) => {
        const roles = info.getValue();
        return (
          <div className="text-gray-600">
            {roles?.join(', ') || 'No roles'}
          </div>
        );
      },
    }),
    columnHelper.accessor('created_at', {
      header: 'Created',
      cell: (info) => {
        const date = info.getValue();
        return (
          <div className="text-gray-600">
            {date ? new Date(date).toLocaleDateString() : 'Unknown'}
          </div>
        );
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: 'Actions',
      cell: (info) => {
        const user = info.row.original;
        return (
          <div className="flex gap-2">
            <button
              onClick={() => handleToggleUser(user.id, user.is_active)}
              disabled={toggleUserMutation.isPending}
              className={`px-3 py-1 text-xs font-medium rounded ${
                user.is_active
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              } disabled:opacity-50`}
            >
              {user.is_active ? 'Deactivate' : 'Activate'}
            </button>
            <button
              className="px-3 py-1 text-xs font-medium rounded bg-blue-100 text-blue-700 hover:bg-blue-200"
            >
              Edit
            </button>
          </div>
        );
      },
    }),
  ], [toggleUserMutation.isPending]);

  const table = useReactTable({
    data: usersData?.items || [],
    columns,
    pageCount: usersData?.pages || 0,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
  });

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">Failed to load users data</p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
        <p className="text-gray-600">Manage user accounts and permissions</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search users..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Add User
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          <div className="flex items-center gap-2">
                            {flexRender(header.column.columnDef.header, header.getContext())}
                            {header.column.getIsSorted() && (
                              <span className="text-blue-600">
                                {header.column.getIsSorted() === 'desc' ? '↓' : '↑'}
                              </span>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="px-6 py-4 whitespace-nowrap">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">
                    Showing {pagination.pageIndex * pagination.pageSize + 1} to{' '}
                    {Math.min((pagination.pageIndex + 1) * pagination.pageSize, usersData?.total || 0)} of{' '}
                    {usersData?.total || 0} results
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                    className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-700">
                    Page {pagination.pageIndex + 1} of {usersData?.pages || 1}
                  </span>
                  <button
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                    className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Stats */}
      {usersData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-2xl font-bold text-gray-900">{usersData.total}</div>
            <div className="text-sm text-gray-600">Total Users</div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">
              {usersData.items.filter(u => u.is_active).length}
            </div>
            <div className="text-sm text-gray-600">Active Users</div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-2xl font-bold text-red-600">
              {usersData.items.filter(u => !u.is_active).length}
            </div>
            <div className="text-sm text-gray-600">Inactive Users</div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{usersData.pages}</div>
            <div className="text-sm text-gray-600">Total Pages</div>
          </div>
        </div>
      )}
    </div>
  );
}
