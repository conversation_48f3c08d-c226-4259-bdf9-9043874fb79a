'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LucideIcon, Plus, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ReactNode, useState } from 'react';

interface StatsCard {
  title: string;
  value: string;
  icon: LucideIcon;
  color: string;
  bgColor: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
}

interface FilterOption {
  label: string;
  value: string;
}

interface ModulePageLayoutProps {
  // Header props
  title: string;
  description: string;
  icon: LucideIcon;
  badge?: { label: string; variant?: 'default' | 'outline' | 'secondary' };
  
  // Stats props
  statsCards: StatsCard[];
  
  // Filter props
  searchPlaceholder?: string;
  filters?: {
    label: string;
    options: FilterOption[];
    value: string;
    onChange: (value: string) => void;
  }[];
  
  // Content props
  children: ReactNode;
  
  // Actions
  createRoute: string;
  createLabel: string;
  
  // Search
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  
  // Results count
  totalItems?: number;
  filteredItems?: number;
}

export function ModulePageLayout({
  title,
  description,
  icon: Icon,
  badge,
  statsCards,
  searchPlaceholder = 'Search...',
  filters = [],
  children,
  createRoute,
  createLabel,
  searchValue = '',
  onSearchChange,
  totalItems,
  filteredItems,
}: ModulePageLayoutProps) {
  const router = useRouter();
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue);

  const handleCreateClick = () => {
    router.push(createRoute);
  };

  const handleSearchChange = (value: string) => {
    setInternalSearchValue(value);
    onSearchChange?.(value);
  };

  const currentSearchValue = onSearchChange ? searchValue : internalSearchValue;

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title={title}
        description={description}
        icon={Icon}
        badge={badge}
        actions={[
          {
            label: createLabel,
            icon: Plus,
            onClick: handleCreateClick,
          },
        ]}
      />

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                  {stat.change && (
                    <p className={`text-xs ${
                      stat.changeType === 'positive' ? 'text-green-600' : 
                      stat.changeType === 'negative' ? 'text-red-600' : 
                      'text-gray-600'
                    }`}>
                      {stat.change}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder={searchPlaceholder}
                value={currentSearchValue}
                onChange={e => handleSearchChange(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Dynamic Filters */}
            {filters.map((filter, index) => (
              <Select key={index} value={filter.value} onValueChange={filter.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder={filter.label} />
                </SelectTrigger>
                <SelectContent>
                  {filter.options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ))}

            {/* Results Count */}
            {(totalItems !== undefined && filteredItems !== undefined) && (
              <div className='flex items-center text-sm text-muted-foreground'>
                Showing {filteredItems} of {totalItems} items
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {children}
    </div>
  );
}
