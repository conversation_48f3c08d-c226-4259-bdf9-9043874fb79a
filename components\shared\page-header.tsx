/**
 * Page Header Component
 *
 * Standardized page header with title, description, and actions
 */

import { LucideIcon } from 'lucide-react';
import { ReactNode } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface PageHeaderAction {
  label: string;
  icon?: LucideIcon;
  onClick?: () => void;
  href?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  size?: 'sm' | 'default' | 'lg';
}

export interface PageHeaderProps {
  title: string;
  description?: string;
  badge?: {
    label: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
    icon?: LucideIcon;
  };
  actions?: PageHeaderAction[];
  children?: ReactNode;
  className?: string;
}

export function PageHeader({
  title,
  description,
  badge,
  actions,
  children,
  className,
}: PageHeaderProps) {
  return (
    <div className={cn('flex flex-col space-y-6', className)}>
      {/* Header Content */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl sm:text-4xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              {title}
            </h1>
            {badge && (
              <Badge variant={badge.variant} className="flex items-center space-x-1">
                {badge.icon && <badge.icon className="h-3 w-3" />}
                <span>{badge.label}</span>
              </Badge>
            )}
          </div>
          {description && (
            <p className="text-slate-600 mt-2 text-lg leading-relaxed max-w-3xl">
              {description}
            </p>
          )}
        </div>

        {/* Actions */}
        {actions && actions.length > 0 && (
          <div className="flex items-center space-x-3">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'default'}
                size={action.size || 'default'}
                onClick={action.onClick}
                className="flex items-center space-x-2"
              >
                {action.icon && <action.icon className="h-4 w-4" />}
                <span>{action.label}</span>
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Additional Content */}
      {children && (
        <div className="border-t border-slate-200 pt-6">
          {children}
        </div>
      )}
    </div>
  );
}

// Default export for lazy loading
export default PageHeader;
