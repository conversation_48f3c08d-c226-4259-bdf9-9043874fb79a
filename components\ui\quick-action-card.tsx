/**
 * Quick Action Card Component
 * 
 * Reusable action card with gradient backgrounds and hover effects
 */

import { ReactNode } from 'react';
import Link from 'next/link';
import { LucideIcon, ArrowUpRight } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface QuickActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'yellow' | 'pink' | 'indigo';
  className?: string;
  onClick?: () => void;
}

const colorVariants = {
  blue: 'bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
  green: 'bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
  purple: 'bg-gradient-to-br from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
  orange: 'bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',
  red: 'bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
  yellow: 'bg-gradient-to-br from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
  pink: 'bg-gradient-to-br from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700',
  indigo: 'bg-gradient-to-br from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700',
};

export function QuickActionCard({
  title,
  description,
  icon: Icon,
  href,
  color,
  className,
  onClick,
}: QuickActionCardProps) {
  const content = (
    <Card className={cn(
      'border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group hover:scale-105',
      className
    )}>
      <CardContent className={cn(
        'p-6 text-white rounded-lg transition-all duration-300',
        colorVariants[color]
      )}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-lg mb-1">{title}</h3>
            <p className="text-sm opacity-90 leading-relaxed">{description}</p>
          </div>
          <div className="p-3 bg-white/20 rounded-xl group-hover:bg-white/30 transition-colors shadow-lg">
            <Icon className="h-5 w-5" />
          </div>
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="flex-1" />
          <ArrowUpRight className="h-4 w-4 opacity-70 group-hover:opacity-100 group-hover:translate-x-1 group-hover:-translate-y-1 transition-all duration-200" />
        </div>
      </CardContent>
    </Card>
  );

  if (onClick) {
    return (
      <div onClick={onClick}>
        {content}
      </div>
    );
  }

  return (
    <Link href={href}>
      {content}
    </Link>
  );
}
