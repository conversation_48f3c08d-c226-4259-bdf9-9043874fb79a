'use client';

import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Plus, GripVertical, Edit2, Trash2, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialog<PERSON>ooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FeeTypeSchema, type FeeType, type CreateFeeType } from '../../schemas/fees.schemas';

// Mock data for development
const mockFeeTypes: FeeType[] = [
  {
    id: '1',
    name: 'Tuition Fee',
    description: 'Monthly tuition charges',
    category: 'ACADEMIC',
    isActive: true,
    sortOrder: 0,
    isRequired: true,
  },
  {
    id: '2',
    name: 'Transport Fee',
    description: 'School bus transportation',
    category: 'TRANSPORT',
    isActive: true,
    sortOrder: 1,
    isRequired: false,
  },
  {
    id: '3',
    name: 'Laboratory Fee',
    description: 'Science lab usage charges',
    category: 'LABORATORY',
    isActive: true,
    sortOrder: 2,
    isRequired: false,
  },
];

const categoryColors = {
  ACADEMIC: 'bg-blue-50 text-blue-700 border-blue-200',
  TRANSPORT: 'bg-green-50 text-green-700 border-green-200',
  HOSTEL: 'bg-purple-50 text-purple-700 border-purple-200',
  LIBRARY: 'bg-orange-50 text-orange-700 border-orange-200',
  LABORATORY: 'bg-red-50 text-red-700 border-red-200',
  SPORTS: 'bg-yellow-50 text-yellow-700 border-yellow-200',
  OTHER: 'bg-gray-50 text-gray-700 border-gray-200',
};

interface FeeTypesManagementProps {
  onDataChange?: () => void;
}

/**
 * FeeTypesManagement Component
 * 
 * Manages base fee heads with:
 * - Drag and drop reordering
 * - Add/Edit/Delete operations
 * - Category-based organization
 * - Usage validation before delete
 */
export function FeeTypesManagement({ onDataChange }: FeeTypesManagementProps) {
  const [feeTypes, setFeeTypes] = useState<FeeType[]>(mockFeeTypes);
  const [editingFeeType, setEditingFeeType] = useState<FeeType | null>(null);
  const [deletingFeeType, setDeletingFeeType] = useState<FeeType | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const form = useForm<CreateFeeType>({
    resolver: zodResolver(FeeTypeSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    defaultValues: {
      name: '',
      description: '',
      category: 'ACADEMIC',
      isActive: true,
      sortOrder: 0,
      isRequired: false,
    },
  });

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(feeTypes);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update sort orders
    const updatedItems = items.map((item, index) => ({
      ...item,
      sortOrder: index,
    }));

    setFeeTypes(updatedItems);
    onDataChange?.();
  };

  // Handle add fee type
  const handleAdd = (data: CreateFeeType) => {
    const newFeeType: FeeType = {
      ...data,
      id: Date.now().toString(),
      sortOrder: feeTypes.length,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setFeeTypes([...feeTypes, newFeeType]);
    setIsAddDialogOpen(false);
    form.reset();
    onDataChange?.();
  };

  // Handle edit fee type
  const handleEdit = (data: CreateFeeType) => {
    if (!editingFeeType) return;

    const updatedFeeTypes = feeTypes.map(ft =>
      ft.id === editingFeeType.id
        ? { ...ft, ...data, updatedAt: new Date().toISOString() }
        : ft
    );

    setFeeTypes(updatedFeeTypes);
    setIsEditDialogOpen(false);
    setEditingFeeType(null);
    form.reset();
    onDataChange?.();
  };

  // Handle delete fee type
  const handleDelete = () => {
    if (!deletingFeeType) return;

    // Check if fee type is in use (mock check)
    const isInUse = deletingFeeType.isRequired; // Simplified check
    
    if (isInUse) {
      // Show error - fee type is in use
      return;
    }

    setFeeTypes(feeTypes.filter(ft => ft.id !== deletingFeeType.id));
    setDeletingFeeType(null);
    onDataChange?.();
  };

  // Open edit dialog
  const openEditDialog = (feeType: FeeType) => {
    setEditingFeeType(feeType);
    form.reset({
      name: feeType.name,
      description: feeType.description || '',
      category: feeType.category,
      isActive: feeType.isActive,
      isRequired: feeType.isRequired,
    });
    setIsEditDialogOpen(true);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tag className="h-5 w-5 text-sky-600" />
            <CardTitle>Fee Types</CardTitle>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-gradient-to-r from-sky-600 to-violet-600">
                <Plus className="h-4 w-4 mr-2" />
                Add Fee Type
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Fee Type</DialogTitle>
              </DialogHeader>
              <form onSubmit={form.handleSubmit(handleAdd)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    {...form.register('name')}
                    placeholder="Enter fee type name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={form.watch('category')}
                    onValueChange={(value) => form.setValue('category', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACADEMIC">Academic</SelectItem>
                      <SelectItem value="TRANSPORT">Transport</SelectItem>
                      <SelectItem value="HOSTEL">Hostel</SelectItem>
                      <SelectItem value="LIBRARY">Library</SelectItem>
                      <SelectItem value="LABORATORY">Laboratory</SelectItem>
                      <SelectItem value="SPORTS">Sports</SelectItem>
                      <SelectItem value="OTHER">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...form.register('description')}
                    placeholder="Enter description"
                    rows={2}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isRequired"
                    checked={form.watch('isRequired')}
                    onCheckedChange={(checked) => form.setValue('isRequired', checked)}
                  />
                  <Label htmlFor="isRequired">Required for all students</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={form.watch('isActive')}
                    onCheckedChange={(checked) => form.setValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Fee Type</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {feeTypes.length === 0 ? (
          <div className="text-center py-8 text-slate-500">
            <Tag className="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <p>No fee types configured</p>
            <p className="text-sm">Add your first fee type to get started</p>
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="fee-types">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                  {feeTypes.map((feeType, index) => (
                    <Draggable key={feeType.id} draggableId={feeType.id!} index={index}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`flex items-center gap-3 p-3 bg-white border rounded-lg transition-shadow ${
                            snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-sm'
                          }`}
                        >
                          <div {...provided.dragHandleProps} className="cursor-grab">
                            <GripVertical className="h-4 w-4 text-slate-400" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-slate-900">{feeType.name}</h4>
                              <Badge className={categoryColors[feeType.category]}>
                                {feeType.category}
                              </Badge>
                              {feeType.isRequired && (
                                <Badge variant="outline" className="text-xs">Required</Badge>
                              )}
                              {!feeType.isActive && (
                                <Badge variant="secondary" className="text-xs">Inactive</Badge>
                              )}
                            </div>
                            {feeType.description && (
                              <p className="text-sm text-slate-600">{feeType.description}</p>
                            )}
                          </div>

                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(feeType)}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setDeletingFeeType(feeType)}
                              disabled={feeType.isRequired}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Fee Type</DialogTitle>
            </DialogHeader>
            <form onSubmit={form.handleSubmit(handleEdit)} className="space-y-4">
              {/* Same form fields as add dialog */}
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name *</Label>
                <Input
                  id="edit-name"
                  {...form.register('name')}
                  placeholder="Enter fee type name"
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!deletingFeeType} onOpenChange={() => setDeletingFeeType(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Fee Type</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete "{deletingFeeType?.name}"? This action cannot be undone.
                {deletingFeeType?.isRequired && (
                  <span className="block mt-2 text-red-600 font-medium">
                    This fee type is required and cannot be deleted.
                  </span>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={deletingFeeType?.isRequired}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
}
