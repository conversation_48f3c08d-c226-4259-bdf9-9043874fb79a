/**
 * Student React Query Hooks
 *
 * Comprehensive hooks for student data management with:
 * - Query hooks for data fetching
 * - Mutation hooks for data modification
 * - Optimistic updates
 * - Cache management
 * - Error handling
 * - Loading states
 */

import { studentService, type StudentQuery } from '@/api/services/studentService';
import { 
  Student, 
  StudentCreate, 
  StudentUpdate, 
  StudentFilters,
  StudentListResponse,
  StudentToggleResponse,
  StudentPhotoResponse,
  StudentImportResponse
} from '@/types';
import { useQueryClient } from '@tanstack/react-query';
import { useMutationBase } from './useMutationBase';
import { useQueryBase } from './useQueryBase';

// Query Keys
export const studentKeys = {
  all: ['students'] as const,
  lists: () => [...studentKeys.all, 'list'] as const,
  list: (filters: StudentFilters) => [...studentKeys.lists(), filters] as const,
  details: () => [...studentKeys.all, 'detail'] as const,
  detail: (id: string) => [...studentKeys.details(), id] as const,
  stats: () => [...studentKeys.all, 'stats'] as const,
  search: (query: string) => [...studentKeys.all, 'search', query] as const,
  byClass: (classId: number) => [...studentKeys.all, 'class', classId] as const,
  bySection: (sectionId: number) => [...studentKeys.all, 'section', sectionId] as const,
};

// Query Hooks
export function useStudents(filters: StudentFilters = {}, options?: { keepPreviousData?: boolean }) {
  return useQueryBase(
    studentKeys.list(filters),
    () => studentService.getStudents(filters),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
      keepPreviousData: options?.keepPreviousData ?? true,
    }
  );
}

export function useStudent(id: string, enabled = true) {
  return useQueryBase(
    studentKeys.detail(id),
    () => studentService.getStudent(id),
    {
      enabled: enabled && !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useStudentStats() {
  return useQueryBase(
    studentKeys.stats(),
    () => studentService.getStudentStats(),
    {
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
}

export function useSearchStudents(query: string, limit = 10) {
  return useQueryBase(
    studentKeys.search(query),
    () => studentService.searchStudents(query),
    {
      enabled: query.length >= 2, // Only search with 2+ characters
      staleTime: 30 * 1000, // 30 seconds
    }
  );
}

export function useStudentsByClass(classId: number) {
  return useQueryBase(
    studentKeys.byClass(classId),
    () => studentService.getStudentsByClass(classId.toString()),
    {
      enabled: !!classId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useStudentsBySection(sectionId: number) {
  return useQueryBase(
    studentKeys.bySection(sectionId),
    () => studentService.getStudentsByGrade(sectionId.toString()),
    {
      enabled: !!sectionId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Mutation Hooks
export function useCreateStudent() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (studentData: StudentCreate) => studentService.createStudent(studentData),
    {
      successMessage: 'Student created successfully!',
      errorMessage: 'Failed to create student',
      invalidateQueries: [[...studentKeys.lists()], [...studentKeys.stats()]],
      onSuccess: newStudent => {
        // Add to cache optimistically
        queryClient.setQueryData(studentKeys.detail(newStudent.id), newStudent);
      },
    }
  );
}

export function useUpdateStudent() {
  const queryClient = useQueryClient();

  return useMutationBase(
    ({ id, data }: { id: string; data: StudentUpdate }) => studentService.updateStudent(id, data),
    {
      successMessage: 'Student updated successfully!',
      errorMessage: 'Failed to update student',
      invalidateQueries: [[...studentKeys.lists()], [...studentKeys.stats()]],
      onSuccess: (updatedStudent, { id }) => {
        // Update cache
        queryClient.setQueryData(studentKeys.detail(id), updatedStudent);
      },
    }
  );
}

export function useDeleteStudent() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (id: string) => studentService.deleteStudent(id),
    {
      successMessage: 'Student deleted successfully!',
      errorMessage: 'Failed to delete student',
      invalidateQueries: [[...studentKeys.lists()], [...studentKeys.stats()]],
      onSuccess: (_, id) => {
        // Remove from cache
        queryClient.removeQueries({ queryKey: studentKeys.detail(id) });
      },
    }
  );
}

export function useToggleStudent() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (id: string) => studentService.toggleStudent(id),
    {
      successMessage: (data: StudentToggleResponse) => 
        `Student ${data.is_active ? 'activated' : 'deactivated'} successfully!`,
      errorMessage: 'Failed to toggle student status',
      invalidateQueries: [[...studentKeys.lists()], [...studentKeys.stats()]],
      onSuccess: (data: StudentToggleResponse, id) => {
        // Update cache optimistically
        queryClient.setQueryData(studentKeys.detail(id), (old: Student | undefined) => {
          if (!old) return old;
          return { ...old, is_active: data.is_active };
        });
      },
    }
  );
}

export function useUploadStudentPhoto() {
  const queryClient = useQueryClient();

  return useMutationBase(
    ({ id, file }: { id: string; file: File }) => studentService.uploadStudentPhoto(id, file),
    {
      successMessage: 'Student photo uploaded successfully!',
      errorMessage: 'Failed to upload student photo',
      invalidateQueries: [[...studentKeys.lists()]],
      onSuccess: (data: StudentPhotoResponse, { id }) => {
        // Update cache
        queryClient.setQueryData(studentKeys.detail(id), (old: Student | undefined) => {
          if (!old) return old;
          return { ...old, photo_url: data.photo_url };
        });
      },
    }
  );
}

export function useImportStudents() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (file: File) => studentService.importStudents(file),
    {
      successMessage: (data: StudentImportResponse) => 
        `Import completed! ${data.created} created, ${data.updated} updated${data.errors.length > 0 ? `, ${data.errors.length} errors` : ''}`,
      errorMessage: 'Failed to import students',
      invalidateQueries: [[...studentKeys.lists()], [...studentKeys.stats()]],
    }
  );
}

// Utility hooks
export function useStudentCache() {
  const queryClient = useQueryClient();

  return {
    // Prefetch student data
    prefetchStudent: (id: string) => {
      return queryClient.prefetchQuery({
        queryKey: studentKeys.detail(id),
        queryFn: () => studentService.getStudent(id),
        staleTime: 5 * 60 * 1000,
      });
    },

    // Prefetch students list
    prefetchStudents: (filters: StudentFilters = {}) => {
      return queryClient.prefetchQuery({
        queryKey: studentKeys.list(filters),
        queryFn: () => studentService.getStudents(filters),
        staleTime: 2 * 60 * 1000,
      });
    },

    // Invalidate all student queries
    invalidateStudents: () => {
      return queryClient.invalidateQueries({ queryKey: studentKeys.all });
    },

    // Clear student cache
    clearStudentCache: () => {
      queryClient.removeQueries({ queryKey: studentKeys.all });
    },

    // Get cached student data
    getCachedStudent: (id: string): Student | undefined => {
      return queryClient.getQueryData(studentKeys.detail(id));
    },

    // Set student data in cache
    setCachedStudent: (id: string, student: Student) => {
      queryClient.setQueryData(studentKeys.detail(id), student);
    },

    // Optimistically update student
    optimisticUpdateStudent: (id: string, updates: Partial<Student>) => {
      queryClient.setQueryData(studentKeys.detail(id), (old: Student | undefined) => {
        if (!old) return old;
        return { ...old, ...updates };
      });
    },
  };
}

// Export all hooks for convenience
export const studentHooks = {
  // Queries
  useStudents,
  useStudent,
  useStudentStats,
  useSearchStudents,
  useStudentsByClass,
  useStudentsBySection,

  // Mutations
  useCreateStudent,
  useUpdateStudent,
  useDeleteStudent,
  useToggleStudent,
  useUploadStudentPhoto,
  useImportStudents,

  // Utilities
  useStudentCache,

  // Keys
  studentKeys,
}; 