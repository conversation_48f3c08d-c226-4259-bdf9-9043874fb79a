/**
 * Enhanced Teachers Page - Complete State Management + API Integration
 *
 * Features:
 * - React Query hooks for data fetching
 * - Zustand auth store integration
 * - API client with dummy data fallback
 * - Professional UI with shadcn/ui components
 * - Real-time search and filtering
 * - Optimistic updates
 * - Error handling and loading states
 * - Responsive design
 */

'use client';

import { Award, Building, Clock, Grid3X3, List, Plus, Search, Users } from 'lucide-react';
import { useState } from 'react';

// shadcn/ui components
import type { TeacherFilters } from '@/api/services/teacherService';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Hooks and services
import { useAuth } from '@/hooks/useAuth';
import { useTeachers, useTeacherStats } from '@/hooks/useTeachers';
import { mockDepartments } from '@/lib/mockTeachers';

export default function EnhancedTeachersPage() {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const pageSize = 12;

  // Auth state
  const { user } = useAuth();

  // Build filters object
  const filters: TeacherFilters = {
    page: currentPage,
    pageSize,
  };

  if (searchTerm) {
    filters.search = searchTerm;
  }
  if (selectedDepartment !== 'all') {
    filters.department = selectedDepartment;
  }
  if (selectedStatus !== 'all') {
    filters.status = selectedStatus as 'ACTIVE' | 'INACTIVE';
  }

  // Data fetching with React Query
  const {
    data: teachersResult,
    isLoading: teachersLoading,
    error: teachersError,
    refetch: refetchTeachers,
    isFetching,
  } = useTeachers(filters);

  const { data: stats, isLoading: statsLoading, error: statsError } = useTeacherStats();

  const teachers = teachersResult?.data || [];
  const totalTeachers = teachersResult?.total || 0;
  const totalPages = teachersResult?.totalPages || 1;

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleDepartmentChange = (value: string) => {
    setSelectedDepartment(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Loading state
  if (teachersLoading && !teachers.length) {
    return <LoadingSkeleton />;
  }

  // Error state
  if (teachersError && !teachers.length) {
    return <ErrorState error={teachersError} onRetry={() => refetchTeachers()} />;
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Teachers</h1>
          <p className='text-muted-foreground'>
            Manage and view all teachers in the system
            <Badge variant='outline' className='ml-2'>
              {process.env.NEXT_PUBLIC_USE_DUMMY_DATA === 'true' ? 'Demo Mode' : 'Live Data'}
            </Badge>
          </p>
        </div>
        <Button className='w-full sm:w-auto'>
          <Plus className='mr-2 h-4 w-4' />
          Add Teacher
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search teachers...'
                value={searchTerm}
                onChange={e => handleSearch(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={selectedDepartment} onValueChange={handleDepartmentChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select department' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Departments</SelectItem>
                {mockDepartments.slice(1).map(dept => (
                  <SelectItem key={dept} value={dept as string}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Status</SelectItem>
                <SelectItem value='ACTIVE'>Active</SelectItem>
                <SelectItem value='INACTIVE'>Inactive</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex gap-2'>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size='icon'
                onClick={() => setViewMode('grid')}
                className='flex-1 sm:flex-none'
              >
                <Grid3X3 className='h-4 w-4' />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size='icon'
                onClick={() => setViewMode('table')}
                className='flex-1 sm:flex-none'
              >
                <List className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        <StatsCard
          title='Total'
          value={stats?.total || 0}
          icon={Users}
          loading={statsLoading}
          color='blue'
        />
        <StatsCard
          title='Active'
          value={stats?.active || 0}
          icon={Award}
          loading={statsLoading}
          color='green'
        />
        <StatsCard
          title='Departments'
          value={stats?.departments || 0}
          icon={Building}
          loading={statsLoading}
          color='purple'
        />
        <StatsCard
          title='Avg. Experience'
          value={`${stats?.averageExperience || 0}y`}
          icon={Clock}
          loading={statsLoading}
          color='orange'
        />
      </div>

      {/* Content */}
      <Tabs value={viewMode} onValueChange={value => setViewMode(value as 'grid' | 'table')}>
        <TabsList className='grid w-full grid-cols-2 lg:w-[400px]'>
          <TabsTrigger value='grid'>Grid View</TabsTrigger>
          <TabsTrigger value='table'>Table View</TabsTrigger>
        </TabsList>

        {/* Loading indicator */}
        {isFetching && (
          <div className='flex items-center justify-center py-4'>
            <div className='flex items-center space-x-2 text-muted-foreground'>
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-current' />
              <span className='text-sm'>Updating...</span>
            </div>
          </div>
        )}

        {/* Grid View */}
        <TabsContent value='grid' className='space-y-6'>
          {teachers.length === 0 ? (
            <EmptyState
              searchTerm={searchTerm}
              hasFilters={selectedDepartment !== 'all' || selectedStatus !== 'all'}
            />
          ) : (
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4'>
              {teachers.map(teacher => (
                <TeacherCard key={teacher.id} teacher={teacher} />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Table View */}
        <TabsContent value='table'>
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher</TableHead>
                  <TableHead className='hidden sm:table-cell'>Department</TableHead>
                  <TableHead className='hidden md:table-cell'>Subject</TableHead>
                  <TableHead className='hidden lg:table-cell'>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachers.map(teacher => (
                  <TableRow key={teacher.id}>
                    <TableCell>
                      <div className='flex items-center space-x-3'>
                        <Avatar className='h-8 w-8'>
                          <AvatarImage
                            src={`https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`}
                          />
                          <AvatarFallback className='text-xs'>
                            {teacher.name
                              .split(' ')
                              .map(n => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className='font-medium'>{teacher.name}</div>
                          <div className='text-sm text-muted-foreground sm:hidden'>
                            {teacher.department}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className='hidden sm:table-cell'>{teacher.department}</TableCell>
                    <TableCell className='hidden md:table-cell'>{teacher.subject}</TableCell>
                    <TableCell className='hidden lg:table-cell'>{teacher.email}</TableCell>
                    <TableCell>
                      <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
                        {teacher.status}
                      </Badge>
                    </TableCell>
                    <TableCell className='text-right'>
                      <div className='flex justify-end gap-2'>
                        <Button variant='ghost' size='sm'>
                          View
                        </Button>
                        <Button variant='ghost' size='sm'>
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalTeachers}
          pageSize={pageSize}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
}

// Helper Components
function LoadingSkeleton() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <div className='space-y-2'>
        <Skeleton className='h-8 w-48' />
        <Skeleton className='h-4 w-96' />
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className='h-10 w-full' />
        ))}
      </div>
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <Skeleton className='h-8 w-8 rounded' />
                <div className='space-y-1'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-6 w-16' />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-4 mb-4'>
                <Skeleton className='h-12 w-12 rounded-full' />
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-3 w-20' />
                </div>
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-3 w-full' />
                <Skeleton className='h-3 w-3/4' />
                <Skeleton className='h-3 w-1/2' />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

function ErrorState({ error, onRetry }: { error: any; onRetry: () => void }) {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      <Card className='max-w-md mx-auto'>
        <CardHeader className='text-center'>
          <div className='mx-auto h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4'>
            <span className='text-red-600 text-2xl'>⚠️</span>
          </div>
          <CardTitle>Error Loading Teachers</CardTitle>
          <CardDescription>{error?.message || 'An unexpected error occurred'}</CardDescription>
        </CardHeader>
        <CardContent className='flex justify-center'>
          <Button onClick={onRetry}>Try Again</Button>
        </CardContent>
      </Card>
    </div>
  );
}

function EmptyState({ searchTerm, hasFilters }: { searchTerm: string; hasFilters: boolean }) {
  return (
    <Card>
      <CardContent className='flex flex-col items-center justify-center py-16'>
        <div className='text-6xl mb-4'>👨‍🏫</div>
        <CardTitle className='mb-2'>No Teachers Found</CardTitle>
        <CardDescription>
          {searchTerm || hasFilters
            ? 'No teachers match your current filters.'
            : 'There are no teachers in the system yet.'}
        </CardDescription>
      </CardContent>
    </Card>
  );
}

function TeacherCard({ teacher }: { teacher: any }) {
  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardHeader className='pb-4'>
        <div className='flex items-center space-x-3'>
          <Avatar>
            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`} />
            <AvatarFallback>
              {teacher.name
                .split(' ')
                .map((n: string) => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div className='flex-1 min-w-0'>
            <CardTitle className='text-base truncate'>{teacher.name}</CardTitle>
            <CardDescription className='truncate'>{teacher.subject}</CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className='space-y-3'>
        <div className='space-y-2 text-sm'>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>Department:</span>
            <span className='font-medium truncate ml-2'>{teacher.department}</span>
          </div>
          <div className='flex justify-between'>
            <span className='text-muted-foreground'>Email:</span>
            <span className='font-medium truncate ml-2'>{teacher.email}</span>
          </div>
          <div className='flex justify-between items-center'>
            <span className='text-muted-foreground'>Status:</span>
            <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
              {teacher.status}
            </Badge>
          </div>
        </div>
      </CardContent>

      <CardContent className='pt-0'>
        <div className='flex gap-2'>
          <Button variant='outline' size='sm' className='flex-1'>
            View
          </Button>
          <Button variant='outline' size='sm' className='flex-1'>
            Edit
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function StatsCard({
  title,
  value,
  icon: Icon,
  loading,
  color,
}: {
  title: string;
  value: string | number;
  icon: any;
  loading: boolean;
  color: string;
}) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600',
    orange: 'bg-orange-100 text-orange-600',
  };

  return (
    <Card>
      <CardContent className='p-6'>
        <div className='flex items-center space-x-2'>
          <div className={`p-2 rounded-lg ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className='h-4 w-4' />
          </div>
          <div>
            <p className='text-sm font-medium text-muted-foreground'>{title}</p>
            {loading ? (
              <Skeleton className='h-6 w-12' />
            ) : (
              <p className='text-2xl font-bold'>{value}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function Pagination({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
}) {
  return (
    <div className='flex flex-col sm:flex-row items-center justify-between gap-4'>
      <p className='text-sm text-muted-foreground'>
        Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalItems)}{' '}
        of {totalItems} teachers
      </p>
      <div className='flex items-center space-x-2'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
          disabled={currentPage === 1}
        >
          Previous
        </Button>

        <div className='flex space-x-1'>
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const pageNum = i + 1;
            return (
              <Button
                key={pageNum}
                variant={currentPage === pageNum ? 'default' : 'outline'}
                size='sm'
                onClick={() => onPageChange(pageNum)}
                className='w-10'
              >
                {pageNum}
              </Button>
            );
          })}
        </div>

        <Button
          variant='outline'
          size='sm'
          onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
          disabled={currentPage === totalPages}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
