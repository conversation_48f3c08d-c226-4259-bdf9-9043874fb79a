/**
 * Date Utilities
 * 
 * Comprehensive date manipulation and formatting utilities
 */

import { format, parseISO, isValid, differenceInDays, differenceInHours, differenceInMinutes, addDays, subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

/**
 * Format date to display string
 */
export function formatDate(date: string | Date, formatStr: string = 'MMM dd, yyyy'): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return 'Invalid Date';
    return format(dateObj, formatStr);
  } catch (error) {
    console.warn('Error formatting date:', error);
    return 'Invalid Date';
  }
}

/**
 * Format date for input fields
 */
export function formatDateForInput(date: string | Date): string {
  return formatDate(date, 'yyyy-MM-dd');
}

/**
 * Format datetime
 */
export function formatDateTime(date: string | Date): string {
  return formatDate(date, 'MMM dd, yyyy HH:mm');
}

/**
 * Format time only
 */
export function formatTime(date: string | Date): string {
  return formatDate(date, 'HH:mm');
}

/**
 * Get relative time (e.g., "2 hours ago", "in 3 days")
 */
export function getRelativeTime(date: string | Date): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return 'Invalid Date';

    const now = new Date();
    const diffInMinutes = differenceInMinutes(now, dateObj);
    const diffInHours = differenceInHours(now, dateObj);
    const diffInDays = differenceInDays(now, dateObj);

    if (Math.abs(diffInMinutes) < 1) {
      return 'just now';
    } else if (Math.abs(diffInMinutes) < 60) {
      return diffInMinutes > 0 
        ? `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
        : `in ${Math.abs(diffInMinutes)} minute${Math.abs(diffInMinutes) === 1 ? '' : 's'}`;
    } else if (Math.abs(diffInHours) < 24) {
      return diffInHours > 0
        ? `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
        : `in ${Math.abs(diffInHours)} hour${Math.abs(diffInHours) === 1 ? '' : 's'}`;
    } else {
      return diffInDays > 0
        ? `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
        : `in ${Math.abs(diffInDays)} day${Math.abs(diffInDays) === 1 ? '' : 's'}`;
    }
  } catch (error) {
    console.warn('Error calculating relative time:', error);
    return 'Invalid Date';
  }
}

/**
 * Check if date is today
 */
export function isToday(date: string | Date): boolean {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return false;
    return differenceInDays(new Date(), dateObj) === 0;
  } catch (error) {
    return false;
  }
}

/**
 * Check if date is in the past
 */
export function isPast(date: string | Date): boolean {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return false;
    return dateObj < new Date();
  } catch (error) {
    return false;
  }
}

/**
 * Check if date is in the future
 */
export function isFuture(date: string | Date): boolean {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return false;
    return dateObj > new Date();
  } catch (error) {
    return false;
  }
}

/**
 * Get date range for common periods
 */
export function getDateRange(period: 'today' | 'yesterday' | 'thisWeek' | 'lastWeek' | 'thisMonth' | 'lastMonth'): {
  start: Date;
  end: Date;
} {
  const now = new Date();

  switch (period) {
    case 'today':
      return {
        start: startOfDay(now),
        end: endOfDay(now),
      };
    case 'yesterday':
      const yesterday = subDays(now, 1);
      return {
        start: startOfDay(yesterday),
        end: endOfDay(yesterday),
      };
    case 'thisWeek':
      return {
        start: startOfWeek(now),
        end: endOfWeek(now),
      };
    case 'lastWeek':
      const lastWeekStart = startOfWeek(subDays(now, 7));
      const lastWeekEnd = endOfWeek(subDays(now, 7));
      return {
        start: lastWeekStart,
        end: lastWeekEnd,
      };
    case 'thisMonth':
      return {
        start: startOfMonth(now),
        end: endOfMonth(now),
      };
    case 'lastMonth':
      const lastMonth = subDays(startOfMonth(now), 1);
      return {
        start: startOfMonth(lastMonth),
        end: endOfMonth(lastMonth),
      };
    default:
      return {
        start: startOfDay(now),
        end: endOfDay(now),
      };
  }
}

/**
 * Calculate age from birth date
 */
export function calculateAge(birthDate: string | Date): number {
  try {
    const birthDateObj = typeof birthDate === 'string' ? parseISO(birthDate) : birthDate;
    if (!isValid(birthDateObj)) return 0;
    
    const today = new Date();
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDiff = today.getMonth() - birthDateObj.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.warn('Error calculating age:', error);
    return 0;
  }
}

/**
 * Get academic year from date
 */
export function getAcademicYear(date: string | Date = new Date()): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    const year = dateObj.getFullYear();
    const month = dateObj.getMonth();
    
    // Academic year typically starts in August/September
    if (month >= 7) { // August onwards
      return `${year}-${year + 1}`;
    } else {
      return `${year - 1}-${year}`;
    }
  } catch (error) {
    console.warn('Error getting academic year:', error);
    return '';
  }
}

/**
 * Get current term based on date
 */
export function getCurrentTerm(date: string | Date = new Date()): 'FIRST' | 'SECOND' | 'THIRD' {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return 'FIRST';
    
    const month = dateObj.getMonth();
    
    if (month >= 7 && month <= 11) { // Aug-Dec
      return 'FIRST';
    } else if (month >= 0 && month <= 3) { // Jan-Apr
      return 'SECOND';
    } else { // May-Jul
      return 'THIRD';
    }
  } catch (error) {
    console.warn('Error getting current term:', error);
    return 'FIRST';
  }
}

/**
 * Generate date range array
 */
export function generateDateRange(startDate: string | Date, endDate: string | Date): Date[] {
  try {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    
    if (!isValid(start) || !isValid(end)) return [];
    
    const dates: Date[] = [];
    let currentDate = start;
    
    while (currentDate <= end) {
      dates.push(new Date(currentDate));
      currentDate = addDays(currentDate, 1);
    }
    
    return dates;
  } catch (error) {
    console.warn('Error generating date range:', error);
    return [];
  }
}

/**
 * Check if two dates are the same day
 */
export function isSameDay(date1: string | Date, date2: string | Date): boolean {
  try {
    const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
    const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;
    
    if (!isValid(d1) || !isValid(d2)) return false;
    
    return differenceInDays(d1, d2) === 0;
  } catch (error) {
    return false;
  }
}
