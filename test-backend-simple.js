/**
 * Simple Backend Test using Node.js built-in fetch
 */

async function testBackend() {
  console.log('🧪 Testing backend connection...\n');

  try {
    // Test if backend is running
    const response = await fetch('http://127.0.0.1:8000/docs');
    console.log('✅ Backend is running!');
    console.log('   Status:', response.status);
    console.log('   URL:', response.url);
    
    if (response.status === 200) {
      console.log('   FastAPI docs are accessible');
    }
    
  } catch (error) {
    console.log('❌ Backend connection failed:', error.message);
    console.log('   Make sure the backend is running on port 8000');
    console.log('   Try: cd ../backend && python start_server.py');
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Test students endpoint
    const response = await fetch('http://127.0.0.1:8000/api/v1/students?page=1&size=5');
    console.log('Students endpoint test:');
    console.log('   Status:', response.status);
    
    if (response.status === 401) {
      console.log('   ✅ Expected 401 (authentication required)');
      console.log('   This confirms the endpoint exists and requires auth');
    } else if (response.status === 200) {
      console.log('   ✅ Unexpected 200 (no auth required?)');
      const data = await response.json();
      console.log('   Data:', JSON.stringify(data, null, 2));
    } else {
      console.log('   ❌ Unexpected status:', response.status);
      const text = await response.text();
      console.log('   Response:', text);
    }
    
  } catch (error) {
    console.log('❌ Students endpoint failed:', error.message);
  }
}

testBackend().catch(console.error);
