'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>tor, Percent, DollarSign, Plus, Edit2, Trash2, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  LateFeeRuleSchema, 
  DiscountRuleSchema, 
  type LateFeeRule, 
  type DiscountRule,
  type FeeCalculationPreview 
} from '../../schemas/fees.schemas';

// Mock data
const mockLateFeeRule: LateFeeRule = {
  id: '1',
  type: 'PERCENTAGE',
  value: 5,
  graceDays: 7,
  maxAmount: 1000,
  isActive: true,
  description: 'Standard late fee policy',
};

const mockDiscountRules: DiscountRule[] = [
  {
    id: '1',
    name: 'Sibling Discount',
    type: 'PERCENTAGE',
    value: 10,
    applicableToFees: ['tuition', 'transport'],
    conditions: {
      siblingDiscount: true,
      earlyPayment: false,
    },
    isActive: true,
  },
  {
    id: '2',
    name: 'Early Payment Discount',
    type: 'FLAT',
    value: 500,
    applicableToFees: ['tuition'],
    conditions: {
      siblingDiscount: false,
      earlyPayment: true,
      earlyPaymentDays: 15,
    },
    isActive: true,
  },
];

interface LateFeeDiscountRulesProps {
  onDataChange?: () => void;
}

/**
 * LateFeeDiscountRules Component
 * 
 * Manages late fee and discount rules with:
 * - Global late fee configuration
 * - Multiple discount rules
 * - Per-class overrides
 * - Fee calculation preview
 */
export function LateFeeDiscountRules({ onDataChange }: LateFeeDiscountRulesProps) {
  const [lateFeeRule, setLateFeeRule] = useState<LateFeeRule>(mockLateFeeRule);
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>(mockDiscountRules);
  const [previewAmount, setPreviewAmount] = useState<string>('5000');
  const [previewDaysLate, setPreviewDaysLate] = useState<string>('10');
  const [calculationPreview, setCalculationPreview] = useState<FeeCalculationPreview | null>(null);
  const [isAddDiscountOpen, setIsAddDiscountOpen] = useState(false);

  const lateFeeForm = useForm<LateFeeRule>({
    resolver: zodResolver(LateFeeRuleSchema),
    defaultValues: lateFeeRule,
  });

  const discountForm = useForm<DiscountRule>({
    resolver: zodResolver(DiscountRuleSchema),
    defaultValues: {
      name: '',
      type: 'PERCENTAGE',
      value: 0,
      applicableToFees: [],
      conditions: {
        siblingDiscount: false,
        earlyPayment: false,
      },
      isActive: true,
    },
  });

  // Calculate fee preview
  const calculatePreview = () => {
    const baseAmount = parseFloat(previewAmount) || 0;
    const daysLate = parseInt(previewDaysLate) || 0;
    
    let lateFee = 0;
    let discount = 0;
    const appliedRules: any[] = [];

    // Calculate late fee
    if (daysLate > lateFeeRule.graceDays && lateFeeRule.isActive) {
      if (lateFeeRule.type === 'PERCENTAGE') {
        lateFee = (baseAmount * lateFeeRule.value) / 100;
      } else {
        lateFee = lateFeeRule.value;
      }
      
      if (lateFeeRule.maxAmount && lateFee > lateFeeRule.maxAmount) {
        lateFee = lateFeeRule.maxAmount;
      }

      appliedRules.push({
        type: 'LATE_FEE',
        name: 'Late Fee',
        value: lateFee,
        calculation: lateFeeRule.type === 'PERCENTAGE' 
          ? `${lateFeeRule.value}% of ₹${baseAmount}`
          : `Flat ₹${lateFeeRule.value}`,
      });
    }

    // Calculate discounts (simplified)
    const activeDiscounts = discountRules.filter(rule => rule.isActive);
    activeDiscounts.forEach(rule => {
      let discountAmount = 0;
      if (rule.type === 'PERCENTAGE') {
        discountAmount = (baseAmount * rule.value) / 100;
      } else {
        discountAmount = rule.value;
      }
      
      if (rule.maxAmount && discountAmount > rule.maxAmount) {
        discountAmount = rule.maxAmount;
      }
      
      discount += discountAmount;
      appliedRules.push({
        type: 'DISCOUNT',
        name: rule.name,
        value: -discountAmount,
        calculation: rule.type === 'PERCENTAGE' 
          ? `${rule.value}% discount`
          : `Flat ₹${rule.value} discount`,
      });
    });

    const finalAmount = baseAmount + lateFee - discount;

    setCalculationPreview({
      baseAmount,
      lateFee,
      discount,
      finalAmount: Math.max(0, finalAmount),
      daysLate,
      appliedRules,
    });
  };

  // Handle late fee rule update
  const handleLateFeeUpdate = (data: LateFeeRule) => {
    setLateFeeRule(data);
    onDataChange?.();
  };

  // Handle discount rule add
  const handleAddDiscount = (data: DiscountRule) => {
    const newRule: DiscountRule = {
      ...data,
      id: Date.now().toString(),
    };
    setDiscountRules(prev => [...prev, newRule]);
    setIsAddDiscountOpen(false);
    discountForm.reset();
    onDataChange?.();
  };

  // Handle discount rule delete
  const handleDeleteDiscount = (id: string) => {
    setDiscountRules(prev => prev.filter(rule => rule.id !== id));
    onDataChange?.();
  };

  return (
    <div className="space-y-6">
      {/* Late Fee Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-red-600" />
            Late Fee Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={lateFeeForm.handleSubmit(handleLateFeeUpdate)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Late Fee Type</Label>
                <Select
                  value={lateFeeForm.watch('type')}
                  onValueChange={(value) => lateFeeForm.setValue('type', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FLAT">Flat Amount</SelectItem>
                    <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>
                  {lateFeeForm.watch('type') === 'PERCENTAGE' ? 'Percentage (%)' : 'Amount (₹)'}
                </Label>
                <Input
                  type="number"
                  {...lateFeeForm.register('value', { valueAsNumber: true })}
                  placeholder={lateFeeForm.watch('type') === 'PERCENTAGE' ? '5' : '100'}
                />
              </div>

              <div className="space-y-2">
                <Label>Grace Period (Days)</Label>
                <Input
                  type="number"
                  {...lateFeeForm.register('graceDays', { valueAsNumber: true })}
                  placeholder="7"
                />
              </div>

              <div className="space-y-2">
                <Label>Maximum Amount (₹)</Label>
                <Input
                  type="number"
                  {...lateFeeForm.register('maxAmount', { valueAsNumber: true })}
                  placeholder="1000"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={lateFeeForm.watch('isActive')}
                onCheckedChange={(checked) => lateFeeForm.setValue('isActive', checked)}
              />
              <Label>Enable late fee calculation</Label>
            </div>

            <Button type="submit" size="sm">
              Update Late Fee Rule
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Discount Rules */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-green-600" />
              Discount Rules
            </CardTitle>
            <Dialog open={isAddDiscountOpen} onOpenChange={setIsAddDiscountOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-gradient-to-r from-sky-600 to-violet-600">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Discount
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add Discount Rule</DialogTitle>
                </DialogHeader>
                <form onSubmit={discountForm.handleSubmit(handleAddDiscount)} className="space-y-4">
                  <div className="space-y-2">
                    <Label>Discount Name</Label>
                    <Input
                      {...discountForm.register('name')}
                      placeholder="Enter discount name"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Type</Label>
                      <Select
                        value={discountForm.watch('type')}
                        onValueChange={(value) => discountForm.setValue('type', value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FLAT">Flat Amount</SelectItem>
                          <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Value</Label>
                      <Input
                        type="number"
                        {...discountForm.register('value', { valueAsNumber: true })}
                        placeholder="10"
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label>Conditions</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={discountForm.watch('conditions.siblingDiscount')}
                          onCheckedChange={(checked) => 
                            discountForm.setValue('conditions.siblingDiscount', checked)
                          }
                        />
                        <Label className="text-sm">Sibling discount</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={discountForm.watch('conditions.earlyPayment')}
                          onCheckedChange={(checked) => 
                            discountForm.setValue('conditions.earlyPayment', checked)
                          }
                        />
                        <Label className="text-sm">Early payment discount</Label>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setIsAddDiscountOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit">Add Discount</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {discountRules.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Percent className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No discount rules configured</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Conditions</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {discountRules.map((rule) => (
                  <TableRow key={rule.id}>
                    <TableCell className="font-medium">{rule.name}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {rule.type === 'PERCENTAGE' ? 'Percentage' : 'Flat Amount'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {rule.type === 'PERCENTAGE' ? `${rule.value}%` : `₹${rule.value}`}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {rule.conditions?.siblingDiscount && (
                          <Badge variant="secondary" className="text-xs">Sibling</Badge>
                        )}
                        {rule.conditions?.earlyPayment && (
                          <Badge variant="secondary" className="text-xs">Early Pay</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                        {rule.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteDiscount(rule.id!)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Fee Calculation Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            Fee Calculation Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="space-y-2">
              <Label>Base Amount (₹)</Label>
              <Input
                type="number"
                value={previewAmount}
                onChange={(e) => setPreviewAmount(e.target.value)}
                placeholder="5000"
              />
            </div>
            <div className="space-y-2">
              <Label>Days Late</Label>
              <Input
                type="number"
                value={previewDaysLate}
                onChange={(e) => setPreviewDaysLate(e.target.value)}
                placeholder="10"
              />
            </div>
            <div className="flex items-end">
              <Button onClick={calculatePreview} className="w-full">
                Calculate
              </Button>
            </div>
          </div>

          {calculationPreview && (
            <div className="border rounded-lg p-4 bg-slate-50">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-sm text-slate-600">Base Amount</p>
                  <p className="text-lg font-semibold">₹{calculationPreview.baseAmount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-600">Late Fee</p>
                  <p className="text-lg font-semibold text-red-600">+₹{calculationPreview.lateFee.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-600">Discount</p>
                  <p className="text-lg font-semibold text-green-600">-₹{calculationPreview.discount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-600">Final Amount</p>
                  <p className="text-xl font-bold text-blue-600">₹{calculationPreview.finalAmount.toLocaleString()}</p>
                </div>
              </div>

              {calculationPreview.appliedRules.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-slate-700 mb-2">Applied Rules:</p>
                  <div className="space-y-1">
                    {calculationPreview.appliedRules.map((rule, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{rule.name}</span>
                        <span className={rule.type === 'LATE_FEE' ? 'text-red-600' : 'text-green-600'}>
                          {rule.type === 'LATE_FEE' ? '+' : ''}₹{Math.abs(rule.value).toLocaleString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
