import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ListCard } from '@/components/shared/ListCard'

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  User: () => <div data-testid="user-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  Phone: () => <div data-testid="phone-icon" />,
  MoreHorizontal: () => <div data-testid="more-icon" />,
  Edit: () => <div data-testid="edit-icon" />,
  Trash: () => <div data-testid="trash-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
}))

describe('ListCard', () => {
  const mockProps = {
    title: '<PERSON> Do<PERSON>',
    subtitle: 'Software Engineer',
    description: 'Experienced developer with 5+ years in React',
  }

  it('renders basic card with title, subtitle, and description', () => {
    render(<ListCard {...mockProps} />)

    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
    expect(screen.getByText('Software Engineer')).toBeInTheDocument()
    expect(screen.getByText('Experienced developer with 5+ years in React')).toBeInTheDocument()
  })

  it('renders avatar when provided', () => {
    const propsWithAvatar = {
      ...mockProps,
      avatar: {
        src: '/avatar.jpg',
        alt: 'John Doe Avatar',
        fallback: 'JD',
      },
    }

    render(<ListCard {...propsWithAvatar} />)

    const avatar = screen.getByRole('img', { name: 'John Doe Avatar' })
    expect(avatar).toBeInTheDocument()
  })

  it('renders avatar fallback when no src provided', () => {
    const propsWithFallback = {
      ...mockProps,
      avatar: {
        fallback: 'JD',
      },
    }

    render(<ListCard {...propsWithFallback} />)

    expect(screen.getByText('JD')).toBeInTheDocument()
  })

  it('renders badge when provided', () => {
    const propsWithBadge = {
      ...mockProps,
      badge: {
        text: 'Active',
        variant: 'success' as const,
      },
    }

    render(<ListCard {...propsWithBadge} />)

    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('renders metadata items correctly', () => {
    const propsWithMetadata = {
      ...mockProps,
      metadata: [
        { icon: 'Mail', label: 'Email', value: '<EMAIL>' },
        { icon: 'Phone', label: 'Phone', value: '+1234567890' },
      ],
    }

    render(<ListCard {...propsWithMetadata} />)

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('+1234567890')).toBeInTheDocument()
    expect(screen.getByTestId('mail-icon')).toBeInTheDocument()
    expect(screen.getByTestId('phone-icon')).toBeInTheDocument()
  })

  it('renders action buttons correctly', async () => {
    const mockOnEdit = jest.fn()
    const mockOnDelete = jest.fn()
    const mockOnView = jest.fn()

    const propsWithActions = {
      ...mockProps,
      actions: [
        { icon: 'Eye', label: 'View', onClick: mockOnView },
        { icon: 'Edit', label: 'Edit', onClick: mockOnEdit },
        { icon: 'Trash', label: 'Delete', onClick: mockOnDelete, variant: 'destructive' as const },
      ],
    }

    const user = userEvent.setup()
    render(<ListCard {...propsWithActions} />)

    // Check that action buttons are rendered
    const viewButton = screen.getByRole('button', { name: 'View' })
    const editButton = screen.getByRole('button', { name: 'Edit' })
    const deleteButton = screen.getByRole('button', { name: 'Delete' })

    expect(viewButton).toBeInTheDocument()
    expect(editButton).toBeInTheDocument()
    expect(deleteButton).toBeInTheDocument()

    // Test action button clicks
    await user.click(viewButton)
    expect(mockOnView).toHaveBeenCalled()

    await user.click(editButton)
    expect(mockOnEdit).toHaveBeenCalled()

    await user.click(deleteButton)
    expect(mockOnDelete).toHaveBeenCalled()
  })

  it('handles click events when clickable', async () => {
    const mockOnClick = jest.fn()
    const clickableProps = {
      ...mockProps,
      onClick: mockOnClick,
    }

    const user = userEvent.setup()
    render(<ListCard {...clickableProps} />)

    const card = screen.getByRole('button')
    expect(card).toBeInTheDocument()

    await user.click(card)
    expect(mockOnClick).toHaveBeenCalled()
  })

  it('supports keyboard navigation when clickable', async () => {
    const mockOnClick = jest.fn()
    const clickableProps = {
      ...mockProps,
      onClick: mockOnClick,
    }

    const user = userEvent.setup()
    render(<ListCard {...clickableProps} />)

    const card = screen.getByRole('button')
    
    // Test Enter key
    await user.type(card, '{enter}')
    expect(mockOnClick).toHaveBeenCalled()

    // Test Space key
    mockOnClick.mockClear()
    await user.type(card, ' ')
    expect(mockOnClick).toHaveBeenCalled()
  })

  it('renders in horizontal layout', () => {
    const horizontalProps = {
      ...mockProps,
      layout: 'horizontal' as const,
    }

    render(<ListCard {...horizontalProps} />)

    // Check that the card has horizontal layout class
    const card = screen.getByText('John Doe').closest('[role="article"]')
    expect(card).toHaveClass('flex-row')
  })

  it('renders in vertical layout by default', () => {
    render(<ListCard {...mockProps} />)

    // Check that the card has vertical layout class (default)
    const card = screen.getByText('John Doe').closest('[role="article"]')
    expect(card).toHaveClass('flex-col')
  })

  it('applies loading state correctly', () => {
    const loadingProps = {
      ...mockProps,
      isLoading: true,
    }

    render(<ListCard {...loadingProps} />)

    // Check for loading skeletons
    expect(screen.getByTestId('card-loading')).toBeInTheDocument()
  })

  it('handles empty metadata gracefully', () => {
    const propsWithEmptyMetadata = {
      ...mockProps,
      metadata: [],
    }

    render(<ListCard {...propsWithEmptyMetadata} />)

    // Should render without metadata section
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.queryByTestId('metadata-section')).not.toBeInTheDocument()
  })

  it('handles empty actions gracefully', () => {
    const propsWithEmptyActions = {
      ...mockProps,
      actions: [],
    }

    render(<ListCard {...propsWithEmptyActions} />)

    // Should render without actions section
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.queryByTestId('actions-section')).not.toBeInTheDocument()
  })

  it('applies custom className correctly', () => {
    const propsWithClassName = {
      ...mockProps,
      className: 'custom-card-class',
    }

    render(<ListCard {...propsWithClassName} />)

    const card = screen.getByText('John Doe').closest('[role="article"]')
    expect(card).toHaveClass('custom-card-class')
  })

  it('renders with proper accessibility attributes', () => {
    const accessibleProps = {
      ...mockProps,
      ariaLabel: 'John Doe profile card',
    }

    render(<ListCard {...accessibleProps} />)

    const card = screen.getByLabelText('John Doe profile card')
    expect(card).toBeInTheDocument()
    expect(card).toHaveAttribute('role', 'article')
  })

  it('handles long text content gracefully', () => {
    const longTextProps = {
      title: 'Very Long Title That Should Be Truncated Properly',
      subtitle: 'Very Long Subtitle That Should Also Be Truncated',
      description: 'Very long description that should wrap properly and not break the layout of the card component',
    }

    render(<ListCard {...longTextProps} />)

    expect(screen.getByText(longTextProps.title)).toBeInTheDocument()
    expect(screen.getByText(longTextProps.subtitle)).toBeInTheDocument()
    expect(screen.getByText(longTextProps.description)).toBeInTheDocument()
  })

  it('renders dropdown menu for actions when many actions provided', async () => {
    const manyActions = Array.from({ length: 5 }, (_, i) => ({
      icon: 'Edit' as const,
      label: `Action ${i + 1}`,
      onClick: jest.fn(),
    }))

    const propsWithManyActions = {
      ...mockProps,
      actions: manyActions,
    }

    const user = userEvent.setup()
    render(<ListCard {...propsWithManyActions} />)

    // Should show dropdown trigger when many actions
    const dropdownTrigger = screen.getByRole('button', { name: /more actions/i })
    expect(dropdownTrigger).toBeInTheDocument()

    // Open dropdown
    await user.click(dropdownTrigger)

    // Check that actions are in dropdown
    expect(screen.getByText('Action 1')).toBeInTheDocument()
    expect(screen.getByText('Action 5')).toBeInTheDocument()
  })
})
