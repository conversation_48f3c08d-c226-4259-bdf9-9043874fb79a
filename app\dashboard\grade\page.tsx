'use client';

import { Award, BarChart3, BookOpen, Plus, Search, TrendingUp, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Mock data for demonstration
const mockGradeStats = {
  totalStudents: 450,
  averageGrade: 85.2,
  passRate: 92,
  topPerformers: 45,
};

const mockGradeRecords = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: 'Grade 10',
    subject: 'Mathematics',
    grade: 'A',
    score: 92,
    maxScore: 100,
    date: '2024-01-15',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    class: 'Grade 9',
    subject: 'Science',
    grade: 'B+',
    score: 87,
    maxScore: 100,
    date: '2024-01-14',
  },
  {
    id: 3,
    studentName: '<PERSON>',
    studentId: 'STU003',
    class: 'Grade 11',
    subject: 'English',
    grade: 'A-',
    score: 89,
    maxScore: 100,
    date: '2024-01-13',
  },
];

export default function GradePage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('All');

  const handleAddGrade = () => {
    router.push('/dashboard/grade/create');
  };

  const handleViewGrade = (gradeId: number) => {
    router.push(`/dashboard/grade/${gradeId}`);
  };

  const handleEditGrade = (gradeId: number) => {
    router.push(`/dashboard/grade/${gradeId}/edit`);
  };

  const statsCards = [
    {
      title: 'Total Students',
      value: mockGradeStats.totalStudents.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Average Grade',
      value: `${mockGradeStats.averageGrade}%`,
      icon: BarChart3,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2.3%',
      changeType: 'positive' as const,
    },
    {
      title: 'Pass Rate',
      value: `${mockGradeStats.passRate}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+1.5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Top Performers',
      value: mockGradeStats.topPerformers.toString(),
      icon: Award,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
  ];

  const filteredRecords = mockGradeRecords.filter(record => {
    const matchesSearch =
      record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = subjectFilter === 'All' || record.subject === subjectFilter;
    return matchesSearch && matchesSubject;
  });

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) {
      return 'text-green-600 bg-green-50';
    }
    if (grade.startsWith('B')) {
      return 'text-blue-600 bg-blue-50';
    }
    if (grade.startsWith('C')) {
      return 'text-yellow-600 bg-yellow-50';
    }
    return 'text-red-600 bg-red-50';
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <PageHeader
        title='Grade Management'
        description='Manage student grades and academic performance'
        icon={Award}
        badge={{ label: 'Demo Data', variant: 'outline' }}
        actions={[
          {
            label: 'Add Grade',
            icon: Plus,
            onClick: handleAddGrade,
          },
        ]}
      />

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={subjectFilter} onValueChange={setSubjectFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select subject' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Subjects</SelectItem>
                <SelectItem value='Mathematics'>Mathematics</SelectItem>
                <SelectItem value='Science'>Science</SelectItem>
                <SelectItem value='English'>English</SelectItem>
                <SelectItem value='History'>History</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex items-center text-sm text-muted-foreground'>
              Showing {filteredRecords.length} of {mockGradeRecords.length} records
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grade Records */}
      <div className='grid grid-cols-1 gap-4'>
        {filteredRecords.map(record => (
          <Card key={record.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
                <div className='flex items-center space-x-4'>
                  <div className='w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center'>
                    <BookOpen className='w-6 h-6 text-purple-600' />
                  </div>
                  <div>
                    <h3 className='font-semibold'>{record.studentName}</h3>
                    <p className='text-sm text-muted-foreground'>
                      {record.studentId} • {record.class}
                    </p>
                  </div>
                </div>

                <div className='flex flex-col sm:flex-row sm:items-center gap-4'>
                  <div className='text-center sm:text-right'>
                    <p className='font-semibold'>{record.subject}</p>
                    <p className='text-sm text-muted-foreground'>{record.date}</p>
                  </div>

                  <div className='text-center sm:text-right'>
                    <div
                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getGradeColor(
                        record.grade
                      )}`}
                    >
                      {record.grade}
                    </div>
                    <p className='text-xs text-muted-foreground mt-1'>
                      {record.score}/{record.maxScore}
                    </p>
                  </div>

                  <div className='flex space-x-2'>
                    <Button variant='outline' size='sm' onClick={() => handleViewGrade(record.id)}>
                      View
                    </Button>
                    <Button variant='outline' size='sm' onClick={() => handleEditGrade(record.id)}>
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredRecords.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>🎯</div>
            <h3 className='text-lg font-semibold mb-2'>No Grade Records Found</h3>
            <p className='text-muted-foreground text-center mb-4'>
              {searchTerm || subjectFilter !== 'All'
                ? 'No grade records match your current filters.'
                : 'There are no grade records in the system yet.'}
            </p>
            <Button onClick={handleAddGrade}>
              <Plus className='mr-2 h-4 w-4' />
              Add First Grade Record
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
