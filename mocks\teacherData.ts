/**
 * Teacher Mock Data
 *
 * Comprehensive dummy data for teacher management including:
 * - Realistic teacher profiles
 * - Various departments and specializations
 * - Different experience levels and qualifications
 * - Complete contact information
 * - Professional certifications
 * - Emergency contacts
 */

import { Teacher } from '@/types';

export const teacherMockData: Teacher[] = [
  {
    id: 'teacher_001',
    teacher_id: 'TCH0001',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0101',
    date_of_birth: '1985-03-15',
    gender: 'FEMALE',
    address: {
      street: '123 Oak Street',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62701',
      country: 'United States',
    },
    department: 'Mathematics',
    subject_specialization: ['Algebra', 'Geometry', 'Calculus'],
    qualification: "Master's Degree",
    experience_years: 8,
    hire_date: '2016-08-15',
    salary: 65000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: '<PERSON>',
      relationship: 'Spouse',
      phone: '******-0102',
      email: 'micha<PERSON>.<EMAIL>',
    },
    certifications: [
      {
        name: 'Mathematics Teaching Certificate',
        issuer: 'State Board of Education',
        issue_date: '2016-06-01',
        expiry_date: '2026-06-01',
        certificate_id: 'MATH-2016-001',
      },
      {
        name: 'Advanced Calculus Certification',
        issuer: 'National Math Teachers Association',
        issue_date: '2019-09-15',
        expiry_date: '2029-09-15',
        certificate_id: 'CALC-2019-045',
      },
    ],
    created_at: '2016-08-15T09:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
  },
  {
    id: 'teacher_002',
    teacher_id: 'TCH0002',
    first_name: 'David',
    last_name: 'Chen',
    email: '<EMAIL>',
    phone: '******-0201',
    date_of_birth: '1982-07-22',
    gender: 'MALE',
    address: {
      street: '456 Pine Avenue',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62702',
      country: 'United States',
    },
    department: 'Science',
    subject_specialization: ['Physics', 'Chemistry', 'Environmental Science'],
    qualification: 'Doctorate',
    experience_years: 12,
    hire_date: '2012-01-10',
    salary: 78000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Lisa Chen',
      relationship: 'Spouse',
      phone: '******-0202',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Science Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2012-01-01',
        expiry_date: '2027-01-01',
        certificate_id: 'SCI-2012-002',
      },
      {
        name: 'Laboratory Safety Certification',
        issuer: 'National Science Foundation',
        issue_date: '2020-03-10',
        expiry_date: '2025-03-10',
        certificate_id: 'LAB-2020-156',
      },
    ],
    created_at: '2012-01-10T08:00:00Z',
    updated_at: '2024-01-10T16:45:00Z',
  },
  {
    id: 'teacher_003',
    teacher_id: 'TCH0003',
    first_name: 'Emily',
    last_name: 'Rodriguez',
    email: '<EMAIL>',
    phone: '******-0301',
    date_of_birth: '1990-11-08',
    gender: 'FEMALE',
    address: {
      street: '789 Maple Drive',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62703',
      country: 'United States',
    },
    department: 'English',
    subject_specialization: ['Literature', 'Creative Writing', 'ESL'],
    qualification: "Master's Degree",
    experience_years: 5,
    hire_date: '2019-08-20',
    salary: 58000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Carlos Rodriguez',
      relationship: 'Brother',
      phone: '******-0302',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'English Teaching Certificate',
        issuer: 'State Board of Education',
        issue_date: '2019-06-15',
        expiry_date: '2029-06-15',
        certificate_id: 'ENG-2019-003',
      },
      {
        name: 'ESL Specialist Certification',
        issuer: 'TESOL International',
        issue_date: '2021-04-20',
        expiry_date: '2031-04-20',
        certificate_id: 'ESL-2021-089',
      },
    ],
    created_at: '2019-08-20T10:30:00Z',
    updated_at: '2024-01-08T11:20:00Z',
  },
  {
    id: 'teacher_004',
    teacher_id: 'TCH0004',
    first_name: 'Robert',
    last_name: 'Thompson',
    email: '<EMAIL>',
    phone: '******-0401',
    date_of_birth: '1978-05-12',
    gender: 'MALE',
    address: {
      street: '321 Elm Street',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62704',
      country: 'United States',
    },
    department: 'History',
    subject_specialization: ['World History', 'American History', 'Government'],
    qualification: "Master's Degree",
    experience_years: 15,
    hire_date: '2009-09-01',
    salary: 72000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Margaret Thompson',
      relationship: 'Spouse',
      phone: '******-0402',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Social Studies Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2009-08-01',
        expiry_date: '2029-08-01',
        certificate_id: 'SOC-2009-004',
      },
    ],
    created_at: '2009-09-01T07:45:00Z',
    updated_at: '2024-01-12T13:15:00Z',
  },
  {
    id: 'teacher_005',
    teacher_id: 'TCH0005',
    first_name: 'Maria',
    last_name: 'Garcia',
    email: '<EMAIL>',
    phone: '******-0501',
    date_of_birth: '1987-09-25',
    gender: 'FEMALE',
    address: {
      street: '654 Cedar Lane',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62705',
      country: 'United States',
    },
    department: 'Physical Education',
    subject_specialization: ['Physical Education', 'Health', 'Sports Medicine'],
    qualification: "Bachelor's Degree",
    experience_years: 7,
    hire_date: '2017-08-15',
    salary: 55000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Jose Garcia',
      relationship: 'Father',
      phone: '******-0502',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Physical Education Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2017-06-01',
        expiry_date: '2027-06-01',
        certificate_id: 'PE-2017-005',
      },
      {
        name: 'CPR/First Aid Certification',
        issuer: 'American Red Cross',
        issue_date: '2023-01-15',
        expiry_date: '2025-01-15',
        certificate_id: 'CPR-2023-789',
      },
    ],
    created_at: '2017-08-15T09:15:00Z',
    updated_at: '2024-01-05T10:30:00Z',
  },
  {
    id: 'teacher_006',
    teacher_id: 'TCH0006',
    first_name: 'James',
    last_name: 'Wilson',
    email: '<EMAIL>',
    phone: '******-0601',
    date_of_birth: '1983-12-03',
    gender: 'MALE',
    address: {
      street: '987 Birch Road',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62706',
      country: 'United States',
    },
    department: 'Computer Science',
    subject_specialization: ['Programming', 'Web Development', 'Database Management'],
    qualification: "Master's Degree",
    experience_years: 10,
    hire_date: '2014-01-15',
    salary: 70000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/2182975/pexels-photo-2182975.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Jennifer Wilson',
      relationship: 'Spouse',
      phone: '******-0602',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Computer Science Teaching Certificate',
        issuer: 'State Board of Education',
        issue_date: '2014-01-01',
        expiry_date: '2029-01-01',
        certificate_id: 'CS-2014-006',
      },
      {
        name: 'Google Certified Educator',
        issuer: 'Google for Education',
        issue_date: '2022-05-10',
        expiry_date: '2032-05-10',
        certificate_id: 'GCE-2022-456',
      },
    ],
    created_at: '2014-01-15T08:30:00Z',
    updated_at: '2024-01-03T15:45:00Z',
  },
  {
    id: 'teacher_007',
    teacher_id: 'TCH0007',
    first_name: 'Lisa',
    last_name: 'Anderson',
    email: '<EMAIL>',
    phone: '******-0701',
    date_of_birth: '1992-04-18',
    gender: 'FEMALE',
    address: {
      street: '147 Spruce Street',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62707',
      country: 'United States',
    },
    department: 'Art',
    subject_specialization: ['Visual Arts', 'Digital Design', 'Art History'],
    qualification: "Master's Degree",
    experience_years: 3,
    hire_date: '2021-08-25',
    salary: 52000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Patricia Anderson',
      relationship: 'Mother',
      phone: '******-0702',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Art Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2021-06-15',
        expiry_date: '2031-06-15',
        certificate_id: 'ART-2021-007',
      },
    ],
    created_at: '2021-08-25T09:45:00Z',
    updated_at: '2024-01-07T12:00:00Z',
  },
  {
    id: 'teacher_008',
    teacher_id: 'TCH0008',
    first_name: 'Michael',
    last_name: 'Brown',
    email: '<EMAIL>',
    phone: '******-0801',
    date_of_birth: '1980-01-30',
    gender: 'MALE',
    address: {
      street: '258 Willow Avenue',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62708',
      country: 'United States',
    },
    department: 'Music',
    subject_specialization: ['Band', 'Orchestra', 'Music Theory'],
    qualification: "Master's Degree",
    experience_years: 13,
    hire_date: '2011-09-01',
    salary: 68000,
    status: 'ON_LEAVE',
    profile_picture:
      'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Susan Brown',
      relationship: 'Spouse',
      phone: '******-0802',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Music Education License',
        issuer: 'State Board of Education',
        issue_date: '2011-08-01',
        expiry_date: '2026-08-01',
        certificate_id: 'MUS-2011-008',
      },
    ],
    created_at: '2011-09-01T08:00:00Z',
    updated_at: '2023-12-15T14:20:00Z',
  },
  {
    id: 'teacher_009',
    teacher_id: 'TCH0009',
    first_name: 'Jennifer',
    last_name: 'Davis',
    email: '<EMAIL>',
    phone: '******-0901',
    date_of_birth: '1988-06-14',
    gender: 'FEMALE',
    address: {
      street: '369 Poplar Street',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62709',
      country: 'United States',
    },
    department: 'Foreign Language',
    subject_specialization: ['Spanish', 'French', 'Latin'],
    qualification: "Master's Degree",
    experience_years: 6,
    hire_date: '2018-08-20',
    salary: 60000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Robert Davis',
      relationship: 'Spouse',
      phone: '******-0902',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Foreign Language Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2018-06-01',
        expiry_date: '2028-06-01',
        certificate_id: 'FL-2018-009',
      },
      {
        name: 'Spanish Proficiency Certificate',
        issuer: 'Instituto Cervantes',
        issue_date: '2020-07-15',
        expiry_date: '2030-07-15',
        certificate_id: 'ESP-2020-234',
      },
    ],
    created_at: '2018-08-20T10:00:00Z',
    updated_at: '2024-01-09T16:30:00Z',
  },
  {
    id: 'teacher_010',
    teacher_id: 'TCH0010',
    first_name: 'Christopher',
    last_name: 'Miller',
    email: '<EMAIL>',
    phone: '******-1001',
    date_of_birth: '1975-10-05',
    gender: 'MALE',
    address: {
      street: '741 Hickory Lane',
      city: 'Springfield',
      state: 'Illinois',
      postal_code: '62710',
      country: 'United States',
    },
    department: 'Mathematics',
    subject_specialization: ['Statistics', 'Pre-Calculus', 'AP Mathematics'],
    qualification: "Master's Degree",
    experience_years: 18,
    hire_date: '2006-08-15',
    salary: 75000,
    status: 'ACTIVE',
    profile_picture:
      'https://images.pexels.com/photos/2182976/pexels-photo-2182976.jpeg?auto=compress&cs=tinysrgb&w=400',
    emergency_contact: {
      name: 'Linda Miller',
      relationship: 'Spouse',
      phone: '******-1002',
      email: '<EMAIL>',
    },
    certifications: [
      {
        name: 'Mathematics Teaching License',
        issuer: 'State Board of Education',
        issue_date: '2006-06-01',
        expiry_date: '2026-06-01',
        certificate_id: 'MATH-2006-010',
      },
      {
        name: 'AP Mathematics Certification',
        issuer: 'College Board',
        issue_date: '2015-08-20',
        expiry_date: '2025-08-20',
        certificate_id: 'AP-2015-567',
      },
    ],
    created_at: '2006-08-15T07:30:00Z',
    updated_at: '2024-01-11T09:45:00Z',
  },
];

// Helper functions for mock data
export const teacherMockHelpers = {
  // Get teachers by department
  getByDepartment: (department: string): Teacher[] => {
    return teacherMockData.filter(teacher => teacher.department === department);
  },

  // Get teachers by status
  getByStatus: (status: string): Teacher[] => {
    return teacherMockData.filter(teacher => teacher.status === status);
  },

  // Get teachers by experience range
  getByExperience: (minYears: number, maxYears: number): Teacher[] => {
    return teacherMockData.filter(
      teacher => teacher.experience_years >= minYears && teacher.experience_years <= maxYears
    );
  },

  // Search teachers
  search: (query: string): Teacher[] => {
    const searchTerm = query.toLowerCase();
    return teacherMockData.filter(
      teacher =>
        teacher.first_name.toLowerCase().includes(searchTerm) ||
        teacher.last_name.toLowerCase().includes(searchTerm) ||
        teacher.email.toLowerCase().includes(searchTerm) ||
        teacher.department.toLowerCase().includes(searchTerm) ||
        teacher.subject_specialization.some((subject: string) =>
          subject.toLowerCase().includes(searchTerm)
        )
    );
  },

  // Get department statistics
  getDepartmentStats: () => {
    const stats: Record<string, number> = {};
    teacherMockData.forEach(teacher => {
      stats[teacher.department] = (stats[teacher.department] || 0) + 1;
    });
    return stats;
  },

  // Get experience statistics
  getExperienceStats: () => {
    const junior = teacherMockData.filter(t => t.experience_years < 3).length;
    const mid = teacherMockData.filter(
      t => t.experience_years >= 3 && t.experience_years <= 7
    ).length;
    const senior = teacherMockData.filter(t => t.experience_years > 7).length;

    return { junior, mid, senior };
  },
};

export default teacherMockData;
