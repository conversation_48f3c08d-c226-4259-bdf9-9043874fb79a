import Link from "next/link";

/**
 * Global 404 Not Found Page
 * 
 * This page is shown when a route doesn't exist
 * Provides helpful navigation options to get users back on track
 */
export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        {/* 404 illustration */}
        <div className="mx-auto h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
          <span className="text-gray-400 text-4xl">📚</span>
        </div>
        
        {/* 404 message */}
        <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          Page Not Found
        </h2>
        
        <p className="text-gray-600 mb-8">
          The page you're looking for doesn't exist or has been moved. 
          Let's get you back to where you need to be.
        </p>
        
        {/* Navigation options */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link
            href="/"
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Home
          </Link>
          
          <Link
            href="/dashboard"
            className="px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Dashboard
          </Link>
        </div>
        
        {/* Quick links */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-3">Quick Links:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/dashboard/students" className="text-blue-600 hover:underline">
              Students
            </Link>
            <Link href="/dashboard/teachers" className="text-blue-600 hover:underline">
              Teachers
            </Link>
            <Link href="/dashboard/classes" className="text-blue-600 hover:underline">
              Classes
            </Link>
            <Link href="/dashboard/attendance" className="text-blue-600 hover:underline">
              Attendance
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
