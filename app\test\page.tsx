"use client";

import { useEffect, useState } from "react";
import { api } from "@/api/apiClient";

export default function TestPage() {
  const [status, setStatus] = useState<string>("Testing...");
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log("🧪 Testing API connectivity...");
    console.log("📍 Current URL:", window.location.href);
    console.log("🔗 API Base URL:", "/api");
    
    // Test the students endpoint
    api.get("/students/")
      .then((res) => {
        console.log("✅ Students fetched:", res.data);
        setStatus("✅ Success!");
        setData(res.data);
        setError(null);
      })
      .catch((err) => {
        console.error("❌ API error:", err);
        setStatus("❌ Failed!");
        setError(err.message);
        setData(null);
      });
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">🧪 API Connectivity Test</h1>
      
      <div className="mb-4">
        <strong>Status:</strong> {status}
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {data && (
        <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <strong>Data:</strong> 
          <pre className="mt-2 text-sm overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}

      <div className="text-sm text-gray-600">
        <p><strong>Expected:</strong> Request to /api/students/ should be proxied to http://localhost:8000/api/v1/students/</p>
        <p><strong>Check Console:</strong> Look for [API ▶] and [API ✓] logs</p>
        <p><strong>Check Network Tab:</strong> Should see request to /api/students/ with 200 status</p>
      </div>
    </div>
  );
}
