/**
 * Authentication Hook
 *
 * Provides easy access to auth state and actions in components
 * Built on top of Zustand auth store
 */

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import {
  useAuth as useAuthStore,
  useAuthError as useStoreAuthError,
  useAuthLoading as useStoreAuthLoading,
  useAuth<PERSON>ser as useStoreAuthUser,
} from '../stores/authStore';

export const useAuth = () => {
  const auth = useAuthStore();
  const router = useRouter();

  // Auto-check token expiry on mount and periodically
  useEffect(() => {
    if (auth.isAuthenticated) {
      // Check immediately
      auth.checkTokenExpiry();

      // Set up periodic check (every 5 minutes)
      const interval = setInterval(() => {
        auth.checkTokenExpiry();
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }

    // Return empty cleanup function for non-authenticated case
    return () => {};
  }, [auth.isAuthenticated, auth.checkTokenExpiry]);

  // Enhanced login with navigation
  const loginWithRedirect = async (
    credentials: Parameters<typeof auth.login>[0],
    redirectTo?: string
  ) => {
    try {
      await auth.login(credentials);
      router.push(redirectTo || '/dashboard');
    } catch (error) {
      // Error is already handled by the store
      throw error;
    }
  };

  // Enhanced logout with navigation
  const logoutWithRedirect = (redirectTo?: string) => {
    auth.logout();
    router.push(redirectTo || '/login');
  };

  return {
    ...auth,
    loginWithRedirect,
    logoutWithRedirect,
    // Computed values
    isAdmin: auth.user?.role === 'ADMIN',
    isTeacher: auth.user?.role === 'TEACHER',
    isStudent: auth.user?.role === 'STUDENT',
  };
};

// Specialized hooks for better performance
export const useAuthUser = () => useStoreAuthUser();
export const useAuthLoading = () => useStoreAuthLoading();
export const useAuthError = () => useStoreAuthError();

// Hook for protected routes
export const useRequireAuth = (redirectTo = '/login') => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
};

// Hook for guest-only routes (login, register)
export const useRequireGuest = (redirectTo = '/dashboard') => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
};
