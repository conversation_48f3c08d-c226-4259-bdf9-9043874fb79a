{"name": "school-management-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "dev:webpack": "next dev", "dev:debug": "NODE_OPTIONS='--inspect' next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint --max-warnings 0", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "type-check:watch": "tsc --noEmit --skipLib<PERSON><PERSON><PERSON> --watch", "format": "prettier --write .", "format:check": "prettier --check .", "quality": "node scripts/code-quality-check.js", "quality:fix": "npm run lint:fix && npm run format", "quality:check": "npm run type-check && npm run lint:strict && npm run format:check", "pre-commit": "npm run quality:check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "clean": "rm -rf .next out node_modules/.cache", "clean:all": "npm run clean && rm -rf node_modules package-lock.json", "reinstall": "npm run clean:all && npm install", "update-deps": "npx npm-check-updates -u && npm install", "security-audit": "npm audit --audit-level moderate", "bundle-analyzer": "ANALYZE=true npm run build", "export": "next export", "setup": "node scripts/setup-dev.js", "postinstall": "npm run type-check"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.7", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.20.5", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "html2pdf.js": "^0.10.3", "jose": "^6.0.11", "lucide-react": "^0.452.0", "next": "^14.2.31", "next-themes": "^0.4.6", "react": "^18.3.1", "react-big-calendar": "^1.13.2", "react-calendar": "^5.0.0", "react-csv": "^2.2.2", "react-day-picker": "^9.2.2", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.4", "sonner": "^1.7.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.14", "@types/node": "^20.16.10", "@types/react": "^18.3.12", "@types/react-big-calendar": "^1.8.9", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "typescript": "^5.6.2"}}