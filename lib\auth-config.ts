/**
 * Auth Configuration and Namespace Management
 *
 * Handles the two possible backend namespaces:
 * - Mode A: /api/v1/auth/*
 * - Mode B: /api/v1/users/auth/*
 */

// Get the auth namespace from environment
const AUTH_NAMESPACE = process.env.NEXT_PUBLIC_AUTH_NAMESPACE || 'users';
const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';
const API_VERSION = process.env.NEXT_PUBLIC_API_VERSION || 'v1';

export const AUTH_CONFIG = {
  namespace: AUTH_NAMESPACE,
  apiBase: API_BASE,
  apiVersion: API_VERSION,
} as const;

/**
 * Build API path based on the configured namespace
 * Note: API client now uses /api/v1 as baseURL, so we only need the endpoint path
 */
export function buildAuthPath(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

  // <PERSON>le special case for no prefix
  if (AUTH_NAMESPACE === 'none') {
    return `/${cleanEndpoint}`;
  }

  // Handle standard namespaces - no /api/v1 prefix since apiClient handles it
  if (AUTH_NAMESPACE === 'auth') {
    return `/auth/${cleanEndpoint}`;
  } else {
    return `/users/auth/${cleanEndpoint}`;
  }
}

/**
 * Build admin users path (always under users namespace)
 * Note: API client now uses /api/v1 as baseURL, so we only need the endpoint path
 */
export function buildUsersPath(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

  // Handle special case for no prefix
  if (AUTH_NAMESPACE === 'none') {
    return `/admin/${cleanEndpoint}`;
  }

  return `/users/admin/${cleanEndpoint}`;
}

/**
 * Get full backend URL for a path
 */
export function getBackendUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${API_BASE}${cleanPath}`;
}

/**
 * Auth endpoints configuration
 */
export const AUTH_ENDPOINTS = {
  login: buildAuthPath('login'),
  logout: buildAuthPath('logout'),
  me: buildAuthPath('me'),
  updateMe: buildAuthPath('me'),
  changePassword: buildAuthPath('change-password'),
  register: buildAuthPath('register'), // Only available in users namespace
} as const;

/**
 * Users admin endpoints configuration
 */
export const USERS_ENDPOINTS = {
  list: buildUsersPath('users'),
  get: (id: string) => buildUsersPath(`users/${id}`),
  update: (id: string) => buildUsersPath(`users/${id}`),
  deactivate: (id: string) => buildUsersPath(`users/${id}/deactivate`),
  activate: (id: string) => buildUsersPath(`users/${id}/activate`),
  stats: buildUsersPath('stats/users'),
} as const;

/**
 * Detect which namespace is available by testing endpoints
 */
export async function detectAuthNamespace(): Promise<{
  namespace: 'auth' | 'users' | 'none' | null;
  details: Array<{ namespace: string; url: string; status: number | string; response?: any }>;
}> {
  const testEndpoints = [
    { namespace: 'none', url: getBackendUrl('/me') },
    { namespace: 'auth', url: getBackendUrl('/api/v1/auth/me') },
    { namespace: 'users', url: getBackendUrl('/api/v1/users/auth/me') },
    // Also test without version prefix in case backend doesn't use v1
    { namespace: 'auth-no-version', url: getBackendUrl('/api/auth/me') },
    { namespace: 'users-no-version', url: getBackendUrl('/api/users/auth/me') },
  ];

  const details: Array<{
    namespace: string;
    url: string;
    status: number | string;
    response?: any;
  }> = [];
  let detectedNamespace: 'auth' | 'users' | 'none' | null = null;

  for (const { namespace, url } of testEndpoints) {
    try {
      console.log(`🔍 Testing ${namespace} namespace: ${url}`);
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      });

      let responseData: any = null;
      try {
        responseData = await response.json();
      } catch (e) {
        responseData = await response.text();
      }

      details.push({
        namespace,
        url,
        status: response.status,
        response: responseData,
      });

      console.log(`📊 ${namespace} namespace result:`, {
        status: response.status,
        url,
        response: responseData,
      });

      // We expect 401 for unauthenticated requests, not 404
      if (response.status === 401) {
        // Map the detected namespace to the standard format
        if (namespace === 'none') {
          detectedNamespace = 'none' as any;
        } else if (namespace.startsWith('auth')) {
          detectedNamespace = 'auth';
        } else if (namespace.startsWith('users')) {
          detectedNamespace = 'users';
        }
        console.log(`✅ Detected working namespace: ${namespace} -> ${detectedNamespace}`);
        break;
      }
    } catch (error) {
      details.push({
        namespace,
        url,
        status: 'NETWORK_ERROR',
        response: String(error),
      });
      console.error(`❌ Network error testing ${namespace}:`, error);
    }
  }

  return {
    namespace: detectedNamespace,
    details,
  };
}

/**
 * Validate current namespace configuration
 */
export async function validateNamespaceConfig(): Promise<{
  isValid: boolean;
  detectedNamespace: 'auth' | 'users' | 'none' | null;
  currentNamespace: string;
  suggestion?: string;
  details: Array<{ namespace: string; url: string; status: number | string; response?: any }>;
}> {
  const detection = await detectAuthNamespace();
  const currentNamespace = AUTH_NAMESPACE;

  const isValid = detection.namespace === currentNamespace;

  let suggestion: string | undefined;
  if (!isValid && detection.namespace) {
    suggestion = `Set NEXT_PUBLIC_AUTH_NAMESPACE=${detection.namespace} in your .env.local file`;
  } else if (!detection.namespace) {
    suggestion =
      'Backend may not be running or endpoints are not available. Check the details below.';
  }

  return {
    isValid,
    detectedNamespace: detection.namespace,
    currentNamespace,
    suggestion,
    details: detection.details,
  };
}
