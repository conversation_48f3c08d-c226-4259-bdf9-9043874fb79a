'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Construction } from 'lucide-react';

interface PlaceholderFormProps {
  title: string;
  description: string;
}

/**
 * PlaceholderForm Component
 * 
 * Temporary placeholder for settings forms that are not yet implemented.
 * Shows a professional "coming soon" message with consistent styling.
 */
export function PlaceholderForm({ title, description }: PlaceholderFormProps) {
  return (
    <div className="p-6">
      <Card className="border-dashed border-2 border-slate-200">
        <CardContent className="p-12 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-4 bg-slate-100 rounded-full">
                <Construction className="h-8 w-8 text-slate-500" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
              <p className="text-sm text-slate-600 mt-1">{description}</p>
            </div>
            
            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
              Coming Soon
            </Badge>
            
            <p className="text-xs text-slate-500 max-w-md mx-auto">
              This settings section is currently under development. 
              The form will be available in a future update with full validation and functionality.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
