/**
 * Session Logout Route Handler
 * 
 * Clears httpOnly cookie and optionally calls backend logout
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getBackendUrl, AUTH_ENDPOINTS } from '@/lib/auth-config';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authToken = cookieStore.get('auth_token')?.value;
    
    // Call backend logout if token exists
    if (authToken) {
      try {
        await fetch(getBackendUrl(AUTH_ENDPOINTS.logout), {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        });
      } catch (error) {
        // Backend logout failed, but we still clear the cookie
        console.warn('Backend logout failed:', error);
      }
    }
    
    // Clear the auth cookie
    cookieStore.delete('auth_token');
    
    return new NextResponse(null, { status: 204 });
    
  } catch (error) {
    console.error('Session logout error:', error);
    
    // Still try to clear the cookie even if there's an error
    const cookieStore = cookies();
    cookieStore.delete('auth_token');
    
    return NextResponse.json(
      { detail: 'Logout completed with warnings' },
      { status: 200 }
    );
  }
}
