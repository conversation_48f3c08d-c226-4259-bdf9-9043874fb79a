'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Check,
  Cloud,
  File,
  Image,
  Plus,
  Upload,
  Video,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface MediaFormData {
  title: string;
  description: string;
  category: string;
  tags: string;
  visibility: string;
  allowDownload: boolean;
  file: File | null;
  fileName: string;
  fileSize: string;
  fileType: string;
}

const initialFormData: MediaFormData = {
  title: '',
  description: '',
  category: '',
  tags: '',
  visibility: 'public',
  allowDownload: true,
  file: null,
  fileName: '',
  fileSize: '',
  fileType: '',
};

const categories = [
  'Images',
  'Videos',
  'Documents',
  'Audio',
  'Presentations',
  'Assignments',
  'Resources',
  'Other',
];

export default function CreateMediaPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<MediaFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (field: keyof MediaFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileSelect = (file: File) => {
    const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
    setFormData(prev => ({
      ...prev,
      file,
      fileName: file.name,
      fileSize: `${sizeInMB} MB`,
      fileType: file.type,
      title: prev.title || file.name.split('.')[0],
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      console.log('Submitting media data:', formData);
      
      // Simulate file upload
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Success - redirect to media list
      router.push('/dashboard/media');
    } catch (error) {
      console.error('Error uploading media:', error);
      // TODO: Show error toast
    } finally {
      setIsSubmitting(false);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="w-8 h-8 text-blue-600" />;
    if (fileType.startsWith('video/')) return <Video className="w-8 h-8 text-purple-600" />;
    return <File className="w-8 h-8 text-gray-600" />;
  };

  const getFileTypeColor = (fileType: string) => {
    if (fileType.startsWith('image/')) return 'text-blue-600 bg-blue-50';
    if (fileType.startsWith('video/')) return 'text-purple-600 bg-purple-50';
    if (fileType.startsWith('audio/')) return 'text-green-600 bg-green-50';
    if (fileType.includes('pdf')) return 'text-red-600 bg-red-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/media">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Media
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center">
              <Plus className="w-8 h-8 mr-3 text-blue-600" />
              Upload Media
            </h1>
            <p className="text-muted-foreground mt-1">
              Upload and organize your media files
            </p>
          </div>
        </div>
      </div>

      {/* File Upload Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="w-5 h-5 mr-2 text-blue-600" />
            File Upload
          </CardTitle>
          <CardDescription>
            Select or drag and drop your file to upload
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {formData.file ? (
              <div className="space-y-4">
                <div className="flex items-center justify-center">
                  {getFileIcon(formData.fileType)}
                </div>
                <div>
                  <p className="font-medium">{formData.fileName}</p>
                  <p className="text-sm text-muted-foreground">{formData.fileSize}</p>
                  <Badge variant="outline" className={getFileTypeColor(formData.fileType)}>
                    {formData.fileType}
                  </Badge>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setFormData(prev => ({ ...prev, file: null, fileName: '', fileSize: '', fileType: '' }))}
                >
                  Remove File
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Cloud className="w-16 h-16 mx-auto text-gray-400" />
                <div>
                  <p className="text-lg font-medium">Drop your file here</p>
                  <p className="text-muted-foreground">or click to browse</p>
                </div>
                <Input
                  type="file"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
                />
                <Label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer">
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </Button>
                </Label>
                <p className="text-xs text-muted-foreground">
                  Supported formats: Images, Videos, Audio, PDF, Documents
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Media Information Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <File className="w-5 h-5 mr-2 text-green-600" />
            Media Information
          </CardTitle>
          <CardDescription>
            Provide details about your media file
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter media title"
                required
              />
            </div>
            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your media file..."
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                placeholder="tag1, tag2, tag3"
              />
            </div>
            <div>
              <Label htmlFor="visibility">Visibility</Label>
              <Select value={formData.visibility} onValueChange={(value) => handleInputChange('visibility', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">Public</SelectItem>
                  <SelectItem value="private">Private</SelectItem>
                  <SelectItem value="restricted">Restricted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="allowDownload"
                checked={formData.allowDownload}
                onChange={(e) => handleInputChange('allowDownload', e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="allowDownload">Allow downloads</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.file || !formData.title}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Uploading Media...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              Upload Media
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
