/**
 * Student Service
 *
 * Handles all student-related API calls:
 * - CRUD operations
 * - Filtering and pagination
 * - Statistics and reports
 * - Bulk operations
 */

import type {
  Student,
  StudentCreate,
  StudentFilters,
  StudentImportResponse,
  StudentListResponse,
  StudentPhotoResponse,
  StudentStats,
  StudentToggleResponse,
  StudentUpdate,
} from '../../types';
import { api } from '../apiClient';
import { validateStudentCreate, validateStudentUpdate, checkForPotentialDuplicates } from '../../lib/validations';

export interface StudentQuery extends StudentFilters {
  page?: number;
  size?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class StudentService {
  private static readonly BASE_URL = '/students';

  /**
   * Get all students with filtering and pagination
   */
  static async getStudents(query: StudentQuery = {}): Promise<StudentListResponse> {
    const params = new URLSearchParams();

    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await api.get<any>(url);

    // Transform backend response to frontend format
    const backendData = response.data;
    const transformedItems = (backendData.items || []).map((backendStudent: any) => ({
      id: backendStudent.id,
      reg_no: backendStudent.reg_no || backendStudent.admission_number,
      first_name: backendStudent.first_name || backendStudent.name,
      last_name: backendStudent.last_name || backendStudent.surname,
      gender: backendStudent.gender || backendStudent.sex,
      dob: backendStudent.dob || backendStudent.date_of_birth,
      class_id: String(backendStudent.class_id),
      section_id: String(backendStudent.section_id || backendStudent.grade_id),
      guardian_name: backendStudent.guardian_name,
      guardian_phone: backendStudent.guardian_phone,
      address: backendStudent.address,
      email: backendStudent.email,
      photo_url: backendStudent.photo_url || backendStudent.img,
      is_active: backendStudent.is_active !== false,
      created_at: backendStudent.created_at || new Date().toISOString(),
    }));

    return {
      items: transformedItems,
      page: backendData.page || 1,
      size: backendData.size || 10,
      total: backendData.total || 0,
      pages: backendData.pages || 1,
    };
  }

  /**
   * Get student by ID
   */
  static async getStudent(id: string): Promise<Student> {
    const response = await api.get<any>(`${this.BASE_URL}/${id}`);

    // Transform backend response to frontend format
    const backendStudent = response.data;
    return {
      id: backendStudent.id,
      reg_no: backendStudent.reg_no || backendStudent.admission_number,
      first_name: backendStudent.first_name || backendStudent.name,
      last_name: backendStudent.last_name || backendStudent.surname,
      gender: backendStudent.gender || backendStudent.sex,
      dob: backendStudent.dob || backendStudent.date_of_birth,
      class_id: String(backendStudent.class_id),
      section_id: String(backendStudent.section_id || backendStudent.grade_id),
      guardian_name: backendStudent.guardian_name,
      guardian_phone: backendStudent.guardian_phone,
      address: backendStudent.address,
      email: backendStudent.email,
      photo_url: backendStudent.photo_url || backendStudent.img,
      is_active: backendStudent.is_active !== false,
      created_at: backendStudent.created_at || new Date().toISOString(),
    };
  }

  /**
   * Create new student
   */
  static async createStudent(data: StudentCreate): Promise<Student> {
    // Comprehensive client-side validation
    const validation = validateStudentCreate(data);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Check for potential duplicates and warn user
    const duplicateWarnings = checkForPotentialDuplicates(data);
    if (duplicateWarnings.length > 0) {
      console.warn('[StudentService] Potential duplicate warnings:', duplicateWarnings);
    }

    // Log validation warnings if any
    if (validation.warnings.length > 0) {
      console.warn('[StudentService] Validation warnings:', validation.warnings);
    }

    // Transform frontend schema to backend schema (exact mapping)
    const backendData = {
      username: data.reg_no, // Use reg_no as username
      admission_number: data.reg_no,
      name: data.first_name,
      surname: data.last_name,
      sex: data.gender?.toUpperCase() || 'OTHER',
      date_of_birth: data.dob || new Date().toISOString().split('T')[0],
      class_id: parseInt(data.class_id),
      grade_id: parseInt(data.section_id),
      guardian_name: data.guardian_name || '',
      guardian_phone: data.guardian_phone || '',
      address: data.address || '',
      email: data.email || null,
      password: data.password,
      parent_id: data.parent_id,
      // Optional fields with proper defaults
      phone: null,
      img: null,
      blood_type: null,
      emergency_contact: null,
      medical_info: null,
      nationality: null,
      religion: null,
      roll_number: null,
      previous_school: null,
      is_active: true,
    };

    console.info('[StudentService] Transformed data for backend:', {
      ...backendData,
      password: '[REDACTED]' // Don't log passwords
    });

    try {
      const response = await api.post<any>(this.BASE_URL, backendData);

      // Transform backend response to frontend format
      const backendStudent = response.data;
      const frontendStudent: Student = {
        id: backendStudent.id,
        reg_no: backendStudent.reg_no || backendStudent.admission_number,
        first_name: backendStudent.first_name || backendStudent.name,
        last_name: backendStudent.last_name || backendStudent.surname,
        gender: (backendStudent.gender || backendStudent.sex)?.toLowerCase() || 'other',
        dob: backendStudent.dob || backendStudent.date_of_birth,
        class_id: String(backendStudent.class_id),
        section_id: String(backendStudent.section_id || backendStudent.grade_id),
        guardian_name: backendStudent.guardian_name,
        guardian_phone: backendStudent.guardian_phone,
        address: backendStudent.address,
        email: backendStudent.email,
        photo_url: backendStudent.photo_url || backendStudent.img,
        is_active: backendStudent.is_active !== false,
        created_at: backendStudent.created_at || new Date().toISOString(),
      };

      return frontendStudent;
    } catch (error: any) {
      // Enhanced error handling with specific error messages
      if (error.response?.status === 409) {
        const detail = error.response.data?.detail || 'Duplicate data found';
        if (detail.includes('admission number')) {
          throw new Error('A student with this registration number already exists');
        } else if (detail.includes('email')) {
          throw new Error('A student with this email already exists');
        } else if (detail.includes('username')) {
          throw new Error('A student with this username already exists');
        }
        throw new Error(detail);
      } else if (error.response?.status === 400) {
        const detail = error.response.data?.detail || 'Invalid data provided';
        throw new Error(detail);
      } else if (error.response?.status === 422) {
        const detail = error.response.data?.detail || 'Validation error';
        if (Array.isArray(detail)) {
          const errorMessages = detail.map((err: any) => `${err.loc?.join('.')}: ${err.msg}`).join(', ');
          throw new Error(`Validation errors: ${errorMessages}`);
        }
        throw new Error(detail);
      }
      throw error;
    }
  }

  /**
   * Update student
   */
  static async updateStudent(id: string, data: StudentUpdate): Promise<Student> {
    // Validate update data
    const validation = validateStudentUpdate(data);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Log validation warnings if any
    if (validation.warnings.length > 0) {
      console.warn('[StudentService] Update validation warnings:', validation.warnings);
    }

    try {
      const response = await api.put<Student>(`${this.BASE_URL}/${id}`, data);
      return response.data;
    } catch (error: any) {
      // Enhanced error handling for updates
      if (error.response?.status === 404) {
        throw new Error('Student not found');
      } else if (error.response?.status === 409) {
        const detail = error.response.data?.detail || 'Duplicate data found';
        throw new Error(detail);
      } else if (error.response?.status === 400) {
        const detail = error.response.data?.detail || 'Invalid data provided';
        throw new Error(detail);
      }
      throw error;
    }
  }

  /**
   * Toggle student active status
   */
  static async toggleStudent(id: string): Promise<StudentToggleResponse> {
    const response = await api.post<StudentToggleResponse>(`${this.BASE_URL}/${id}/toggle-active`);
    return response.data;
  }

  /**
   * Delete student (soft delete)
   */
  static async deleteStudent(id: string): Promise<void> {
    await api.delete<void>(`${this.BASE_URL}/${id}`);
  }

  /**
   * Upload student photo
   */
  static async uploadStudentPhoto(id: string, file: File): Promise<StudentPhotoResponse> {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post<StudentPhotoResponse>(
      `${this.BASE_URL}/${id}/photo`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  /**
   * Import students from CSV
   */
  static async importStudents(file: File): Promise<StudentImportResponse> {
    // Validate file before upload
    if (!file.name.toLowerCase().endsWith('.csv')) {
      throw new Error('Please select a CSV file');
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      throw new Error('File size must be less than 10MB');
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post<StudentImportResponse>(`${this.BASE_URL}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      // Enhanced error handling for CSV import
      if (error.response?.status === 400) {
        const detail = error.response.data?.detail || 'Invalid CSV file';
        throw new Error(detail);
      } else if (error.response?.status === 422) {
        throw new Error('CSV file format is invalid or contains validation errors');
      }
      throw error;
    }
  }

  /**
   * Download sample CSV file for import
   */
  static async downloadSampleCSV(): Promise<void> {
    try {
      const response = await api.get(`${this.BASE_URL}/import/sample`, {
        responseType: 'blob',
      });

      // Create download link
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'student_import_sample.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Failed to download sample CSV:', error);
      throw new Error('Failed to download sample CSV file');
    }
  }

  /**
   * Get student statistics
   */
  static async getStudentStats(): Promise<StudentStats> {
    const response = await api.get<StudentStats>(`${this.BASE_URL}/stats`);
    return response.data;
  }

  /**
   * Get students by grade
   */
  static async getStudentsByGrade(grade: string): Promise<Student[]> {
    const response = await api.get<Student[]>(`${this.BASE_URL}/grade/${grade}`);
    return response.data;
  }

  /**
   * Get students by class
   */
  static async getStudentsByClass(className: string): Promise<Student[]> {
    const response = await api.get<Student[]>(`${this.BASE_URL}/class/${className}`);
    return response.data;
  }

  /**
   * Search students
   */
  static async searchStudents(query: string): Promise<Student[]> {
    const response = await api.get<Student[]>(
      `${this.BASE_URL}/search?q=${encodeURIComponent(query)}`
    );
    return response.data;
  }

  /**
   * Get available grades
   */
  static async getGrades(): Promise<string[]> {
    const response = await api.get<string[]>(`${this.BASE_URL}/grades`);
    return response.data;
  }

  /**
   * Get available classes
   */
  static async getClasses(): Promise<string[]> {
    const response = await api.get<string[]>(`${this.BASE_URL}/classes`);
    return response.data;
  }

  /**
   * Export students data
   */
  static async exportStudents(
    format: 'csv' | 'excel' | 'pdf',
    filters?: StudentFilters
  ): Promise<void> {
    const params = new URLSearchParams({ format });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });
    }

    // Note: download method needs to be implemented in apiClient
    await api.get(`${this.BASE_URL}/export?${params.toString()}`);
  }

  /**
   * Get student's grades
   */
  static async getStudentGrades(studentId: string): Promise<
    Array<{
      id: string;
      subject: string;
      exam: string;
      marksObtained: number;
      totalMarks: number;
      percentage: number;
      grade: string;
      date: string;
    }>
  > {
    const response = await api.get<
      Array<{
        id: string;
        subject: string;
        exam: string;
        marksObtained: number;
        totalMarks: number;
        percentage: number;
        grade: string;
        date: string;
      }>
    >(`${this.BASE_URL}/${studentId}/grades`);
    return response.data;
  }

  /**
   * Get student's attendance
   */
  static async getStudentAttendance(
    studentId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<
    Array<{
      id: string;
      date: string;
      status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
      checkInTime?: string;
      checkOutTime?: string;
      notes?: string;
    }>
  > {
    const params = new URLSearchParams();
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const url = `${this.BASE_URL}/${studentId}/attendance${
      params.toString() ? `?${params.toString()}` : ''
    }`;
    const response = await api.get<
      Array<{
        id: string;
        date: string;
        status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
        checkInTime?: string;
        checkOutTime?: string;
        notes?: string;
      }>
    >(url);
    return response.data;
  }

  /**
   * Get student's fee records
   */
  static async getStudentFees(studentId: string): Promise<
    Array<{
      id: string;
      feeType: string;
      amount: number;
      dueDate: string;
      paidDate?: string;
      status: 'PAID' | 'PENDING' | 'OVERDUE';
      paymentMethod?: string;
    }>
  > {
    const response = await api.get<
      Array<{
        id: string;
        feeType: string;
        amount: number;
        dueDate: string;
        paidDate?: string;
        status: 'PAID' | 'PENDING' | 'OVERDUE';
        paymentMethod?: string;
      }>
    >(`${this.BASE_URL}/${studentId}/fees`);
    return response.data;
  }

  /**
   * Transfer student to another class
   */
  static async transferStudent(
    studentId: string,
    newGrade: string,
    newClass: string,
    transferDate: string,
    reason?: string
  ): Promise<Student> {
    const response = await api.post<Student>(`${this.BASE_URL}/${studentId}/transfer`, {
      newGrade,
      newClass,
      transferDate,
      reason,
    });
    return response.data;
  }

  /**
   * Graduate student
   */
  static async graduateStudent(
    studentId: string,
    graduationDate: string,
    finalGrade?: string
  ): Promise<Student> {
    const response = await api.post<Student>(`${this.BASE_URL}/${studentId}/graduate`, {
      graduationDate,
      finalGrade,
    });
    return response.data;
  }
}

// Export default instance
export const studentService = StudentService;
