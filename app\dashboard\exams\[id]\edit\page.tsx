'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, BookOpen, Save } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock exam data
const mockExams = [
  {
    id: 1,
    name: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'Grade 10',
    date: '2024-03-15',
    time: '09:00',
    duration: '2',
    totalMarks: 100,
    teacher: 'Dr. <PERSON>',
    room: 'Room 101',
    instructions: 'Bring calculator and ruler. No mobile phones allowed.',
  },
  {
    id: 2,
    name: 'Physics Final Exam',
    subject: 'Physics',
    grade: 'Grade 11',
    date: '2024-03-20',
    time: '10:00',
    duration: '3',
    totalMarks: 100,
    teacher: 'Prof. <PERSON>',
    room: 'Room 205',
    instructions: 'Formula sheet will be provided. Bring scientific calculator.',
  },
];

export default function EditExamPage() {
  const { id } = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const record = mockExams.find(r => r.id === Number(id));
    if (record) {
      setFormData(record);
    }
  }, [id]);

  if (!formData) {
    return <p className='p-6'>Exam not found</p>;
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated exam:', formData);
      router.push(`/dashboard/exams/${id}`);
    } catch (error) {
      console.error('Error updating exam:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History'];
  const grades = ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
  const teachers = ['Dr. Sarah Johnson', 'Prof. Michael Chen', 'Ms. Emily Davis', 'Mr. John Smith'];
  const rooms = ['Room 101', 'Room 102', 'Room 201', 'Room 202', 'Room 205', 'Room 301'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/exams/${id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Exam
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <BookOpen className='w-8 h-8 text-blue-600' />
            Edit Exam
          </h1>
          <p className='text-gray-600 mt-1'>Update exam details and schedule</p>
        </div>
      </div>

      {/* Form */}
      <div className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Exam Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='name'>Exam Name *</Label>
              <Input
                id='name'
                placeholder='e.g., Mathematics Midterm'
                value={formData.name}
                onChange={e => handleChange('name', e.target.value)}
                required
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='subject'>Subject *</Label>
                <Select value={formData.subject} onValueChange={value => handleChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select subject' />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='grade'>Grade *</Label>
                <Select value={formData.grade} onValueChange={value => handleChange('grade', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select grade' />
                  </SelectTrigger>
                  <SelectContent>
                    {grades.map(grade => (
                      <SelectItem key={grade} value={grade}>
                        {grade}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='teacher'>Teacher *</Label>
              <Select value={formData.teacher} onValueChange={value => handleChange('teacher', value)}>
                <SelectTrigger>
                  <SelectValue placeholder='Select teacher' />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map(teacher => (
                    <SelectItem key={teacher} value={teacher}>
                      {teacher}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Schedule & Venue */}
        <Card>
          <CardHeader>
            <CardTitle>Schedule & Venue</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='date'>Date *</Label>
                <Input
                  id='date'
                  type='date'
                  value={formData.date}
                  onChange={e => handleChange('date', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='time'>Time *</Label>
                <Input
                  id='time'
                  type='time'
                  value={formData.time}
                  onChange={e => handleChange('time', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='duration'>Duration (hours) *</Label>
                <Input
                  id='duration'
                  type='number'
                  min='0.5'
                  max='6'
                  step='0.5'
                  value={formData.duration}
                  onChange={e => handleChange('duration', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='room'>Room *</Label>
                <Select value={formData.room} onValueChange={value => handleChange('room', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select room' />
                  </SelectTrigger>
                  <SelectContent>
                    {rooms.map(room => (
                      <SelectItem key={room} value={room}>
                        {room}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='totalMarks'>Total Marks *</Label>
                <Input
                  id='totalMarks'
                  type='number'
                  min='1'
                  max='200'
                  value={formData.totalMarks}
                  onChange={e => handleChange('totalMarks', parseInt(e.target.value))}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Label htmlFor='instructions'>Exam Instructions</Label>
              <Textarea
                id='instructions'
                placeholder='Enter any special instructions for students...'
                value={formData.instructions}
                onChange={e => handleChange('instructions', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/exams/${id}`}>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Updating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Update Exam
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
