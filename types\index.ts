/**
 * Shared Types and Interfaces
 *
 * Centralized type definitions for the entire application
 */

// ============================================================================
// COMMON TYPES
// ============================================================================

export type Status = 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'SUSPENDED' | 'ON_LEAVE';
export type UserRole = 'ADMIN' | 'TEACHER' | 'STUDENT' | 'PARENT';
export type AttendanceStatus = 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
export type ExamStatus = 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'POSTPONED';
export type PaymentStatus = 'PAID' | 'PENDING' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';

// ============================================================================
// BASE INTERFACES
// ============================================================================

export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface TimestampedEntity extends BaseEntity {
  created_by?: string;
  updated_by?: string;
}

// ============================================================================
// USER TYPES
// ============================================================================

export interface User extends BaseEntity {
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  status: Status;
  permissions: string[];
  last_login?: string;
}

export interface UserProfile extends User {
  bio?: string;
  address?: string;
  emergency_contact?: string;
  date_of_birth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
}

// ============================================================================
// ADDRESS AND CONTACT TYPES
// ============================================================================

export interface Address {
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email: string;
}

// ============================================================================
// TEACHER TYPES
// ============================================================================

export interface Teacher extends TimestampedEntity {
  teacher_id?: string | undefined;
  first_name: string;
  last_name: string;
  name?: string | undefined; // Keep for backward compatibility
  email: string;
  phone: string;
  date_of_birth?: string | undefined;
  gender?: 'MALE' | 'FEMALE' | 'OTHER' | undefined;
  address: Address;
  department: string;
  subject_specialization: string[];
  qualification: string;
  experience_years: number;
  experience?: number | undefined; // Keep for backward compatibility
  hire_date: string;
  salary?: number | undefined;
  status: Status;
  profile_picture?: string | undefined;
  avatar?: string | undefined; // Keep for backward compatibility
  emergency_contact: EmergencyContact;
  certifications?:
    | Array<{
        name: string;
        issuer: string;
        issue_date: string;
        expiry_date: string;
        certificate_id: string;
      }>
    | undefined;
}

export interface TeacherFilters {
  search?: string | undefined;
  department?: string | undefined;
  subject?: string | undefined;
  subject_specialization?: string | undefined;
  status?: string | undefined;
  experience_min?: number | undefined;
  experience_max?: number | undefined;
  experience_years_min?: number | undefined;
  experience_years_max?: number | undefined;
  qualification?: string | undefined;
  gender?: 'MALE' | 'FEMALE' | 'OTHER' | undefined;
}

export interface TeacherStats {
  total: number;
  active: number;
  inactive: number;
  departments: number;
  averageExperience: number;
  newHires: number;
}

// ============================================================================
// STUDENT TYPES
// ============================================================================

export interface Student extends TimestampedEntity {
  id: string;
  reg_no: string; // admission_number from backend
  first_name: string; // name from backend
  last_name: string; // surname from backend
  gender: 'male' | 'female' | 'other';
  dob?: string; // date_of_birth from backend
  class_id: string;
  section_id: string; // grade_id from backend
  guardian_name?: string;
  guardian_phone?: string;
  address?: string;
  email?: string;
  photo_url?: string; // img from backend
  is_active: boolean;
  created_at: string;
}

export interface StudentFilters {
  search?: string | undefined;
  class_id?: string | undefined;
  section_id?: string | undefined;
  status?: string | undefined;
  gender?: string | undefined;
  enrollment_year?: string | undefined;
}

export interface StudentStats {
  total: number;
  active: number;
  graduated: number;
  transferred: number;
  grades: number;
  classes: number;
  newEnrollments: number;
}

export interface StudentCreate {
  reg_no: string;
  first_name: string;
  last_name: string;
  gender: 'male' | 'female' | 'other';
  dob?: string;
  class_id: string;
  section_id: string;
  guardian_name?: string;
  guardian_phone?: string;
  address?: string;
  email?: string;
  password: string; // Required for backend
  parent_id: string; // Required for backend
}

export interface StudentUpdate {
  reg_no?: string;
  first_name?: string;
  last_name?: string;
  gender?: 'male' | 'female' | 'other';
  dob?: string;
  class_id?: string;
  section_id?: string;
  guardian_name?: string;
  guardian_phone?: string;
  address?: string;
  email?: string;
  photo_url?: string;
  is_active?: boolean;
}

export interface StudentListResponse {
  items: Student[];
  page: number;
  size: number;
  total: number;
  pages: number;
}

export interface StudentToggleResponse {
  id: string;
  is_active: boolean;
}

export interface StudentPhotoResponse {
  photo_url: string;
}

export interface StudentImportResponse {
  created: number;
  updated: number;
  errors: Array<{ row: number; error: string }>;
}

// ============================================================================
// CLASS TYPES
// ============================================================================

export interface Class extends TimestampedEntity {
  name: string;
  grade: string;
  section: string;
  capacity: number;
  enrolled: number;
  teacher_id: string;
  teacher_name: string;
  room: string;
  schedule: string;
  academic_year: string;
  status: Status;
  subjects?: string[];
  class_monitor?: string;
}

export interface ClassFilters {
  search?: string;
  grade?: string;
  section?: string;
  status?: string;
  academic_year?: string;
  teacher_id?: string;
}

export interface ClassStats {
  total: number;
  active: number;
  totalCapacity: number;
  totalEnrolled: number;
  averageCapacity: number;
  occupancyRate: number;
  grades: number;
}

// ============================================================================
// EXAM TYPES
// ============================================================================

export interface Exam extends TimestampedEntity {
  title: string;
  subject: string;
  grade: string;
  class: string;
  date: string;
  start_time: string;
  end_time: string;
  duration: number; // in minutes
  total_marks: number;
  passing_marks: number;
  room: string;
  teacher_id: string;
  teacher_name: string;
  instructions?: string;
  status: ExamStatus;
  exam_type?: 'MIDTERM' | 'FINAL' | 'QUIZ' | 'PRACTICAL' | 'ASSIGNMENT';
}

export interface ExamFilters {
  search?: string;
  subject?: string;
  grade?: string;
  class?: string;
  status?: string;
  exam_type?: string;
  date_from?: string;
  date_to?: string;
  teacher_id?: string;
}

export interface ExamStats {
  total: number;
  scheduled: number;
  completed: number;
  inProgress: number;
  cancelled: number;
  subjects: number;
  averageDuration: number;
  totalMarks: number;
}

// ============================================================================
// ATTENDANCE TYPES
// ============================================================================

export interface Attendance extends TimestampedEntity {
  student_id: string;
  student_name: string;
  class: string;
  date: string;
  status: AttendanceStatus;
  check_in_time?: string;
  check_out_time?: string;
  teacher_id: string;
  teacher_name: string;
  notes?: string;
  late_minutes?: number;
}

export interface AttendanceFilters {
  search?: string;
  class?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
  student_id?: string;
  teacher_id?: string;
}

export interface AttendanceStats {
  total: number;
  present: number;
  absent: number;
  late: number;
  excused: number;
  attendanceRate: number;
}

export interface AttendanceSummary {
  date: string;
  total: number;
  present: number;
  absent: number;
  late: number;
  excused: number;
  attendanceRate: number;
}

// ============================================================================
// FEE TYPES
// ============================================================================

export interface Fee extends TimestampedEntity {
  student_id: string;
  student_name: string;
  class: string;
  fee_type: 'TUITION' | 'TRANSPORT' | 'LIBRARY' | 'LABORATORY' | 'SPORTS' | 'OTHER';
  amount: number;
  due_date: string;
  paid_date?: string;
  payment_method?: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHEQUE' | 'ONLINE';
  transaction_id?: string;
  status: PaymentStatus;
  discount?: number;
  late_fee?: number;
  notes?: string;
}

export interface FeeFilters {
  search?: string;
  class?: string;
  fee_type?: string;
  status?: string;
  due_date_from?: string;
  due_date_to?: string;
  student_id?: string;
}

export interface FeeStats {
  total: number;
  paid: number;
  pending: number;
  overdue: number;
  totalAmount: number;
  collectedAmount: number;
  pendingAmount: number;
  collectionRate: number;
}

// ============================================================================
// GRADE TYPES
// ============================================================================

export interface Grade extends TimestampedEntity {
  student_id: string;
  student_name: string;
  class: string;
  subject: string;
  exam_id?: string;
  exam_title?: string;
  marks_obtained: number;
  total_marks: number;
  percentage: number;
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F';
  remarks?: string;
  teacher_id: string;
  teacher_name: string;
  academic_year: string;
  term: 'FIRST' | 'SECOND' | 'THIRD' | 'ANNUAL';
}

export interface GradeFilters {
  search?: string;
  class?: string;
  subject?: string;
  grade?: string;
  academic_year?: string;
  term?: string;
  student_id?: string;
  teacher_id?: string;
  exam_id?: string;
}

export interface GradeStats {
  total: number;
  averagePercentage: number;
  passRate: number;
  gradeDistribution: Record<string, number>;
  topPerformers: number;
  needsImprovement: number;
}

// ============================================================================
// API TYPES
// ============================================================================

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
  timestamp: string;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ProfileUpdateData {
  name?: string;
  phone?: string;
  avatar?: string;
  bio?: string;
  address?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type SortDirection = 'asc' | 'desc';
export type ViewMode = 'grid' | 'table' | 'list';

export interface SortConfig {
  key: string;
  direction: SortDirection;
}

export interface FilterConfig {
  [key: string]: any;
}

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  isLoading?: boolean;
  loadingText?: string;
}

export interface ErrorProps extends BaseComponentProps {
  error?: string | Error | null;
  onRetry?: () => void;
}

export interface EmptyStateProps extends BaseComponentProps {
  title?: string;
  description?: string;
  action?: React.ReactNode;
  icon?: React.ReactNode;
}
