import { useRouter } from 'next/navigation';

/**
 * Custom hook for navigating to create pages
 * Provides consistent navigation patterns across all modules
 */
export function useNavigateToCreate() {
  const router = useRouter();

  const navigateToCreate = (module: string) => {
    const path = `/dashboard/${module}/create`;
    router.push(path);
  };

  return { navigateToCreate };
}

/**
 * Module-specific navigation hooks for better type safety
 */
export const useModuleNavigation = () => {
  const { navigateToCreate } = useNavigateToCreate();

  return {
    // Student management
    navigateToCreateStudent: () => navigateToCreate('students'),
    
    // Teacher management
    navigateToCreateTeacher: () => navigateToCreate('teachers'),
    
    // Subject management
    navigateToCreateSubject: () => navigateToCreate('subjects'),
    
    // Student fee management
    navigateToCreateStudentFee: () => navigateToCreate('student-fee'),
    
    // Attendance management
    navigateToCreateAttendance: () => navigateToCreate('attendance'),
    
    // Grade management
    navigateToCreateGrade: () => navigateToCreate('grade'),
    
    // Media management
    navigateToCreateMedia: () => navigateToCreate('media'),
    
    // Exam management
    navigateToCreateExam: () => navigateToCreate('exams'),
  };
};
