/**
 * API Proxy Route Handler
 * 
 * Proxies all requests to the FastAPI backend with:
 * - Automatic token injection from httpOnly cookies
 * - Proper error handling and logging
 * - Request/response transformation
 * - Security headers
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getBackendUrl } from '@/lib/auth-config';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';

interface ProxyError {
  message: string;
  status: number;
  details?: any;
}

/**
 * Create a standardized error response
 */
function createErrorResponse(error: ProxyError): NextResponse {
  return NextResponse.json(
    {
      detail: error.message,
      status_code: error.status,
      request_id: crypto.randomUUID(),
      ...(error.details && { details: error.details }),
    },
    { status: error.status }
  );
}

/**
 * Log request/response for debugging
 */
function logRequest(
  method: string,
  path: string,
  status: number,
  duration: number,
  error?: any
) {
  if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
    const timestamp = new Date().toISOString();
    const logLevel = error ? 'ERROR' : status >= 400 ? 'WARN' : 'INFO';
    
    console.log(`[${timestamp}] [PROXY ${logLevel}] ${method} ${path} - ${status} (${duration}ms)`);
    
    if (error) {
      console.error('Proxy Error Details:', error);
    }
  }
}

/**
 * Handle all HTTP methods
 */
async function handleRequest(
  request: NextRequest,
  { params }: { params: { path: string[] } }
): Promise<NextResponse> {
  const startTime = Date.now();
  const method = request.method;
  const path = params.path.join('/');
  const backendUrl = getBackendUrl(`/api/v1/${path}`);
  
  try {
    // Get auth token from cookie
    const cookieStore = cookies();
    const authToken = cookieStore.get('auth_token')?.value;
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Add authorization header if token exists
    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }
    
    // Copy relevant headers from the original request
    const forwardHeaders = ['user-agent', 'accept-language', 'x-forwarded-for'];
    forwardHeaders.forEach(headerName => {
      const value = request.headers.get(headerName);
      if (value) {
        headers[headerName] = value;
      }
    });
    
    // Prepare request body
    let body: string | undefined;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const requestBody = await request.text();
        body = requestBody || undefined;
      } catch (error) {
        console.warn('Failed to read request body:', error);
      }
    }
    
    // Make request to backend
    const response = await fetch(backendUrl, {
      method,
      headers,
      body,
      // Don't follow redirects automatically
      redirect: 'manual',
    });
    
    const duration = Date.now() - startTime;
    
    // Handle different response types
    let responseData: any;
    const contentType = response.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      try {
        responseData = await response.json();
      } catch (error) {
        responseData = { detail: 'Invalid JSON response from backend' };
      }
    } else {
      responseData = await response.text();
    }
    
    // Log the request
    logRequest(method, path, response.status, duration);
    
    // Handle authentication errors
    if (response.status === 401) {
      // Clear the auth cookie on 401
      const errorResponse = NextResponse.json(
        {
          detail: 'Authentication required',
          status_code: 401,
          request_id: crypto.randomUUID(),
        },
        { status: 401 }
      );
      
      errorResponse.cookies.delete('auth_token');
      return errorResponse;
    }
    
    // Create response with same status and data
    const nextResponse = NextResponse.json(responseData, {
      status: response.status,
      statusText: response.statusText,
    });
    
    // Copy relevant response headers
    const responseHeaders = ['content-type', 'cache-control', 'etag'];
    responseHeaders.forEach(headerName => {
      const value = response.headers.get(headerName);
      if (value) {
        nextResponse.headers.set(headerName, value);
      }
    });
    
    return nextResponse;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      logRequest(method, path, 503, duration, error);
      return createErrorResponse({
        message: 'Backend service unavailable',
        status: 503,
        details: process.env.NEXT_PUBLIC_DEBUG === 'true' ? error.message : undefined,
      });
    }
    
    // Handle other errors
    logRequest(method, path, 500, duration, error);
    return createErrorResponse({
      message: 'Internal proxy error',
      status: 500,
      details: process.env.NEXT_PUBLIC_DEBUG === 'true' ? String(error) : undefined,
    });
  }
}

// Export handlers for all HTTP methods
export const GET = handleRequest;
export const POST = handleRequest;
export const PUT = handleRequest;
export const PATCH = handleRequest;
export const DELETE = handleRequest;
export const HEAD = handleRequest;
export const OPTIONS = handleRequest;
