/**
 * Reports Loading Page - Professional Loading State
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function ReportsLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header Skeleton */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <Skeleton className='h-8 w-48 mb-2' />
          <Skeleton className='h-4 w-64' />
        </div>
        <Skeleton className='h-10 w-32' />
      </div>

      {/* Quick Stats Skeleton */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <Skeleton className='h-8 w-8 rounded-lg' />
                <div>
                  <Skeleton className='h-4 w-20 mb-1' />
                  <Skeleton className='h-6 w-8' />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Report Categories Skeleton */}
      <div>
        <Skeleton className='h-6 w-32 mb-6' />
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <Skeleton className='h-12 w-12 rounded-lg' />
                  <Skeleton className='h-6 w-20' />
                </div>
                <Skeleton className='h-5 w-32' />
                <Skeleton className='h-4 w-full' />
              </CardHeader>
              <CardContent>
                <div className='flex items-center justify-between'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-8 w-20' />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Exports Skeleton */}
      <div>
        <Skeleton className='h-6 w-32 mb-6' />
        <Card>
          <CardContent className='p-6'>
            <div className='space-y-4'>
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className='flex items-center justify-between p-4 border rounded-lg'>
                  <div className='flex items-center space-x-4'>
                    <Skeleton className='h-8 w-8 rounded-lg' />
                    <div>
                      <Skeleton className='h-4 w-48 mb-1' />
                      <Skeleton className='h-3 w-32' />
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Skeleton className='h-6 w-16' />
                    <Skeleton className='h-8 w-8' />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
