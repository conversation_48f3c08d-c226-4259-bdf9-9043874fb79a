'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON><PERSON>, Clock, Loader2, Save, X } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';

// Schema imports - demonstrating strict type usage
import {
  SubjectCreateSchema,
  SubjectUpdateSchema,
  type SubjectCreate,
  type SubjectUpdate,
} from '@/schemas/zodSchemas';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSection,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Types for the component props - strict typing
interface SubjectFormProps {
  mode: 'create' | 'edit';
  initialData?: SubjectUpdate;
  onSubmit: (data: SubjectCreate | SubjectUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Subject categories with type safety
const SUBJECT_CATEGORIES = [
  { value: 'CORE', label: 'Core Subject' },
  { value: 'ELECTIVE', label: 'Elective' },
  { value: 'ADVANCED', label: 'Advanced Placement' },
  { value: 'REMEDIAL', label: 'Remedial' },
  { value: 'EXTRACURRICULAR', label: 'Extracurricular' },
] as const;

// Credit hours options
const CREDIT_HOURS = [
  { value: 1, label: '1 Credit Hour' },
  { value: 2, label: '2 Credit Hours' },
  { value: 3, label: '3 Credit Hours' },
  { value: 4, label: '4 Credit Hours' },
  { value: 5, label: '5 Credit Hours' },
] as const;

// Grade levels for subject availability
const GRADE_LEVELS = ['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'] as const;

/**
 * SubjectForm Component
 *
 * Demonstrates strict type usage with Zod + React Hook Form:
 * - Uses zodResolver for validation
 * - Types inferred from Zod schemas
 * - Multi-select functionality for grade levels
 * - Conditional validation based on subject type
 * - Real-time validation with proper error display
 */
export function SubjectForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}: SubjectFormProps) {
  // Schema selection based on mode
  const schema = mode === 'create' ? SubjectCreateSchema : SubjectUpdateSchema;

  // Form initialization with strict typing
  // Form initialization with strict typing
  const form = useForm<SubjectCreate | SubjectUpdate>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'create'
        ? {
            name: '',
            code: '',
            description: '',
            credits: 3,
            category: '',
            is_active: true,
          }
        : initialData || {},
    mode: 'onChange', // Real-time validation
  });

  // Watch values for conditional logic
  const watchedSubjectName = form.watch('name');
  const watchedCreditHours = form.watch('credits');

  // Auto-generate subject code based on subject name
  React.useEffect(() => {
    if (watchedSubjectName && mode === 'create') {
      const code = watchedSubjectName
        .toUpperCase()
        .replace(/[^A-Z0-9\s]/g, '')
        .split(' ')
        .map(word => word.substring(0, 3))
        .join('')
        .substring(0, 6);

      if (code !== form.getValues('code')) {
        form.setValue('code', code, { shouldValidate: true });
      }
    }
  }, [watchedSubjectName, mode, form]);

  // Type-safe form submission
  const handleSubmit = async (data: SubjectCreate | SubjectUpdate) => {
    try {
      // Validate data against schema before submission
      const validatedData = schema.parse(data);
      await onSubmit(validatedData);
    } catch (error) {
      console.error('Form validation error:', error);
    }
  };

  return (
    <Card className='w-full max-w-2xl mx-auto'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <BookOpen className='w-5 h-5' />
          {mode === 'create' ? 'Add New Subject' : 'Edit Subject'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Create a new subject for the curriculum.'
            : 'Update the subject information below.'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            {/* Basic Subject Information */}
            <FormSection title='Subject Details' description='Basic information about the subject'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Subject Name - Required */}
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem className='md:col-span-2'>
                      <FormLabel>Subject Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., Advanced Mathematics, World History' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subject Code - Auto-generated but editable */}
                <FormField
                  control={form.control}
                  name='code'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject Code *</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., MATH101, HIST201' {...field} />
                      </FormControl>
                      <FormMessage />
                      {mode === 'create' && (
                        <p className='text-xs text-gray-500'>Auto-generated from subject name</p>
                      )}
                    </FormItem>
                  )}
                />

                {/* Credit Hours - Number validation */}
                <FormField
                  control={form.control}
                  name='credits'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex items-center gap-2'>
                        <Clock className='w-4 h-4' />
                        Credit Hours *
                      </FormLabel>
                      <Select
                        onValueChange={value => field.onChange(parseInt(value))}
                        defaultValue={field.value?.toString() || ''}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select credit hours' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CREDIT_HOURS.map(option => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Department - Optional */}
                <FormField
                  control={form.control}
                  name='category'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., Mathematics, Science' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                
              </div>
            </FormSection>

            {/* Description Section */}
            <FormSection
              title='Course Description'
              description='Detailed information about the subject content and objectives'
            >
              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <textarea
                        className='flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                        placeholder='Describe the subject content, learning objectives, and key topics covered...'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <p className='text-xs text-gray-500'>
                      {field.value?.length || 0}/1000 characters
                    </p>
                  </FormItem>
                )}
              />
            </FormSection>

            {/* Credit Hours Information */}
            {watchedCreditHours && (
              <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                <div className='flex items-center gap-2 text-blue-800'>
                  <Clock className='w-4 h-4' />
                  <span className='font-medium'>Credit Hours: {watchedCreditHours}</span>
                </div>
                <p className='text-sm text-blue-600 mt-1'>
                  This subject will count for {watchedCreditHours} credit hour
                  {watchedCreditHours !== 1 ? 's' : ''}
                  towards graduation requirements.
                </p>
              </div>
            )}

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 pt-6 border-t'>
              <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                <X className='w-4 h-4 mr-2' />
                Cancel
              </Button>

              <Button type='submit' disabled={isLoading || !form.formState.isValid}>
                {isLoading ? (
                  <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                ) : (
                  <Save className='w-4 h-4 mr-2' />
                )}
                {mode === 'create' ? 'Create Subject' : 'Update Subject'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Export type for external usage
export type { SubjectFormProps };

// Default export for lazy loading
export default SubjectForm;
