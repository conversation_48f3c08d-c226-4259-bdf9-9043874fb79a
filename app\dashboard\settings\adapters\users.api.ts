/**
 * Users API Adapter
 * 
 * Type-safe API wrapper for user management operations:
 * - User CRUD operations
 * - Role management
 * - Status updates
 * - Search and filtering
 */

import apiClient from '@/api/apiService';
import type {
  User,
  CreateUser,
  UpdateUser,
  UpdateUserStatus,
  UserListQuery,
  UserListResponse,
  UserRoleType,
  UserStatusType,
} from '../schemas/users.schemas';

// API endpoints
const ENDPOINTS = {
  USERS: '/api/settings/users',
  USER: (id: string) => `/api/settings/users/${id}`,
  USER_STATUS: (id: string) => `/api/settings/users/${id}/status`,
} as const;

// Mock data for development
const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'system',
  },
  {
    id: '2',
    name: 'Sarah <PERSON>',
    email: '<EMAIL>',
    role: 'Teacher',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-14T15:45:00Z',
    createdAt: '2024-01-15T00:00:00Z',
    createdBy: '1',
  },
  {
    id: '3',
    name: 'Mike Accountant',
    email: '<EMAIL>',
    role: 'Accountant',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-13T09:20:00Z',
    createdAt: '2024-02-01T00:00:00Z',
    createdBy: '1',
  },
  {
    id: '4',
    name: 'Lisa Clerk',
    email: '<EMAIL>',
    role: 'Clerk',
    status: 'Inactive',
    phone: '+91-**********',
    lastLogin: '2024-03-10T14:15:00Z',
    createdAt: '2024-02-15T00:00:00Z',
    createdBy: '1',
  },
  {
    id: '5',
    name: 'Emma Student',
    email: '<EMAIL>',
    role: 'Student',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-12T11:30:00Z',
    createdAt: '2024-03-01T00:00:00Z',
    createdBy: '2',
  },
];

/**
 * Users API Service
 * 
 * Provides type-safe methods for user management operations
 */
export const usersApi = {
  // Get users with search and filtering
  getUsers: async (query: UserListQuery = {}): Promise<UserListResponse> => {
    try {
      const params = new URLSearchParams();
      
      if (query.search) params.append('search', query.search);
      if (query.role) params.append('role', query.role);
      if (query.status) params.append('status', query.status);
      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortOrder) params.append('sortOrder', query.sortOrder);

      const response = await apiClient.get(`${ENDPOINTS.USERS}?${params.toString()}`);
      return response.data;
    } catch (_error) {
      console.warn('Using mock users data');
      
      // Apply filters to mock data
      let filteredUsers = [...mockUsers];
      
      if (query.search) {
        const searchLower = query.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.name.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower)
        );
      }
      
      if (query.role) {
        filteredUsers = filteredUsers.filter(user => user.role === query.role);
      }
      
      if (query.status) {
        filteredUsers = filteredUsers.filter(user => user.status === query.status);
      }
      
      // Apply sorting
      const sortBy = query.sortBy || 'name';
      const sortOrder = query.sortOrder || 'asc';
      
      filteredUsers.sort((a, b) => {
        let aValue = a[sortBy as keyof User] as string;
        let bValue = b[sortBy as keyof User] as string;
        
        if (sortBy === 'createdAt' || sortBy === 'lastLogin') {
          aValue = aValue || '';
          bValue = bValue || '';
        }
        
        const comparison = aValue.localeCompare(bValue);
        return sortOrder === 'asc' ? comparison : -comparison;
      });
      
      // Apply pagination
      const page = query.page || 1;
      const limit = query.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
      
      return {
        users: paginatedUsers,
        pagination: {
          page,
          limit,
          total: filteredUsers.length,
          totalPages: Math.ceil(filteredUsers.length / limit),
        },
      };
    }
  },

  // Get single user by ID
  getUser: async (id: string): Promise<User> => {
    try {
      const response = await apiClient.get(ENDPOINTS.USER(id));
      return response.data;
    } catch (_error) {
      console.warn('Using mock user data');
      const user = mockUsers.find(u => u.id === id);
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    }
  },

  // Create new user
  createUser: async (data: CreateUser): Promise<User> => {
    try {
      const response = await apiClient.post(ENDPOINTS.USERS, data);
      return response.data;
    } catch (_error) {
      console.warn('Mock create - returning new user');
      
      // Check for duplicate email in mock data
      const existingUser = mockUsers.find(u => u.email === data.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }
      
      const newUser: User = {
        ...data,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        createdBy: 'current-user',
      };
      
      mockUsers.push(newUser);
      return newUser;
    }
  },

  // Update user
  updateUser: async (id: string, data: UpdateUser): Promise<User> => {
    try {
      const response = await apiClient.put(ENDPOINTS.USER(id), data);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated user');
      
      const userIndex = mockUsers.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      // Check for duplicate email (excluding current user)
      if (data.email) {
        const existingUser = mockUsers.find(u => u.email === data.email && u.id !== id);
        if (existingUser) {
          throw new Error('User with this email already exists');
        }
      }
      
      const updatedUser: User = {
        ...mockUsers[userIndex],
        ...data,
        updatedAt: new Date().toISOString(),
      };
      
      mockUsers[userIndex] = updatedUser;
      return updatedUser;
    }
  },

  // Update user status
  updateUserStatus: async (id: string, data: UpdateUserStatus): Promise<User> => {
    try {
      const response = await apiClient.patch(ENDPOINTS.USER_STATUS(id), data);
      return response.data;
    } catch (_error) {
      console.warn('Mock status update - returning updated user');
      
      const userIndex = mockUsers.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      const updatedUser: User = {
        ...mockUsers[userIndex],
        status: data.status,
        updatedAt: new Date().toISOString(),
      };
      
      mockUsers[userIndex] = updatedUser;
      return updatedUser;
    }
  },

  // Delete user (soft delete)
  deleteUser: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(ENDPOINTS.USER(id));
    } catch (_error) {
      console.warn('Mock delete - user removed');
      
      const userIndex = mockUsers.findIndex(u => u.id === id);
      if (userIndex === -1) {
        throw new Error('User not found');
      }
      
      // In real implementation, this would be a soft delete
      mockUsers.splice(userIndex, 1);
    }
  },

  // Get user roles
  getRoles: async (): Promise<UserRoleType[]> => {
    try {
      const response = await apiClient.get('/api/settings/users/roles');
      return response.data;
    } catch (_error) {
      console.warn('Using mock roles data');
      return ['Admin', 'Teacher', 'Accountant', 'Clerk', 'Student'];
    }
  },

  // Get user statistics
  getUserStats: async (): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRoleType, number>;
  }> => {
    try {
      const response = await apiClient.get('/api/settings/users/stats');
      return response.data;
    } catch (_error) {
      console.warn('Using mock stats data');
      
      const total = mockUsers.length;
      const active = mockUsers.filter(u => u.status === 'Active').length;
      const inactive = mockUsers.filter(u => u.status === 'Inactive').length;
      
      const byRole: Record<UserRoleType, number> = {
        Admin: mockUsers.filter(u => u.role === 'Admin').length,
        Teacher: mockUsers.filter(u => u.role === 'Teacher').length,
        Accountant: mockUsers.filter(u => u.role === 'Accountant').length,
        Clerk: mockUsers.filter(u => u.role === 'Clerk').length,
        Student: mockUsers.filter(u => u.role === 'Student').length,
      };
      
      return { total, active, inactive, byRole };
    }
  },
};

// Query keys for TanStack Query
export const userQueryKeys = {
  all: ['settings', 'users'] as const,
  lists: () => [...userQueryKeys.all, 'list'] as const,
  list: (query: UserListQuery) => [...userQueryKeys.lists(), query] as const,
  details: () => [...userQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...userQueryKeys.details(), id] as const,
  stats: () => [...userQueryKeys.all, 'stats'] as const,
  roles: () => [...userQueryKeys.all, 'roles'] as const,
} as const;
