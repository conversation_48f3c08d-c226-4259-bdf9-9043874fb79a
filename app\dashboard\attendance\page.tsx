'use client';

import {
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Filter,
  Plus,
  Search,
  UserCheck,
  Users,
  XCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Type definitions for better type safety
interface AttendanceRecord {
  id: number;
  studentName: string;
  studentId: string;
  class: string;
  section?: string;
  date: string;
  status: 'Present' | 'Absent' | 'Late' | 'Excused';
  timeIn: string | null;
  timeOut: string | null;
  notes?: string;
  parentNotified?: boolean;
}

interface AttendanceStats {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  attendanceRate: number;
}

// Mock data with enhanced structure
const mockAttendanceStats: AttendanceStats = {
  totalStudents: 450,
  presentToday: 385,
  absentToday: 25,
  lateToday: 40,
  attendanceRate: 94.4,
};

const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: 1,
    studentName: 'John Doe',
    studentId: 'STU001',
    class: 'Grade 10',
    section: 'A',
    date: '2024-01-15',
    status: 'Present',
    timeIn: '08:00 AM',
    timeOut: '03:30 PM',
    notes: 'Regular attendance',
    parentNotified: false,
  },
  {
    id: 2,
    studentName: 'Jane Smith',
    studentId: 'STU002',
    class: 'Grade 9',
    section: 'B',
    date: '2024-01-15',
    status: 'Absent',
    timeIn: null,
    timeOut: null,
    notes: 'Sick leave reported',
    parentNotified: true,
  },
  {
    id: 3,
    studentName: 'Mike Johnson',
    studentId: 'STU003',
    class: 'Grade 11',
    section: 'A',
    date: '2024-01-15',
    status: 'Late',
    timeIn: '08:30 AM',
    timeOut: '03:30 PM',
    notes: 'Transportation delay',
    parentNotified: true,
  },
  {
    id: 4,
    studentName: 'Sarah Wilson',
    studentId: 'STU004',
    class: 'Grade 10',
    section: 'B',
    date: '2024-01-15',
    status: 'Excused',
    timeIn: null,
    timeOut: null,
    notes: 'Medical appointment',
    parentNotified: false,
  },
];

const STATUS_OPTIONS = ['All', 'Present', 'Absent', 'Late', 'Excused'] as const;
const DATE_OPTIONS = ['Today', 'Yesterday', 'This Week', 'This Month'] as const;
const CLASS_OPTIONS = ['All Classes', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'] as const;

export default function AttendancePage() {
  const router = useRouter();

  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [dateFilter, setDateFilter] = useState<string>('Today');
  const [classFilter, setClassFilter] = useState<string>('All Classes');
  const [isLoading, setIsLoading] = useState(false);

  // Memoized filtered records for performance
  const filteredRecords = useMemo(() => {
    return mockAttendanceRecords.filter(record => {
      const matchesSearch =
        record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.class.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'All' || record.status === statusFilter;
      const matchesClass = classFilter === 'All Classes' || record.class === classFilter;

      return matchesSearch && matchesStatus && matchesClass;
    });
  }, [searchTerm, statusFilter, classFilter]);

  // Navigation handlers
  const handleMarkAttendance = () => {
    router.push('/dashboard/attendance/create');
  };

  const handleViewDetails = (attendanceId: number) => {
    router.push(`/dashboard/attendance/${attendanceId}`);
  };

  const handleEditAttendance = (attendanceId: number) => {
    router.push(`/dashboard/attendance/${attendanceId}/edit`);
  };

  const handleExportData = async () => {
    setIsLoading(true);
    // Simulate export functionality
    setTimeout(() => {
      console.log('Exporting attendance data...');
      setIsLoading(false);
    }, 2000);
  };

  // Stats configuration
  const statsCards = [
    {
      title: 'Total Students',
      value: mockAttendanceStats.totalStudents.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+2%',
      changeType: 'positive' as const,
      description: 'Enrolled students',
    },
    {
      title: 'Present Today',
      value: mockAttendanceStats.presentToday.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5%',
      changeType: 'positive' as const,
      description: 'Students present',
    },
    {
      title: 'Absent Today',
      value: mockAttendanceStats.absentToday.toString(),
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '-2%',
      changeType: 'negative' as const,
      description: 'Students absent',
    },
    {
      title: 'Attendance Rate',
      value: `${mockAttendanceStats.attendanceRate}%`,
      icon: UserCheck,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+1.2%',
      changeType: 'positive' as const,
      description: 'Overall rate',
    },
  ];

  // Utility functions
  const getStatusConfig = (status: string) => {
    const configs = {
      Present: {
        color: 'text-green-600 bg-green-50 border-green-200',
        icon: CheckCircle,
        variant: 'default' as const,
      },
      Absent: {
        color: 'text-red-600 bg-red-50 border-red-200',
        icon: XCircle,
        variant: 'destructive' as const,
      },
      Late: {
        color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
        icon: Clock,
        variant: 'secondary' as const,
      },
      Excused: {
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        icon: Calendar,
        variant: 'outline' as const,
      },
    };
    return configs[status as keyof typeof configs] || configs.Present;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Attendance Management'
        description='Track and manage student attendance records with comprehensive reporting'
        icon={UserCheck}
        badge={{ label: 'Live System', variant: 'outline' }}
        actions={[
          {
            label: 'Export Data',
            icon: Download,
            onClick: handleExportData,
            variant: 'outline',
            loading: isLoading,
          },
          {
            label: 'Mark Attendance',
            icon: Plus,
            onClick: handleMarkAttendance,
            variant: 'default',
          },
        ]}
      />

      {/* Enhanced Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-muted-foreground mb-1'>{stat.title}</p>
                  <p className='text-2xl font-bold mb-1'>{stat.value}</p>
                  <p className='text-xs text-muted-foreground'>{stat.description}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </div>
              <div className='mt-3 flex items-center'>
                <span
                  className={`text-xs font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stat.change}
                </span>
                <span className='text-xs text-muted-foreground ml-1'>from yesterday</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col lg:flex-row gap-4'>
            <div className='flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
              {/* Search Input */}
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
                <Input
                  placeholder='Search students, ID, or class...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>

              {/* Status Filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className='h-4 w-4 mr-2' />
                  <SelectValue placeholder='All Status' />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map(status => (
                    <SelectItem key={status} value={status}>
                      {status === 'All' ? 'All Status' : status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Class Filter */}
              <Select value={classFilter} onValueChange={setClassFilter}>
                <SelectTrigger>
                  <SelectValue placeholder='All Classes' />
                </SelectTrigger>
                <SelectContent>
                  {CLASS_OPTIONS.map(cls => (
                    <SelectItem key={cls} value={cls}>
                      {cls}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Date Filter */}
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger>
                  <Calendar className='h-4 w-4 mr-2' />
                  <SelectValue placeholder='Select period' />
                </SelectTrigger>
                <SelectContent>
                  {DATE_OPTIONS.map(date => (
                    <SelectItem key={date} value={date}>
                      {date}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Results Summary */}
            <div className='flex items-center justify-center lg:justify-end'>
              <div className='text-sm text-muted-foreground bg-muted px-3 py-2 rounded-md'>
                Showing <span className='font-semibold'>{filteredRecords.length}</span> of{' '}
                <span className='font-semibold'>{mockAttendanceRecords.length}</span> records
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Attendance Records */}
      <div className='grid grid-cols-1 gap-4'>
        {filteredRecords.map(record => {
          const statusConfig = getStatusConfig(record.status);
          const StatusIcon = statusConfig.icon;

          return (
            <Card key={record.id} className='hover:shadow-md transition-all duration-200'>
              <CardContent className='p-6'>
                <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                  {/* Student Info */}
                  <div className='flex items-start space-x-4'>
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center ${statusConfig.color} border-2`}
                    >
                      <StatusIcon className='w-5 h-5' />
                    </div>
                    <div className='flex-1'>
                      <div className='flex items-center gap-3 mb-2'>
                        <h3 className='font-semibold text-lg'>{record.studentName}</h3>
                        <Badge variant={statusConfig.variant} className='text-xs'>
                          {record.status}
                        </Badge>
                        {record.parentNotified && (
                          <Badge variant='outline' className='text-xs'>
                            Parent Notified
                          </Badge>
                        )}
                      </div>
                      <div className='flex flex-wrap gap-2 text-sm text-muted-foreground mb-2'>
                        <span className='font-medium'>{record.studentId}</span>
                        <span>•</span>
                        <span>
                          {record.class}
                          {record.section && ` - Section ${record.section}`}
                        </span>
                        <span>•</span>
                        <span>{formatDate(record.date)}</span>
                      </div>
                      {record.notes && (
                        <p className='text-sm text-muted-foreground italic'>Note: {record.notes}</p>
                      )}
                    </div>
                  </div>

                  {/* Time Info & Actions */}
                  <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
                    {/* Time Information */}
                    <div className='text-right'>
                      <div className='space-y-1'>
                        {record.timeIn ? (
                          <>
                            <p className='text-sm'>
                              <span className='text-muted-foreground'>In:</span>{' '}
                              <span className='font-medium'>{record.timeIn}</span>
                            </p>
                            {record.timeOut && (
                              <p className='text-sm'>
                                <span className='text-muted-foreground'>Out:</span>{' '}
                                <span className='font-medium'>{record.timeOut}</span>
                              </p>
                            )}
                          </>
                        ) : (
                          <p className='text-sm text-muted-foreground'>No time recorded</p>
                        )}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className='flex gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleViewDetails(record.id)}
                        className='min-w-[100px]'
                      >
                        View Details
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleEditAttendance(record.id)}
                        className='min-w-[80px]'
                      >
                        Edit
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Enhanced Empty State */}
      {filteredRecords.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4'>
              <UserCheck className='w-12 h-12 text-muted-foreground' />
            </div>
            <h3 className='text-lg font-semibold mb-2'>No Attendance Records Found</h3>
            <p className='text-muted-foreground text-center mb-6 max-w-md'>
              {searchTerm || statusFilter !== 'All' || classFilter !== 'All Classes'
                ? 'No attendance records match your current search criteria. Try adjusting your filters or search terms.'
                : 'There are no attendance records in the system yet. Start by marking attendance for your students.'}
            </p>
            <div className='flex gap-2'>
              {(searchTerm || statusFilter !== 'All' || classFilter !== 'All Classes') && (
                <Button
                  variant='outline'
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('All');
                    setClassFilter('All Classes');
                  }}
                >
                  Clear Filters
                </Button>
              )}
              <Button onClick={handleMarkAttendance}>
                <Plus className='mr-2 h-4 w-4' />
                Mark Attendance
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
