'use client';

/**
 * Create Notification Page - Professional Notification Creation Form
 *
 * Features:
 * - Comprehensive notification creation form
 * - Priority and type selection
 * - Recipient targeting
 * - Schedule and delivery options
 * - Responsive design
 * - Loading states and validation
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Bell, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

const notificationTypes = [
  { id: 'alert', name: '<PERSON><PERSON>', description: 'Urgent notifications requiring immediate attention' },
  { id: 'reminder', name: 'Reminder', description: 'Reminders for upcoming events or deadlines' },
  { id: 'announcement', name: 'Announcement', description: 'General announcements and news' },
  { id: 'information', name: 'Information', description: 'Informational updates and notices' },
];

const priorities = [
  { id: 'critical', name: 'Critical', description: 'Requires immediate action' },
  { id: 'high', name: 'High', description: 'Important but not urgent' },
  { id: 'medium', name: 'Medium', description: 'Standard priority' },
  { id: 'low', name: 'Low', description: 'Low priority, informational' },
];

const recipientGroups = [
  { id: 'all-students', name: 'All Students' },
  { id: 'all-parents', name: 'All Parents' },
  { id: 'all-teachers', name: 'All Teachers' },
  { id: 'all-staff', name: 'All Staff' },
  { id: 'grade-specific', name: 'Specific Grade' },
  { id: 'class-specific', name: 'Specific Class' },
  { id: 'custom', name: 'Custom Selection' },
];

const senders = [
  { id: '1', name: 'Administration' },
  { id: '2', name: 'Principal Office' },
  { id: '3', name: 'Science Department' },
  { id: '4', name: 'Library' },
  { id: '5', name: 'Cafeteria' },
  { id: '6', name: 'School Board' },
];

interface NotificationFormData {
  title: string;
  message: string;
  type: string;
  priority: string;
  sender: string;
  recipientGroup: string;
  scheduleType: 'immediate' | 'scheduled';
  scheduledDate: string;
  scheduledTime: string;
  emailNotification: boolean;
  smsNotification: boolean;
  pushNotification: boolean;
  requiresAcknowledgment: boolean;
}

export default function CreateNotificationPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<NotificationFormData>({
    title: '',
    message: '',
    type: '',
    priority: 'medium',
    sender: '',
    recipientGroup: '',
    scheduleType: 'immediate',
    scheduledDate: '',
    scheduledTime: '',
    emailNotification: true,
    smsNotification: false,
    pushNotification: true,
    requiresAcknowledgment: false,
  });

  const handleInputChange = (field: keyof NotificationFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Creating notification:', formData);
      
      // Redirect to notifications list on success
      router.push('/dashboard/notifications');
    } catch (error) {
      console.error('Error creating notification:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href='/dashboard/notifications'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Notifications
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <Bell className='w-8 h-8 text-blue-600' />
            Create New Notification
          </h1>
          <p className='text-gray-600 mt-1'>Send announcements and alerts to the school community</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Content</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='title'>Title *</Label>
              <Input
                id='title'
                placeholder='e.g., Parent-Teacher Conference Reminder'
                value={formData.title}
                onChange={e => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='message'>Message *</Label>
              <Textarea
                id='message'
                placeholder='Enter the notification message...'
                value={formData.message}
                onChange={e => handleInputChange('message', e.target.value)}
                rows={6}
                required
              />
              <p className='text-sm text-gray-500'>
                {formData.message.length}/500 characters
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='type'>Notification Type *</Label>
                <Select value={formData.type} onValueChange={value => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select type' />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>
                        <div>
                          <div className='font-medium'>{type.name}</div>
                          <div className='text-sm text-gray-500'>{type.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='priority'>Priority *</Label>
                <Select value={formData.priority} onValueChange={value => handleInputChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select priority' />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities.map(priority => (
                      <SelectItem key={priority.id} value={priority.id}>
                        <div>
                          <div className='font-medium'>{priority.name}</div>
                          <div className='text-sm text-gray-500'>{priority.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recipients & Sender */}
        <Card>
          <CardHeader>
            <CardTitle>Recipients & Sender</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='sender'>Sender *</Label>
                <Select value={formData.sender} onValueChange={value => handleInputChange('sender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select sender' />
                  </SelectTrigger>
                  <SelectContent>
                    {senders.map(sender => (
                      <SelectItem key={sender.id} value={sender.id}>
                        {sender.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='recipientGroup'>Recipients *</Label>
                <Select value={formData.recipientGroup} onValueChange={value => handleInputChange('recipientGroup', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select recipients' />
                  </SelectTrigger>
                  <SelectContent>
                    {recipientGroups.map(group => (
                      <SelectItem key={group.id} value={group.id}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Options */}
        <Card>
          <CardHeader>
            <CardTitle>Delivery Options</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Schedule Type */}
            <div className='space-y-4'>
              <Label>Delivery Schedule</Label>
              <div className='flex gap-4'>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='immediate'
                    name='scheduleType'
                    value='immediate'
                    checked={formData.scheduleType === 'immediate'}
                    onChange={e => handleInputChange('scheduleType', e.target.value as 'immediate' | 'scheduled')}
                  />
                  <Label htmlFor='immediate'>Send Immediately</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='scheduled'
                    name='scheduleType'
                    value='scheduled'
                    checked={formData.scheduleType === 'scheduled'}
                    onChange={e => handleInputChange('scheduleType', e.target.value as 'immediate' | 'scheduled')}
                  />
                  <Label htmlFor='scheduled'>Schedule for Later</Label>
                </div>
              </div>
            </div>

            {/* Scheduled Date/Time */}
            {formData.scheduleType === 'scheduled' && (
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='scheduledDate'>Scheduled Date</Label>
                  <Input
                    id='scheduledDate'
                    type='date'
                    value={formData.scheduledDate}
                    onChange={e => handleInputChange('scheduledDate', e.target.value)}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='scheduledTime'>Scheduled Time</Label>
                  <Input
                    id='scheduledTime'
                    type='time'
                    value={formData.scheduledTime}
                    onChange={e => handleInputChange('scheduledTime', e.target.value)}
                  />
                </div>
              </div>
            )}

            {/* Delivery Methods */}
            <div className='space-y-4'>
              <Label>Delivery Methods</Label>
              <div className='space-y-3'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='emailNotification'
                    checked={formData.emailNotification}
                    onCheckedChange={checked => handleInputChange('emailNotification', checked as boolean)}
                  />
                  <Label htmlFor='emailNotification'>Email Notification</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='smsNotification'
                    checked={formData.smsNotification}
                    onCheckedChange={checked => handleInputChange('smsNotification', checked as boolean)}
                  />
                  <Label htmlFor='smsNotification'>SMS Notification</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='pushNotification'
                    checked={formData.pushNotification}
                    onCheckedChange={checked => handleInputChange('pushNotification', checked as boolean)}
                  />
                  <Label htmlFor='pushNotification'>Push Notification</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='requiresAcknowledgment'
                    checked={formData.requiresAcknowledgment}
                    onCheckedChange={checked => handleInputChange('requiresAcknowledgment', checked as boolean)}
                  />
                  <Label htmlFor='requiresAcknowledgment'>Requires Acknowledgment</Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href='/dashboard/notifications'>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Creating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                {formData.scheduleType === 'immediate' ? 'Send Notification' : 'Schedule Notification'}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
