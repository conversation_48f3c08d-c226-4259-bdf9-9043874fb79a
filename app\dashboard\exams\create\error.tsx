'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateExamErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateExamError({ error, reset }: CreateExamErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Schedule Exam"
      moduleIcon="📝"
      backHref="/dashboard/exams"
    />
  );
}
