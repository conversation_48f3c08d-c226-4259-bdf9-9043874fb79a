/**
 * Export Utilities - CSV and PDF Export Functions
 *
 * Features:
 * - CSV export with custom headers and data formatting
 * - PDF export with professional styling
 * - File download handling
 * - Data transformation utilities
 */

// CSV Export Utility
export const exportToCSV = (data: any[], filename: string, headers?: string[]) => {
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  // Get headers from first object if not provided
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    // Headers row
    csvHeaders.join(','),
    // Data rows
    ...data.map(row => 
      csvHeaders.map(header => {
        const value = row[header];
        // Handle values that might contain commas or quotes
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    )
  ].join('\n');

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// PDF Export Utility (using html2pdf.js approach)
export const exportToPDF = async (
  data: any[], 
  filename: string, 
  title: string,
  headers?: string[]
) => {
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  // Dynamic import to avoid SSR issues
  const html2pdf = (await import('html2pdf.js')).default;
  
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create HTML content for PDF
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #1f2937; margin-bottom: 10px;">${title}</h1>
        <p style="color: #6b7280; margin: 0;">Generated on ${new Date().toLocaleDateString()}</p>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            ${csvHeaders.map(header => 
              `<th style="border: 1px solid #d1d5db; padding: 12px; text-align: left; font-weight: 600; color: #374151;">
                ${header.charAt(0).toUpperCase() + header.slice(1).replace(/([A-Z])/g, ' $1')}
              </th>`
            ).join('')}
          </tr>
        </thead>
        <tbody>
          ${data.map((row, index) => `
            <tr style="background-color: ${index % 2 === 0 ? '#ffffff' : '#f9fafb'};">
              ${csvHeaders.map(header => 
                `<td style="border: 1px solid #d1d5db; padding: 12px; color: #374151;">
                  ${row[header] || ''}
                </td>`
              ).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div style="margin-top: 30px; text-align: center; color: #6b7280; font-size: 12px;">
        <p>School Management System - Report Generated Automatically</p>
      </div>
    </div>
  `;

  // PDF options
  const options = {
    margin: 1,
    filename: `${filename}.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
  };

  // Generate and download PDF
  try {
    await html2pdf().set(options).from(htmlContent).save();
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Fallback: open in new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.print();
    }
  }
};

// Excel Export Utility (using SheetJS approach)
export const exportToExcel = async (
  data: any[], 
  filename: string, 
  sheetName: string = 'Sheet1'
) => {
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  try {
    // Dynamic import to avoid SSR issues
    const XLSX = await import('xlsx');
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // Generate and download file
    XLSX.writeFile(workbook, `${filename}.xlsx`);
  } catch (error) {
    console.error('Error generating Excel file:', error);
    // Fallback to CSV
    exportToCSV(data, filename);
  }
};

// Data transformation utilities
export const transformDataForExport = (data: any[], transformations?: Record<string, (value: any) => any>) => {
  if (!transformations) return data;
  
  return data.map(row => {
    const transformedRow = { ...row };
    Object.entries(transformations).forEach(([key, transformer]) => {
      if (transformedRow[key] !== undefined) {
        transformedRow[key] = transformer(transformedRow[key]);
      }
    });
    return transformedRow;
  });
};

// Format date for export
export const formatDateForExport = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Format currency for export
export const formatCurrencyForExport = (amount: number) => {
  if (typeof amount !== 'number') return amount;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Format percentage for export
export const formatPercentageForExport = (value: number) => {
  if (typeof value !== 'number') return value;
  return `${value.toFixed(1)}%`;
};

// Common export configurations
export const EXPORT_CONFIGS = {
  students: {
    headers: ['name', 'studentId', 'grade', 'class', 'email', 'phone', 'status'],
    filename: 'students-report',
    title: 'Students Report'
  },
  attendance: {
    headers: ['date', 'studentName', 'class', 'status', 'timeIn', 'timeOut'],
    filename: 'attendance-report',
    title: 'Attendance Report'
  },
  fees: {
    headers: ['studentName', 'feeType', 'amount', 'dueDate', 'paidDate', 'status'],
    filename: 'fees-report',
    title: 'Fee Collection Report'
  },
  exams: {
    headers: ['examName', 'subject', 'date', 'duration', 'totalMarks', 'studentsCount'],
    filename: 'exams-report',
    title: 'Examinations Report'
  },
  academic: {
    headers: ['studentName', 'subject', 'examType', 'marks', 'grade', 'percentage'],
    filename: 'academic-report',
    title: 'Academic Performance Report'
  }
};

// Bulk export function
export const bulkExport = async (
  data: any[], 
  format: 'csv' | 'pdf' | 'excel',
  config: {
    filename: string;
    title: string;
    headers?: string[];
    transformations?: Record<string, (value: any) => any>;
  }
) => {
  const { filename, title, headers, transformations } = config;
  
  // Transform data if needed
  const exportData = transformations ? transformDataForExport(data, transformations) : data;
  
  switch (format) {
    case 'csv':
      exportToCSV(exportData, filename, headers);
      break;
    case 'pdf':
      await exportToPDF(exportData, filename, title, headers);
      break;
    case 'excel':
      await exportToExcel(exportData, filename);
      break;
    default:
      console.error('Unsupported export format:', format);
  }
};
