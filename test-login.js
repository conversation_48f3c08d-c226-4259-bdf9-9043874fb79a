/**
 * Test script to verify login functionality
 * This script tests the updated auth service with form data
 */

const axios = require('axios');

async function testLogin() {
  console.log('🧪 Testing login functionality...');
  
  try {
    // Test 1: Direct backend call with form data
    console.log('\n1️⃣ Testing direct backend call with form data...');
    
    const formData = new URLSearchParams();
    formData.append('username', 'admin');
    formData.append('password', 'admin123');
    
    const response = await axios.post('http://localhost:8000/api/v1/auth/login', formData.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    console.log('✅ Direct backend call successful!');
    console.log('Response:', response.data);
    
    // Test 2: Test the auth service function
    console.log('\n2️⃣ Testing auth service function...');
    
    // Import the auth service (we'll need to mock the api client)
    const mockApi = {
      post: async (url, data, config) => {
        console.log('Mock API called with:', { url, data, config });
        return { data: { access_token: 'mock-token', token_type: 'bearer' } };
      }
    };
    
    // Mock the login function logic
    const testPayload = { username: 'admin', password: 'admin123' };
    const formDataTest = new URLSearchParams();
    
    const username = testPayload.username || testPayload.email;
    if (!username) {
      throw new Error('Username or email is required');
    }
    
    formDataTest.append('username', username);
    formDataTest.append('password', testPayload.password);
    
    const mockResponse = await mockApi.post('/api/v1/auth/login', formDataTest.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    
    console.log('✅ Auth service logic test successful!');
    console.log('Mock response:', mockResponse.data);
    
    console.log('\n🎉 All tests passed! Login functionality should work correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testLogin();
