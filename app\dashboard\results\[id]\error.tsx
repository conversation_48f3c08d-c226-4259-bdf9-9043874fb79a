'use client';

import { <PERSON><PERSON><PERSON>ir<PERSON>, ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ResultDetailError({ error, reset }: ErrorPageProps) {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-2xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href='/dashboard/results'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Results
          </Button>
        </Link>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Result Details Error</h1>
          <p className='text-gray-600 mt-1'>Something went wrong loading the result details</p>
        </div>
      </div>

      <Card>
        <CardContent className='flex flex-col items-center justify-center py-16'>
          <AlertCircle className='w-16 h-16 text-red-500 mb-4' />
          <h3 className='text-lg font-semibold mb-2'>Failed to Load Result Details</h3>
          <p className='text-muted-foreground text-center mb-6 max-w-md'>
            {error.message || 'An unexpected error occurred while loading the result details.'}
          </p>
          <div className='flex gap-4'>
            <Button onClick={reset}>
              <RefreshCw className='mr-2 h-4 w-4' />
              Try Again
            </Button>
            <Link href='/dashboard/results'>
              <Button variant='outline'>
                Back to Results
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
