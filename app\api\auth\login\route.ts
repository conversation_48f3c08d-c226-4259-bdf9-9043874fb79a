import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

// Mock user data for development
const MOCK_USER = {
  id: '1',
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'ADMIN' as const,
};

const MOCK_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123',
};

// Generate a mock JWT token
function generateMockToken(user: typeof MOCK_USER): string {
  const header = { alg: 'HS256', typ: 'JWT' };
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
    iat: Math.floor(Date.now() / 1000),
  };

  // In a real app, you'd use a proper JWT library with a secret
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const signature = btoa(`mock-signature-${user.id}`);

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // Mock authentication - replace with real authentication logic
    if (email !== MOCK_CREDENTIALS.email || password !== MOCK_CREDENTIALS.password) {
      return NextResponse.json({ error: 'Invalid email or password' }, { status: 401 });
    }

    // Generate token
    const token = generateMockToken(MOCK_USER);

    // Create response
    const response = NextResponse.json({
      success: true,
      user: MOCK_USER,
      token,
      message: 'Login successful',
    });

    // Set httpOnly cookie for middleware
    const cookieStore = cookies();
    cookieStore.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
