/**
 * Mock Classes Data
 *
 * Comprehensive mock data for classes with:
 * - Class information and capacity
 * - Teacher assignments
 * - Student enrollment
 * - Room assignments
 * - Active status tracking
 */

import { Class } from '@/schemas/zodSchemas';

// Sample class data
export const mockClasses: Class[] = [
  {
    id: '1',
    name: '9A',
    grade_level: 'Grade 9',
    capacity: 30,
    current_enrollment: 28,
    teacher_name: '<PERSON>',
    room_number: 'Room 101',
    is_active: true,
  },
  {
    id: '2',
    name: '9B',
    grade_level: 'Grade 9',
    capacity: 30,
    current_enrollment: 29,
    teacher_name: '<PERSON>',
    room_number: 'Room 102',
    is_active: true,
  },
  {
    id: '3',
    name: '9C',
    grade_level: 'Grade 9',
    capacity: 30,
    current_enrollment: 27,
    teacher_name: '<PERSON>',
    room_number: 'Room 103',
    is_active: true,
  },
  {
    id: '4',
    name: '10A',
    grade_level: 'Grade 10',
    capacity: 32,
    current_enrollment: 30,
    teacher_name: '<PERSON>',
    room_number: 'Room 201',
    is_active: true,
  },
  {
    id: '5',
    name: '10B',
    grade_level: 'Grade 10',
    capacity: 32,
    current_enrollment: 31,
    teacher_name: '<PERSON>',
    room_number: 'Room 202',
    is_active: true,
  },
  {
    id: '6',
    name: '11A',
    grade_level: 'Grade 11',
    capacity: 28,
    current_enrollment: 26,
    teacher_name: '<PERSON> <PERSON>',
    room_number: 'Room 301',
    is_active: true,
  },
  {
    id: '7',
    name: '11B',
    grade_level: 'Grade 11',
    capacity: 28,
    current_enrollment: 27,
    teacher_name: 'Jennifer Garcia',
    room_number: 'Room 302',
    is_active: true,
  },
  {
    id: '8',
    name: '12A',
    grade_level: 'Grade 12',
    capacity: 25,
    current_enrollment: 24,
    teacher_name: 'Christopher Lee',
    room_number: 'Room 401',
    is_active: true,
  },
  {
    id: '9',
    name: '12B',
    grade_level: 'Grade 12',
    capacity: 25,
    current_enrollment: 23,
    teacher_name: 'Amanda Taylor',
    room_number: 'Room 402',
    is_active: true,
  },
  {
    id: '10',
    name: '8A',
    grade_level: 'Grade 8',
    capacity: 28,
    current_enrollment: 26,
    teacher_name: 'Mark Thompson',
    room_number: 'Room 001',
    is_active: true,
  },
];

// Available grade levels
export const mockGradeLevels = [
  'All Grades',
  'Grade 8',
  'Grade 9',
  'Grade 10',
  'Grade 11',
  'Grade 12',
];

// Legacy exports for compatibility
export const mockClassGrades = mockGradeLevels;

// Available sections
export const mockClassSections = ['All Sections', 'A', 'B', 'C'];

// Available academic years
export const mockAcademicYears = ['All Years', '2023-2024', '2024-2025', '2025-2026'];

// Class status options
export const mockClassStatuses = ['All Statuses', 'ACTIVE', 'INACTIVE'];

// Helper functions
export const filterMockClasses = (filters: {
  search?: string;
  grade?: string;
  status?: string;
}) => {
  let filtered = [...mockClasses];

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(
      cls =>
        cls.name?.toLowerCase().includes(searchLower) ||
        cls.teacher_name?.toLowerCase().includes(searchLower) ||
        cls.room_number?.toLowerCase().includes(searchLower)
    );
  }

  if (filters.grade && filters.grade !== 'All Grades') {
    filtered = filtered.filter(cls => cls.grade_level === filters.grade);
  }

  if (filters.status && filters.status !== 'All Statuses') {
    const isActive = filters.status === 'ACTIVE';
    filtered = filtered.filter(cls => cls.is_active === isActive);
  }

  return filtered;
};

export const paginateMockClasses = (
  classes: typeof mockClasses,
  page: number = 1,
  pageSize: number = 10
) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    data: classes.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      total: classes.length,
      totalPages: Math.ceil(classes.length / pageSize),
      hasNext: endIndex < classes.length,
      hasPrev: page > 1,
    },
  };
};

// Class statistics
export const mockClassStats = {
  total: mockClasses.length,
  active: mockClasses.filter(c => c.is_active).length,
  inactive: mockClasses.filter(c => !c.is_active).length,
  totalCapacity: mockClasses.reduce((sum, c) => sum + c.capacity, 0),
  totalEnrollment: mockClasses.reduce((sum, c) => sum + (c.current_enrollment || 0), 0),
  occupancyRate: Math.round(
    (mockClasses.reduce((sum, c) => sum + (c.current_enrollment || 0), 0) /
      mockClasses.reduce((sum, c) => sum + c.capacity, 0)) *
      100
  ),
  averageClassSize: Math.round(
    mockClasses.reduce((sum, c) => sum + (c.current_enrollment || 0), 0) / mockClasses.length
  ),
};

// Get class by ID
export const getClassById = (id: string) => {
  return mockClasses.find(cls => cls.id === id);
};

// Get classes by grade level
export const getClassesByGrade = (gradeLevel: string) => {
  return mockClasses.filter(cls => cls.grade_level === gradeLevel);
};

// Get classes by teacher
export const getClassesByTeacher = (teacherName: string) => {
  return mockClasses.filter(cls => cls.teacher_name === teacherName);
};
