<!DOCTYPE html>
<html>
<head>
    <title>Frontend-Backend Integration Test</title>
</head>
<body>
    <h1>Frontend-Backend Integration Test</h1>
    <div id="results"></div>

    <script>
        async function testBackendConnection() {
            const results = document.getElementById('results');
            
            // Test 1: Check if backend is running
            try {
                results.innerHTML += '<h2>Test 1: Backend Health Check</h2>';
                const response = await fetch('http://127.0.0.1:8000/docs');
                results.innerHTML += `<p>✅ Backend is running! Status: ${response.status}</p>`;
            } catch (error) {
                results.innerHTML += `<p>❌ Backend connection failed: ${error.message}</p>`;
                return;
            }

            // Test 2: Test students endpoint (expect 401)
            try {
                results.innerHTML += '<h2>Test 2: Students Endpoint (No Auth)</h2>';
                const response = await fetch('http://127.0.0.1:8000/api/v1/students?page=1&size=5');
                results.innerHTML += `<p>Status: ${response.status}</p>`;
                
                if (response.status === 401) {
                    results.innerHTML += '<p>✅ Expected 401 - Authentication required</p>';
                } else {
                    const text = await response.text();
                    results.innerHTML += `<p>Response: ${text}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p>❌ Students endpoint failed: ${error.message}</p>`;
            }

            // Test 3: Test Next.js proxy
            try {
                results.innerHTML += '<h2>Test 3: Next.js Proxy (/api/students)</h2>';
                const response = await fetch('/api/students?page=1&size=5');
                results.innerHTML += `<p>Status: ${response.status}</p>`;
                
                if (response.status === 401) {
                    results.innerHTML += '<p>✅ Proxy working - Authentication required</p>';
                } else {
                    const text = await response.text();
                    results.innerHTML += `<p>Response: ${text}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p>❌ Next.js proxy failed: ${error.message}</p>`;
            }

            // Test 4: Test with mock auth header
            try {
                results.innerHTML += '<h2>Test 4: With Mock Auth Header</h2>';
                const response = await fetch('/api/students?page=1&size=5', {
                    headers: {
                        'Authorization': 'Bearer mock-token-for-testing'
                    }
                });
                results.innerHTML += `<p>Status: ${response.status}</p>`;
                const text = await response.text();
                results.innerHTML += `<p>Response: ${text}</p>`;
            } catch (error) {
                results.innerHTML += `<p>❌ Mock auth test failed: ${error.message}</p>`;
            }
        }

        // Run tests when page loads
        testBackendConnection();
    </script>
</body>
</html>
