/**
 * List Card Component
 * 
 * Composable card component for displaying list items with consistent styling
 */

import { ReactNode } from 'react';
import { LucideIcon, MoreHorizontal } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

export interface ListCardAction {
  label: string;
  icon?: LucideIcon;
  onClick: () => void;
  variant?: 'default' | 'destructive';
}

export interface ListCardProps {
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: {
    src?: string;
    fallback: string;
    icon?: LucideIcon;
  };
  status?: {
    label: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  };
  metadata?: Array<{
    label: string;
    value: string | ReactNode;
  }>;
  actions?: ListCardAction[];
  className?: string;
  onClick?: () => void;
}

const statusVariants = {
  success: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  default: 'bg-slate-100 text-slate-800 border-slate-200',
  secondary: 'bg-slate-100 text-slate-600 border-slate-200',
  destructive: 'bg-red-100 text-red-800 border-red-200',
  outline: 'bg-transparent border-slate-200',
};

export function ListCard({
  title,
  subtitle,
  description,
  avatar,
  status,
  metadata,
  actions,
  className,
  onClick,
}: ListCardProps) {
  return (
    <Card className={cn(
      'border-0 shadow-sm hover:shadow-md transition-all duration-200',
      onClick && 'cursor-pointer hover:scale-[1.02]',
      className
    )}>
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          {/* Avatar */}
          {avatar && (
            <div className="flex-shrink-0">
              {avatar.icon ? (
                <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center">
                  <avatar.icon className="h-6 w-6 text-slate-600" />
                </div>
              ) : (
                <Avatar className="h-12 w-12">
                  <AvatarImage src={avatar.src} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                    {avatar.fallback}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          )}

          {/* Content */}
          <div className="flex-1 min-w-0" onClick={onClick}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold text-slate-900 leading-tight">
                  {title}
                </h3>
                {subtitle && (
                  <p className="text-sm text-slate-600 mt-1">
                    {subtitle}
                  </p>
                )}
                {description && (
                  <p className="text-sm text-slate-500 mt-2 leading-relaxed">
                    {description}
                  </p>
                )}
              </div>

              {/* Status Badge */}
              {status && (
                <Badge 
                  variant={status.variant as any}
                  className={cn(
                    'ml-3 flex-shrink-0',
                    status.variant === 'success' && statusVariants.success,
                    status.variant === 'warning' && statusVariants.warning
                  )}
                >
                  {status.label}
                </Badge>
              )}
            </div>

            {/* Metadata */}
            {metadata && metadata.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4 pt-4 border-t border-slate-100">
                {metadata.map((metadataItem, index) => (
                  <div key={index} className="flex flex-col">
                    <span className="text-xs text-slate-500 font-medium uppercase tracking-wide">
                      {metadataItem.label}
                    </span>
                    <span className="text-sm text-slate-900 font-medium mt-1">
                      {metadataItem.value}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          {actions && actions.length > 0 && (
            <div className="flex-shrink-0">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {actions.map((action, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={action.onClick}
                      className={cn(
                        action.variant === 'destructive' && 'text-red-600 focus:text-red-600'
                      )}
                    >
                      {action.icon && <action.icon className="h-4 w-4 mr-2" />}
                      {action.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * List Card Grid Component
 * 
 * Container for multiple list cards with responsive grid layout
 */
export interface ListCardGridProps {
  cards: ListCardProps[];
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export function ListCardGrid({ 
  cards, 
  columns = 1, 
  className 
}: ListCardGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  return (
    <div className={cn(
      'grid gap-6',
      gridCols[columns],
      className
    )}>
      {cards.map((cardProps, index) => (
        <ListCard key={index} {...cardProps} />
      ))}
    </div>
  );
}
