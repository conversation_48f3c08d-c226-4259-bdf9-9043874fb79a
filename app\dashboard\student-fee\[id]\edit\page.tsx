'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Dummy data - replace with API call later
const mockFeeRecords = [
  {
    id: 1,
    studentName: 'John <PERSON>e',
    studentId: 'STU001',
    class: 'Grade 10',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Paid',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    class: 'Grade 9',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Pending',
  },
  {
    id: 3,
    studentName: '<PERSON>',
    studentId: 'STU003',
    class: 'Grade 11',
    feeType: 'Lab Fee',
    amount: 300,
    dueDate: '2024-01-20',
    status: 'Overdue',
  },
];

export default function EditStudentFeePage() {
  const { id } = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<any>(null);

  useEffect(() => {
    const record = mockFeeRecords.find(r => r.id === Number(id));
    if (record) {
      setFormData(record);
    }
  }, [id]);

  if (!formData) {
    return <p className='p-6'>Fee record not found</p>;
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    console.log('Updated fee record:', formData);
    router.push('/dashboard/student-fee');
  };

  return (
    <div className='container mx-auto p-6 max-w-2xl'>
      <Card>
        <CardHeader>
          <CardTitle>Edit Fee Record</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div>
            <Label>Student Name</Label>
            <Input
              value={formData.studentName}
              onChange={e => handleChange('studentName', e.target.value)}
            />
          </div>
          <div>
            <Label>Student ID</Label>
            <Input
              value={formData.studentId}
              onChange={e => handleChange('studentId', e.target.value)}
            />
          </div>
          <div>
            <Label>Class</Label>
            <Input value={formData.class} onChange={e => handleChange('class', e.target.value)} />
          </div>
          <div>
            <Label>Fee Type</Label>
            <Input
              value={formData.feeType}
              onChange={e => handleChange('feeType', e.target.value)}
            />
          </div>
          <div>
            <Label>Amount</Label>
            <Input
              type='number'
              value={formData.amount}
              onChange={e => handleChange('amount', e.target.value)}
            />
          </div>
          <div>
            <Label>Due Date</Label>
            <Input
              type='date'
              value={formData.dueDate}
              onChange={e => handleChange('dueDate', e.target.value)}
            />
          </div>
          <div>
            <Label>Status</Label>
            <Select value={formData.status} onValueChange={val => handleChange('status', val)}>
              <SelectTrigger>
                <SelectValue placeholder='Select Status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='Paid'>Paid</SelectItem>
                <SelectItem value='Pending'>Pending</SelectItem>
                <SelectItem value='Overdue'>Overdue</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='flex justify-end gap-2 pt-4'>
            <Button variant='outline' onClick={() => router.push('/dashboard/student-fee')}>
              Cancel
            </Button>
            <Button className='bg-blue-600 hover:bg-blue-700' onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
