'use client';

import { Calendar, Clock, FileText, Plus, Search, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Mock data for demonstration
const mockExamStats = {
  totalExams: 25,
  upcomingExams: 8,
  completedExams: 17,
  totalStudents: 450,
};

const mockExams = [
  {
    id: 1,
    title: 'Mathematics Final Exam',
    subject: 'Mathematics',
    date: '2024-02-15',
    time: '09:00 AM - 12:00 PM',
    duration: '3 hours',
    totalMarks: 100,
    students: 45,
    status: 'Upcoming',
    type: 'Final',
  },
  {
    id: 2,
    title: 'Physics Mid-term',
    subject: 'Physics',
    date: '2024-02-10',
    time: '02:00 PM - 04:00 PM',
    duration: '2 hours',
    totalMarks: 75,
    students: 38,
    status: 'Upcoming',
    type: 'Mid-term',
  },
  {
    id: 3,
    title: 'English Literature Quiz',
    subject: 'English',
    date: '2024-01-20',
    time: '10:00 AM - 11:00 AM',
    duration: '1 hour',
    totalMarks: 50,
    students: 52,
    status: 'Completed',
    type: 'Quiz',
  },
];

export default function ExamsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [typeFilter, setTypeFilter] = useState('All');

  const handleAddExam = () => {
    router.push('/dashboard/exams/create');
  };

  const handleViewExam = (examId: number) => {
    router.push(`/dashboard/exams/${examId}`);
  };

  const handleEditExam = (examId: number) => {
    router.push(`/dashboard/exams/${examId}/edit`);
  };

  const statsCards = [
    {
      title: 'Total Exams',
      value: mockExamStats.totalExams.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+3%',
      changeType: 'positive' as const,
    },
    {
      title: 'Upcoming Exams',
      value: mockExamStats.upcomingExams.toString(),
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      title: 'Completed Exams',
      value: mockExamStats.completedExams.toString(),
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Students',
      value: mockExamStats.totalStudents.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+1%',
      changeType: 'positive' as const,
    },
  ];

  const filteredExams = mockExams.filter(exam => {
    const matchesSearch =
      exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All' || exam.status === statusFilter;
    const matchesType = typeFilter === 'All' || exam.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Upcoming':
        return 'text-orange-600 bg-orange-50';
      case 'Completed':
        return 'text-green-600 bg-green-50';
      case 'In Progress':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Final':
        return 'text-red-600 bg-red-50';
      case 'Mid-term':
        return 'text-blue-600 bg-blue-50';
      case 'Quiz':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <PageHeader
        title='Exam Management'
        description='Schedule and manage examinations and assessments'
        icon={FileText}
        badge={{ label: 'Demo Data', variant: 'outline' }}
        actions={[
          {
            label: 'Schedule Exam',
            icon: Plus,
            onClick: handleAddExam,
          },
        ]}
      />

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search exams...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Status</SelectItem>
                <SelectItem value='Upcoming'>Upcoming</SelectItem>
                <SelectItem value='Completed'>Completed</SelectItem>
                <SelectItem value='In Progress'>In Progress</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Types</SelectItem>
                <SelectItem value='Final'>Final</SelectItem>
                <SelectItem value='Mid-term'>Mid-term</SelectItem>
                <SelectItem value='Quiz'>Quiz</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex items-center text-sm text-muted-foreground'>
              Showing {filteredExams.length} of {mockExams.length} exams
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exams List */}
      <div className='grid grid-cols-1 gap-4'>
        {filteredExams.map(exam => (
          <Card key={exam.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex items-start space-x-4'>
                  <div className='w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center'>
                    <FileText className='w-6 h-6 text-red-600' />
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-2'>
                      <h3 className='font-semibold'>{exam.title}</h3>
                      <Badge variant='outline' className={getTypeColor(exam.type)}>
                        {exam.type}
                      </Badge>
                    </div>
                    <p className='text-sm text-muted-foreground mb-2'>{exam.subject}</p>
                    <div className='flex flex-wrap gap-4 text-sm text-muted-foreground'>
                      <div className='flex items-center'>
                        <Calendar className='w-4 h-4 mr-1' />
                        {exam.date}
                      </div>
                      <div className='flex items-center'>
                        <Clock className='w-4 h-4 mr-1' />
                        {exam.time}
                      </div>
                      <div className='flex items-center'>
                        <Users className='w-4 h-4 mr-1' />
                        {exam.students} students
                      </div>
                    </div>
                  </div>
                </div>

                <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
                  <div className='text-center sm:text-right'>
                    <p className='font-semibold'>{exam.totalMarks} marks</p>
                    <p className='text-sm text-muted-foreground'>{exam.duration}</p>
                  </div>

                  <div className='text-center sm:text-right'>
                    <Badge variant='outline' className={getStatusColor(exam.status)}>
                      {exam.status}
                    </Badge>
                  </div>

                  <div className='flex space-x-2'>
                    <Button variant='outline' size='sm' onClick={() => handleViewExam(exam.id)}>
                      View
                    </Button>
                    <Button variant='outline' size='sm' onClick={() => handleEditExam(exam.id)}>
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredExams.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>📝</div>
            <h3 className='text-lg font-semibold mb-2'>No Exams Found</h3>
            <p className='text-muted-foreground text-center mb-4'>
              {searchTerm || statusFilter !== 'All' || typeFilter !== 'All'
                ? 'No exams match your current filters.'
                : 'There are no exams scheduled yet.'}
            </p>
            <Button onClick={handleAddExam}>
              <Plus className='mr-2 h-4 w-4' />
              Schedule First Exam
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
