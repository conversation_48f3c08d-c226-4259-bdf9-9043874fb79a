import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className='container mx-auto p-6 max-w-2xl space-y-4'>
      <Skeleton className='h-8 w-48' />
      <div className='space-y-4'>
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className='space-y-2'>
            <Skeleton className='h-4 w-32' />
            <Skeleton className='h-10 w-full' />
          </div>
        ))}
      </div>
    </div>
  );
}
