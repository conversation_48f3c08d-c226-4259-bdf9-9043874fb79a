/**
 * ListCard Component - Reusable Card for Lists
 *
 * Features:
 * - Flexible layout (vertical/horizontal)
 * - Avatar support with fallback
 * - Badge/status indicators
 * - Metadata with icons
 * - Action buttons with variants
 * - Hover effects and animations
 * - Responsive design
 * - Accessibility support
 */

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { LucideIcon, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

// Types
interface AvatarConfig {
  src?: string | null;
  fallback: string;
  alt?: string;
}

interface BadgeConfig {
  text: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
}

interface MetadataItem {
  icon: LucideIcon;
  text: string;
  href?: string | undefined;
  onClick?: (() => void) | undefined;
}

interface ActionItem {
  icon: LucideIcon;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | undefined;
  disabled?: boolean | undefined;
}

interface ListCardProps {
  // Content
  title: string;
  subtitle?: string | undefined;
  description?: string | undefined;

  // Visual elements
  avatar?: AvatarConfig | undefined;
  badge?: BadgeConfig | undefined;
  metadata?: MetadataItem[] | undefined;

  // Actions
  actions?: ActionItem[] | undefined;
  href?: string | undefined;
  onClick?: (() => void) | undefined;

  // Layout
  layout?: 'vertical' | 'horizontal' | undefined;
  size?: 'sm' | 'md' | 'lg' | undefined;

  // Styling
  className?: string | undefined;
  contentClassName?: string | undefined;

  // States
  loading?: boolean | undefined;
  disabled?: boolean | undefined;
  selected?: boolean | undefined;

  // Accessibility
  'aria-label'?: string;
  'aria-describedby'?: string;
}

const sizeClasses = {
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
};

const badgeVariants = {
  success: 'bg-green-100 text-green-800 hover:bg-green-200',
  warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
};

export function ListCard({
  title,
  subtitle,
  description,
  avatar,
  badge,
  metadata = [],
  actions = [],
  href,
  onClick,
  layout = 'vertical',
  size = 'md',
  className,
  contentClassName,
  loading = false,
  disabled = false,
  selected = false,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
}: ListCardProps) {
  const isClickable = !!(href || onClick);
  const isHorizontal = layout === 'horizontal';

  const cardContent = (
    <Card
      className={cn(
        'group transition-all duration-200 border-0 shadow-sm hover:shadow-md',
        {
          'cursor-pointer hover:scale-[1.02]': isClickable && !disabled,
          'opacity-50 cursor-not-allowed': disabled,
          'ring-2 ring-blue-500 ring-offset-2': selected,
          'animate-pulse': loading,
        },
        className
      )}
      role={isClickable ? 'button' : undefined}
      tabIndex={isClickable && !disabled ? 0 : undefined}
      aria-label={ariaLabel || title}
      aria-describedby={ariaDescribedBy}
      onKeyDown={e => {
        if (isClickable && !disabled && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          onClick?.();
        }
      }}
    >
      <CardContent
        className={cn(
          sizeClasses[size],
          {
            'flex items-center space-x-4': isHorizontal,
            'space-y-3': !isHorizontal,
          },
          contentClassName
        )}
      >
        {/* Avatar */}
        {avatar && (
          <div
            className={cn('flex-shrink-0', {
              'mb-0': isHorizontal,
              'mb-3': !isHorizontal,
            })}
          >
            <Avatar
              className={cn(
                'transition-transform group-hover:scale-105',
                size === 'sm' ? 'w-8 h-8' : size === 'md' ? 'w-12 h-12' : 'w-16 h-16'
              )}
            >
              <AvatarImage src={avatar.src || undefined} alt={avatar.alt || title} />
              <AvatarFallback className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold'>
                {avatar.fallback}
              </AvatarFallback>
            </Avatar>
          </div>
        )}

        {/* Content */}
        <div
          className={cn('flex-1 min-w-0', {
            'space-y-1': !isHorizontal,
          })}
        >
          {/* Header */}
          <div
            className={cn('flex items-start justify-between', {
              'flex-col space-y-1': !isHorizontal,
              'items-center': isHorizontal,
            })}
          >
            <div className='flex-1 min-w-0'>
              <h3
                className={cn(
                  'font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors',
                  size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg'
                )}
              >
                {title}
              </h3>

              {subtitle && (
                <p className={cn('text-gray-600 truncate', size === 'sm' ? 'text-xs' : 'text-sm')}>
                  {subtitle}
                </p>
              )}
            </div>

            {/* Badge */}
            {badge && (
              <Badge
                variant={
                  badge.variant === 'success' || badge.variant === 'warning'
                    ? 'secondary'
                    : badge.variant
                }
                className={cn(
                  'flex-shrink-0',
                  badge.variant === 'success' && badgeVariants.success,
                  badge.variant === 'warning' && badgeVariants.warning,
                  {
                    'ml-2': isHorizontal,
                    'mt-1': !isHorizontal,
                  }
                )}
              >
                {badge.text}
              </Badge>
            )}
          </div>

          {/* Description */}
          {description && (
            <p
              className={cn('text-gray-600 line-clamp-2', size === 'sm' ? 'text-xs' : 'text-sm', {
                'mt-1': !isHorizontal,
              })}
            >
              {description}
            </p>
          )}

          {/* Metadata */}
          {metadata.length > 0 && (
            <div
              className={cn('flex flex-wrap gap-3', size === 'sm' ? 'text-xs' : 'text-sm', {
                'mt-2': !isHorizontal,
                'mt-1': isHorizontal,
              })}
            >
              {metadata.map((item, index) => {
                const MetadataContent = (
                  <div
                    key={index}
                    className={cn('flex items-center space-x-1 text-gray-500 transition-colors', {
                      'hover:text-blue-600 cursor-pointer': item.href || item.onClick,
                    })}
                    onClick={item.onClick}
                  >
                    <item.icon className='w-3 h-3 flex-shrink-0' />
                    <span className='truncate'>{item.text}</span>
                  </div>
                );

                if (item.href) {
                  return (
                    <Link key={index} href={item.href} className='hover:text-blue-600'>
                      {MetadataContent}
                    </Link>
                  );
                }

                return MetadataContent;
              })}
            </div>
          )}
        </div>

        {/* Actions */}
        {actions.length > 0 && (
          <div
            className={cn('flex-shrink-0', {
              'ml-4': isHorizontal,
              'mt-3 flex justify-end': !isHorizontal,
            })}
          >
            {actions.length === 1 && actions[0] ? (
              <Button
                variant={actions[0].variant || 'ghost'}
                size='sm'
                onClick={e => {
                  e.stopPropagation();
                  if (actions[0]) {
                    actions[0].onClick();
                  }
                }}
                disabled={actions[0]?.disabled}
                className='h-8 w-8 p-0'
              >
                {actions[0] && React.createElement(actions[0].icon, { className: 'w-4 h-4' })}
                <span className='sr-only'>{actions[0]?.label}</span>
              </Button>
            ) : (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-8 w-8 p-0'
                    onClick={e => e.stopPropagation()}
                  >
                    <MoreHorizontal className='w-4 h-4' />
                    <span className='sr-only'>Open menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='w-48'>
                  {actions.map((action, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={e => {
                        e.stopPropagation();
                        action.onClick();
                      }}
                      disabled={action.disabled || false}
                      className={cn({
                        'text-red-600 focus:text-red-600': action.variant === 'destructive',
                      })}
                    >
                      <action.icon className='w-4 h-4 mr-2' />
                      {action.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Wrap with link if href is provided
  if (href && !disabled) {
    return (
      <Link href={href} className='block'>
        {cardContent}
      </Link>
    );
  }

  // Wrap with click handler if onClick is provided
  if (onClick && !disabled) {
    return (
      <div onClick={onClick} className='block'>
        {cardContent}
      </div>
    );
  }

  return cardContent;
}

// Loading skeleton for ListCard
export function ListCardSkeleton({
  layout = 'vertical',
  size = 'md',
}: {
  layout?: 'vertical' | 'horizontal';
  size?: 'sm' | 'md' | 'lg';
}) {
  const isHorizontal = layout === 'horizontal';

  return (
    <Card className='border-0 shadow-sm'>
      <CardContent
        className={cn(sizeClasses[size], {
          'flex items-center space-x-4': isHorizontal,
          'space-y-3': !isHorizontal,
        })}
      >
        {/* Avatar skeleton */}
        <div
          className={cn('flex-shrink-0 animate-pulse', {
            'mb-0': isHorizontal,
            'mb-3': !isHorizontal,
          })}
        >
          <div
            className={cn(
              'bg-gray-200 rounded-full',
              size === 'sm' ? 'w-8 h-8' : size === 'md' ? 'w-12 h-12' : 'w-16 h-16'
            )}
          />
        </div>

        {/* Content skeleton */}
        <div className='flex-1 min-w-0 space-y-2 animate-pulse'>
          <div className='flex items-center justify-between'>
            <div className='space-y-1 flex-1'>
              <div className='h-4 bg-gray-200 rounded w-3/4' />
              <div className='h-3 bg-gray-200 rounded w-1/2' />
            </div>
            <div className='h-5 bg-gray-200 rounded w-16 ml-2' />
          </div>

          <div className='h-3 bg-gray-200 rounded w-full' />
          <div className='h-3 bg-gray-200 rounded w-2/3' />

          <div className='flex space-x-3 mt-2'>
            <div className='h-3 bg-gray-200 rounded w-20' />
            <div className='h-3 bg-gray-200 rounded w-24' />
            <div className='h-3 bg-gray-200 rounded w-16' />
          </div>
        </div>

        {/* Actions skeleton */}
        <div className='flex-shrink-0 animate-pulse'>
          <div className='h-8 w-8 bg-gray-200 rounded' />
        </div>
      </CardContent>
    </Card>
  );
}

export default ListCard;
