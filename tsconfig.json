{
  "compilerOptions": {
    // ===== LIBRARY & COMPATIBILITY =====
    "lib": ["dom", "dom.iterable", "es2022"],
    "target": "ES2020",
    "allowJs": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    // ===== STRICT TYPE CHECKING =====
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "exactOptionalPropertyTypes": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,

    // ===== MODULE RESOLUTION =====
    "module": "esnext",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,

    // ===== EMIT =====
    "noEmit": true,
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    "downlevelIteration": true,

    // ===== JSX =====
    "jsx": "preserve",
    "jsxImportSource": "react",

    // ===== ADVANCED =====
    "incremental": true,
    "tsBuildInfoFile": ".next/cache/tsbuildinfo.json",
    "plugins": [
      {
        "name": "next"
      }
    ],

    // ===== PATH MAPPING =====
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/hooks/*": ["./hooks/*"],
      "@/types/*": ["./types/*"],
      "@/schemas/*": ["./schemas/*"],
      "@/api/*": ["./api/*"],
      "@/constants/*": ["./constants/*"],
      "@/app/*": ["./app/*"],
      "@/stores/*": ["./stores/*"],
      "@/utils/*": ["./utils/*"],
      "@/config/*": ["./config/*"]
    }
  },
  "include": ["next-env.d.ts", "types/jest-dom.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
