/**
 * Fees API Adapter
 *
 * Comprehensive API layer for fees management with:
 * - Fee Types CRUD operations
 * - Class-wise Fee Schedule management
 * - Late Fee & Discount Rules
 * - Opening Balance operations
 * - CSV import/export functionality
 * - Mock data for development
 */

import { apiUtils } from '@/api/apiClient';

import type {
  BulkFeeOperation,
  ClassFeeSchedule,
  CSVImport,
  CSVImportResult,
  DiscountRule,
  FeeCalculationPreview,
  FeeRules,
  FeesSettings,
  FeeType,
  LateFeeRule,
  OpeningBalance,
  OpeningBalanceEntry,
} from '../schemas/fees.schemas';

// API endpoints
const ENDPOINTS = {
  // Fee Types
  FEE_TYPES: '/api/settings/fees/types',
  FEE_TYPE: (id: string) => `/api/settings/fees/types/${id}`,

  // Class Fee Schedule
  FEE_SCHEDULE: '/api/settings/fees/schedule',
  FEE_SCHEDULE_IMPORT: '/api/settings/fees/schedule/import',
  FEE_SCHEDULE_EXPORT: '/api/settings/fees/schedule/export',
  FEE_SCHEDULE_BULK: '/api/settings/fees/schedule/bulk',

  // Fee Rules
  FEE_RULES: '/api/settings/fees/rules',
  LATE_FEE_RULE: '/api/settings/fees/rules/late-fee',
  DISCOUNT_RULES: '/api/settings/fees/rules/discounts',
  DISCOUNT_RULE: (id: string) => `/api/settings/fees/rules/discounts/${id}`,

  // Opening Balance
  OPENING_BALANCE: '/api/settings/fees/opening-balance',
  OPENING_BALANCE_ENTRY: (id: string) => `/api/settings/fees/opening-balance/${id}`,

  // Calculations
  CALCULATE_FEE: '/api/settings/fees/calculate',
} as const;

// Mock data for development
const mockFeeTypes: FeeType[] = [
  {
    id: '1',
    name: 'Tuition Fee',
    description: 'Monthly tuition charges',
    category: 'ACADEMIC',
    isActive: true,
    sortOrder: 0,
    isRequired: true,
  },
  {
    id: '2',
    name: 'Transport Fee',
    description: 'School bus transportation',
    category: 'TRANSPORT',
    isActive: true,
    sortOrder: 1,
    isRequired: false,
  },
];

const mockClassFeeSchedule: ClassFeeSchedule = {
  rows: [
    {
      id: '1',
      className: 'Nursery',
      admissionFee: 5000,
      monthlyFee: 2500,
      examFee: 500,
      securityDeposit: 3000,
      certificateFee: 200,
      version: 1,
    },
    {
      id: '2',
      className: 'LKG',
      admissionFee: 5500,
      monthlyFee: 2800,
      examFee: 600,
      securityDeposit: 3500,
      certificateFee: 250,
      version: 1,
    },
  ],
  version: 1,
  lastSavedAt: new Date().toISOString(),
};

const mockFeeRules: FeeRules = {
  globalLateFeeRule: {
    id: '1',
    type: 'PERCENTAGE',
    value: 5,
    graceDays: 7,
    maxAmount: 1000,
    isActive: true,
  },
  globalDiscountRules: [
    {
      id: '1',
      name: 'Sibling Discount',
      type: 'PERCENTAGE',
      value: 10,
      applicableToFees: ['tuition'],
      conditions: {
        siblingDiscount: true,
        earlyPayment: false,
      },
      isActive: true,
    },
  ],
  classSpecificRules: [],
};

const mockOpeningBalance: OpeningBalance = {
  entries: [
    {
      id: '1',
      type: 'CLASS',
      targetId: 'class-10',
      targetName: 'Class 10',
      amount: -5000,
      description: 'Previous year adjustment',
      effectiveDate: '2024-04-01',
      createdBy: 'admin',
      createdAt: '2024-03-15T10:00:00Z',
    },
  ],
  totalDebit: 2500,
  totalCredit: 5000,
  netBalance: -2500,
};

// Fees API functions
export const feesApi = {
  // Fee Types Management
  getFeeTypes: async (): Promise<FeeType[]> => {
    try {
      const response = await apiClient.get(ENDPOINTS.FEE_TYPES);
      return response.data;
    } catch (_error) {
      console.warn('Using mock fee types data');
      return mockFeeTypes;
    }
  },

  createFeeType: async (data: Omit<FeeType, 'id'>): Promise<FeeType> => {
    try {
      const response = await apiClient.post(ENDPOINTS.FEE_TYPES, data);
      return response.data;
    } catch (_error) {
      console.warn('Mock create - returning new fee type');
      return { ...data, id: Date.now().toString() };
    }
  },

  updateFeeType: async (id: string, data: Partial<FeeType>): Promise<FeeType> => {
    try {
      const response = await apiClient.put(ENDPOINTS.FEE_TYPE(id), data);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated fee type');
      const existing = mockFeeTypes.find(ft => ft.id === id);
      return { ...existing!, ...data };
    }
  },

  deleteFeeType: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(ENDPOINTS.FEE_TYPE(id));
    } catch (_error) {
      console.warn('Mock delete - fee type removed');
    }
  },

  reorderFeeTypes: async (feeTypes: FeeType[]): Promise<FeeType[]> => {
    try {
      const response = await apiClient.put(ENDPOINTS.FEE_TYPES, { feeTypes });
      return response.data;
    } catch (_error) {
      console.warn('Mock reorder - returning reordered types');
      return feeTypes;
    }
  },

  // Class Fee Schedule Management
  getClassFeeSchedule: async (): Promise<ClassFeeSchedule> => {
    try {
      const response = await apiClient.get(ENDPOINTS.FEE_SCHEDULE);
      return response.data;
    } catch (_error) {
      console.warn('Using mock class fee schedule data');
      return mockClassFeeSchedule;
    }
  },

  updateClassFeeSchedule: async (schedule: ClassFeeSchedule): Promise<ClassFeeSchedule> => {
    try {
      const response = await apiClient.put(ENDPOINTS.FEE_SCHEDULE, schedule);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated schedule');
      return {
        ...schedule,
        version: schedule.version + 1,
        lastSavedAt: new Date().toISOString(),
      };
    }
  },

  bulkUpdateClassFees: async (operation: BulkFeeOperation): Promise<ClassFeeSchedule> => {
    try {
      const response = await apiClient.post(ENDPOINTS.FEE_SCHEDULE_BULK, operation);
      return response.data;
    } catch (_error) {
      console.warn('Mock bulk update - returning updated schedule');
      return mockClassFeeSchedule;
    }
  },

  importClassFeeSchedule: async (csvData: CSVImport): Promise<CSVImportResult> => {
    try {
      const response = await apiClient.post(ENDPOINTS.FEE_SCHEDULE_IMPORT, csvData);
      return response.data;
    } catch (_error) {
      console.warn('Mock CSV import');
      return {
        validRows: [],
        errors: [],
        summary: {
          total: csvData.data.length,
          valid: csvData.data.length,
          errors: 0,
          duplicates: 0,
        },
      };
    }
  },

  exportClassFeeSchedule: async (): Promise<Blob> => {
    try {
      const response = await apiClient.get(ENDPOINTS.FEE_SCHEDULE_EXPORT, {
        responseType: 'blob',
      });
      return response.data;
    } catch (_error) {
      console.warn('Mock CSV export');
      const csvContent =
        'Class,Admission Fee,Monthly Fee,Exam Fee,Security Deposit,Certificate Fee\n';
      return new Blob([csvContent], { type: 'text/csv' });
    }
  },

  // Fee Rules Management
  getFeeRules: async (): Promise<FeeRules> => {
    try {
      const response = await apiClient.get(ENDPOINTS.FEE_RULES);
      return response.data;
    } catch (_error) {
      console.warn('Using mock fee rules data');
      return mockFeeRules;
    }
  },

  updateLateFeeRule: async (rule: LateFeeRule): Promise<LateFeeRule> => {
    try {
      const response = await apiClient.put(ENDPOINTS.LATE_FEE_RULE, rule);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated late fee rule');
      return rule;
    }
  },

  getDiscountRules: async (): Promise<DiscountRule[]> => {
    try {
      const response = await apiClient.get(ENDPOINTS.DISCOUNT_RULES);
      return response.data;
    } catch (_error) {
      console.warn('Using mock discount rules data');
      return mockFeeRules.globalDiscountRules;
    }
  },

  createDiscountRule: async (rule: Omit<DiscountRule, 'id'>): Promise<DiscountRule> => {
    try {
      const response = await apiClient.post(ENDPOINTS.DISCOUNT_RULES, rule);
      return response.data;
    } catch (_error) {
      console.warn('Mock create - returning new discount rule');
      return { ...rule, id: Date.now().toString() };
    }
  },

  updateDiscountRule: async (id: string, rule: Partial<DiscountRule>): Promise<DiscountRule> => {
    try {
      const response = await apiClient.put(ENDPOINTS.DISCOUNT_RULE(id), rule);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated discount rule');
      const existing = mockFeeRules.globalDiscountRules.find(r => r.id === id);
      return { ...existing!, ...rule };
    }
  },

  deleteDiscountRule: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(ENDPOINTS.DISCOUNT_RULE(id));
    } catch (_error) {
      console.warn('Mock delete - discount rule removed');
    }
  },

  // Opening Balance Management
  getOpeningBalance: async (): Promise<OpeningBalance> => {
    try {
      const response = await apiClient.get(ENDPOINTS.OPENING_BALANCE);
      return response.data;
    } catch (_error) {
      console.warn('Using mock opening balance data');
      return mockOpeningBalance;
    }
  },

  createOpeningBalanceEntry: async (
    entry: Omit<OpeningBalanceEntry, 'id' | 'createdAt' | 'createdBy'>
  ): Promise<OpeningBalanceEntry> => {
    try {
      const response = await apiClient.post(ENDPOINTS.OPENING_BALANCE, entry);
      return response.data;
    } catch (_error) {
      console.warn('Mock create - returning new opening balance entry');
      return {
        ...entry,
        id: Date.now().toString(),
        createdBy: 'current-user',
        createdAt: new Date().toISOString(),
      };
    }
  },

  updateOpeningBalanceEntry: async (
    id: string,
    entry: Partial<OpeningBalanceEntry>
  ): Promise<OpeningBalanceEntry> => {
    try {
      const response = await apiClient.put(ENDPOINTS.OPENING_BALANCE_ENTRY(id), entry);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated opening balance entry');
      const existing = mockOpeningBalance.entries.find(e => e.id === id);
      return { ...existing!, ...entry };
    }
  },

  deleteOpeningBalanceEntry: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(ENDPOINTS.OPENING_BALANCE_ENTRY(id));
    } catch (_error) {
      console.warn('Mock delete - opening balance entry removed');
    }
  },

  // Fee Calculation
  calculateFee: async (params: {
    baseAmount: number;
    daysLate: number;
    classId?: string;
    studentId?: string;
    appliedDiscounts?: string[];
  }): Promise<FeeCalculationPreview> => {
    try {
      const response = await apiClient.post(ENDPOINTS.CALCULATE_FEE, params);
      return response.data;
    } catch (_error) {
      console.warn('Mock fee calculation');
      const { baseAmount, daysLate } = params;

      // Mock calculation logic
      let lateFee = 0;
      if (daysLate > mockFeeRules.globalLateFeeRule.graceDays) {
        lateFee =
          mockFeeRules.globalLateFeeRule.type === 'PERCENTAGE'
            ? (baseAmount * mockFeeRules.globalLateFeeRule.value) / 100
            : mockFeeRules.globalLateFeeRule.value;
      }

      const discount = 0; // Simplified for mock
      const finalAmount = baseAmount + lateFee - discount;

      return {
        baseAmount,
        lateFee,
        discount,
        finalAmount: Math.max(0, finalAmount),
        daysLate,
        appliedRules:
          lateFee > 0
            ? [
                {
                  type: 'LATE_FEE',
                  name: 'Late Fee',
                  value: lateFee,
                  calculation: `${mockFeeRules.globalLateFeeRule.value}% of ₹${baseAmount}`,
                },
              ]
            : [],
      };
    }
  },

  // Complete Fees Settings
  getAllFeesSettings: async (): Promise<FeesSettings> => {
    try {
      const response = await apiClient.get('/api/settings/fees');
      return response.data;
    } catch (_error) {
      console.warn('Using mock fees settings data');
      return {
        feeTypes: mockFeeTypes,
        classFeeSchedule: mockClassFeeSchedule,
        feeRules: mockFeeRules,
        openingBalance: mockOpeningBalance,
        currency: 'INR',
        currencySymbol: '₹',
        decimalPlaces: 0,
        settings: {
          allowNegativeBalance: false,
          autoCalculateLateFee: true,
          sendLateFeeNotifications: true,
          gracePeriodForNewStudents: 30,
        },
      };
    }
  },

  updateFeesSettings: async (settings: Partial<FeesSettings>): Promise<FeesSettings> => {
    try {
      const response = await apiClient.put('/api/settings/fees', settings);
      return response.data;
    } catch (_error) {
      console.warn('Mock update - returning updated fees settings');
      const current = await feesApi.getAllFeesSettings();
      return { ...current, ...settings };
    }
  },
};
