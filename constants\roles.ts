/**
 * User Roles and Permissions Constants
 * 
 * Centralized role and permission definitions for:
 * - Role-based access control (RBAC)
 * - Permission checking
 * - UI conditional rendering
 * - Route protection
 * - Feature flags
 */

// ===== USER ROLES =====

export const USER_ROLES = {
  ADMIN: 'ADMIN',
  TEACHER: 'TEACHER',
  STUDENT: 'STUDENT',
  PARENT: 'PARENT',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

// ===== ROLE HIERARCHY =====

export const ROLE_HIERARCHY = {
  [USER_ROLES.ADMIN]: 4,
  [USER_ROLES.TEACHER]: 3,
  [USER_ROLES.PARENT]: 2,
  [USER_ROLES.STUDENT]: 1,
} as const;

// ===== PERMISSIONS =====

export const PERMISSIONS = {
  // Dashboard permissions
  'read:dashboard': 'View dashboard',
  'read:analytics': 'View analytics and reports',
  
  // Student permissions
  'read:students': 'View students',
  'create:students': 'Create new students',
  'update:students': 'Update student information',
  'delete:students': 'Delete students',
  'read:student_grades': 'View student grades',
  'update:student_grades': 'Update student grades',
  'read:student_attendance': 'View student attendance',
  'update:student_attendance': 'Mark student attendance',
  
  // Teacher permissions
  'read:teachers': 'View teachers',
  'create:teachers': 'Create new teachers',
  'update:teachers': 'Update teacher information',
  'delete:teachers': 'Delete teachers',
  'read:teacher_performance': 'View teacher performance',
  'update:teacher_schedule': 'Update teacher schedules',
  
  // Class permissions
  'read:classes': 'View classes',
  'create:classes': 'Create new classes',
  'update:classes': 'Update class information',
  'delete:classes': 'Delete classes',
  'manage:class_roster': 'Manage class rosters',
  'read:class_schedule': 'View class schedules',
  'update:class_schedule': 'Update class schedules',
  
  // Attendance permissions
  'read:attendance': 'View attendance records',
  'mark:attendance': 'Mark attendance',
  'update:attendance': 'Update attendance records',
  'read:attendance_reports': 'View attendance reports',
  
  // Grade permissions
  'read:grades': 'View grades',
  'create:grades': 'Create grade entries',
  'update:grades': 'Update grades',
  'delete:grades': 'Delete grade entries',
  'read:gradebook': 'View gradebook',
  'update:gradebook': 'Update gradebook',
  'read:transcripts': 'View transcripts',
  'generate:transcripts': 'Generate transcripts',
  
  // Fee permissions
  'read:fees': 'View fee information',
  'create:fees': 'Create fee entries',
  'update:fees': 'Update fee information',
  'delete:fees': 'Delete fee entries',
  'process:payments': 'Process payments',
  
  // Parent permissions
  'read:parents': 'View parent information',
  'create:parents': 'Create parent accounts',
  'update:parents': 'Update parent information',
  'delete:parents': 'Delete parent accounts',
  'read:parent_communications': 'View parent communications',
  
  // Communication permissions
  'read:communications': 'View communications',
  'create:announcements': 'Create announcements',
  'update:announcements': 'Update announcements',
  'delete:announcements': 'Delete announcements',
  'send:messages': 'Send messages',
  'read:messages': 'Read messages',
  'manage:events': 'Manage events',
  
  // Report permissions
  'read:reports': 'View reports',
  'create:reports': 'Create custom reports',
  'export:reports': 'Export reports',
  'read:academic_reports': 'View academic reports',
  'read:financial_reports': 'View financial reports',
  
  // Administrative permissions
  'admin:access': 'Access admin panel',
  'manage:users': 'Manage user accounts',
  'manage:roles': 'Manage user roles',
  'manage:permissions': 'Manage permissions',
  'read:system_logs': 'View system logs',
  'manage:system_settings': 'Manage system settings',
  'manage:backups': 'Manage system backups',
  'read:audit_logs': 'View audit logs',
} as const;

export type Permission = keyof typeof PERMISSIONS;

// ===== ROLE PERMISSIONS MAPPING =====

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [USER_ROLES.ADMIN]: [
    // Dashboard
    'read:dashboard',
    'read:analytics',
    
    // Students - Full access
    'read:students',
    'create:students',
    'update:students',
    'delete:students',
    'read:student_grades',
    'update:student_grades',
    'read:student_attendance',
    'update:student_attendance',
    
    // Teachers - Full access
    'read:teachers',
    'create:teachers',
    'update:teachers',
    'delete:teachers',
    'read:teacher_performance',
    'update:teacher_schedule',
    
    // Classes - Full access
    'read:classes',
    'create:classes',
    'update:classes',
    'delete:classes',
    'manage:class_roster',
    'read:class_schedule',
    'update:class_schedule',
    
    // Attendance - Full access
    'read:attendance',
    'mark:attendance',
    'update:attendance',
    'read:attendance_reports',
    
    // Grades - Full access
    'read:grades',
    'create:grades',
    'update:grades',
    'delete:grades',
    'read:gradebook',
    'update:gradebook',
    'read:transcripts',
    'generate:transcripts',
    
    // Fees - Full access
    'read:fees',
    'create:fees',
    'update:fees',
    'delete:fees',
    'process:payments',
    'read:financial_reports',
    
    // Parents - Full access
    'read:parents',
    'create:parents',
    'update:parents',
    'delete:parents',
    'read:parent_communications',
    
    // Communications - Full access
    'read:communications',
    'create:announcements',
    'update:announcements',
    'delete:announcements',
    'send:messages',
    'read:messages',
    'manage:events',
    
    // Reports - Full access
    'read:reports',
    'create:reports',
    'export:reports',
    'read:academic_reports',
    'read:attendance_reports',
    'read:financial_reports',
    
    // Administrative - Full access
    'admin:access',
    'manage:users',
    'manage:roles',
    'manage:permissions',
    'read:system_logs',
    'manage:system_settings',
    'manage:backups',
    'read:audit_logs',
  ],
  
  [USER_ROLES.TEACHER]: [
    // Dashboard
    'read:dashboard',
    
    // Students - Limited access
    'read:students',
    'read:student_grades',
    'update:student_grades',
    'read:student_attendance',
    'update:student_attendance',
    
    // Classes - Assigned classes only
    'read:classes',
    'manage:class_roster',
    'read:class_schedule',
    
    // Attendance - Mark and view
    'read:attendance',
    'mark:attendance',
    'read:attendance_reports',
    
    // Grades - Full access for assigned classes
    'read:grades',
    'create:grades',
    'update:grades',
    'read:gradebook',
    'update:gradebook',
    'read:transcripts',
    
    // Communications - Limited
    'read:communications',
    'send:messages',
    'read:messages',
    
    // Reports - Academic only
    'read:reports',
    'read:academic_reports',
    'read:attendance_reports',
  ],
  
  [USER_ROLES.STUDENT]: [
    // Dashboard
    'read:dashboard',
    
    // Own grades and attendance
    'read:student_grades',
    'read:student_attendance',
    'read:grades',
    'read:attendance',
    
    // Classes - View only
    'read:classes',
    'read:class_schedule',
    
    // Communications - Receive only
    'read:communications',
    'read:messages',
    
    // Own transcripts
    'read:transcripts',
  ],
  
  [USER_ROLES.PARENT]: [
    // Dashboard
    'read:dashboard',
    
    // Child's information
    'read:students',
    'read:student_grades',
    'read:student_attendance',
    'read:grades',
    'read:attendance',
    
    // Classes - View child's classes
    'read:classes',
    'read:class_schedule',
    
    // Fees - View and pay
    'read:fees',
    'process:payments',
    
    // Communications
    'read:communications',
    'send:messages',
    'read:messages',
    'read:parent_communications',
    
    // Reports - Child's reports
    'read:reports',
    'read:academic_reports',
    'read:attendance_reports',
    
    // Transcripts - Child's transcripts
    'read:transcripts',
  ],
};

// ===== ROLE DESCRIPTIONS =====

export const ROLE_DESCRIPTIONS = {
  [USER_ROLES.ADMIN]: {
    title: 'Administrator',
    description: 'Full system access with administrative privileges',
    color: 'red',
    icon: 'Shield',
  },
  [USER_ROLES.TEACHER]: {
    title: 'Teacher',
    description: 'Manage assigned classes, students, and academic records',
    color: 'blue',
    icon: 'GraduationCap',
  },
  [USER_ROLES.STUDENT]: {
    title: 'Student',
    description: 'View own academic records and class information',
    color: 'green',
    icon: 'BookOpen',
  },
  [USER_ROLES.PARENT]: {
    title: 'Parent/Guardian',
    description: 'Monitor child\'s academic progress and school communications',
    color: 'purple',
    icon: 'Users',
  },
} as const;

// ===== UTILITY FUNCTIONS =====

export const roleUtils = {
  // Check if user has specific permission
  hasPermission: (userRole: UserRole, permission: Permission): boolean => {
    const rolePermissions = ROLE_PERMISSIONS[userRole];
    return rolePermissions.includes(permission);
  },
  
  // Check if user has any of the specified permissions
  hasAnyPermission: (userRole: UserRole, permissions: Permission[]): boolean => {
    return permissions.some(permission => roleUtils.hasPermission(userRole, permission));
  },
  
  // Check if user has all specified permissions
  hasAllPermissions: (userRole: UserRole, permissions: Permission[]): boolean => {
    return permissions.every(permission => roleUtils.hasPermission(userRole, permission));
  },
  
  // Get all permissions for a role
  getRolePermissions: (userRole: UserRole): Permission[] => {
    return ROLE_PERMISSIONS[userRole] || [];
  },
  
  // Check if role has higher hierarchy than another
  hasHigherRole: (userRole: UserRole, targetRole: UserRole): boolean => {
    return ROLE_HIERARCHY[userRole] > ROLE_HIERARCHY[targetRole];
  },
  
  // Get role hierarchy level
  getRoleLevel: (userRole: UserRole): number => {
    return ROLE_HIERARCHY[userRole] || 0;
  },
  
  // Get role description
  getRoleDescription: (userRole: UserRole) => {
    return ROLE_DESCRIPTIONS[userRole];
  },
  
  // Check if user can manage another user
  canManageUser: (managerRole: UserRole, targetRole: UserRole): boolean => {
    // Admins can manage everyone
    if (managerRole === USER_ROLES.ADMIN) return true;
    
    // Teachers can manage students
    if (managerRole === USER_ROLES.TEACHER && targetRole === USER_ROLES.STUDENT) return true;
    
    // Parents can only manage their own children (handled at application level)
    return false;
  },
  
  // Get available roles for assignment (based on current user role)
  getAssignableRoles: (currentUserRole: UserRole): UserRole[] => {
    switch (currentUserRole) {
      case USER_ROLES.ADMIN:
        return [USER_ROLES.ADMIN, USER_ROLES.TEACHER, USER_ROLES.STUDENT, USER_ROLES.PARENT];
      case USER_ROLES.TEACHER:
        return [USER_ROLES.STUDENT];
      default:
        return [];
    }
  },
};

// ===== PERMISSION GROUPS =====

export const PERMISSION_GROUPS = {
  ACADEMIC: {
    title: 'Academic Management',
    permissions: [
      'read:students',
      'create:students',
      'update:students',
      'delete:students',
      'read:teachers',
      'create:teachers',
      'update:teachers',
      'delete:teachers',
      'read:classes',
      'create:classes',
      'update:classes',
      'delete:classes',
    ],
  },
  ASSESSMENT: {
    title: 'Assessment & Grading',
    permissions: [
      'read:grades',
      'create:grades',
      'update:grades',
      'delete:grades',
      'read:attendance',
      'mark:attendance',
      'update:attendance',
      'read:gradebook',
      'update:gradebook',
    ],
  },
  FINANCIAL: {
    title: 'Financial Management',
    permissions: [
      'read:fees',
      'create:fees',
      'update:fees',
      'delete:fees',
      'process:payments',
      'read:financial_reports',
    ],
  },
  COMMUNICATION: {
    title: 'Communication',
    permissions: [
      'read:communications',
      'create:announcements',
      'update:announcements',
      'delete:announcements',
      'send:messages',
      'read:messages',
      'manage:events',
    ],
  },
  REPORTING: {
    title: 'Reports & Analytics',
    permissions: [
      'read:reports',
      'create:reports',
      'export:reports',
      'read:academic_reports',
      'read:attendance_reports',
      'read:financial_reports',
      'read:analytics',
    ],
  },
  ADMINISTRATION: {
    title: 'System Administration',
    permissions: [
      'admin:access',
      'manage:users',
      'manage:roles',
      'manage:permissions',
      'read:system_logs',
      'manage:system_settings',
      'manage:backups',
      'read:audit_logs',
    ],
  },
} as const;

export default {
  USER_ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  ROLE_DESCRIPTIONS,
  roleUtils,
};
