'use client';

import React, { useState } from 'react';
import { IdCard, Eye, Save, RotateCcw, Type, Bold, Italic, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

// Mock data
const mockIDCardNote = {
  content: `**Important Instructions:**

• Always carry your ID card while on school premises
• Report immediately if your ID card is lost or damaged
• ID cards are non-transferable and for personal use only
• Replacement fee: ₹100 for lost/damaged cards

*For any queries, contact the school office.*

**Emergency Contact:** +91-9876543210`,
  lastUpdated: '2024-03-15T10:30:00Z',
  updatedBy: 'admin',
};

interface IDCardNoteFormProps {
  onDataChange?: () => void;
}

/**
 * IDCardNoteForm Component
 * 
 * Features:
 * - Rich text editor with basic formatting
 * - Live preview on sample ID card
 * - Auto-save functionality
 * - Markdown support
 */
export function IDCardNoteForm({ onDataChange }: IDCardNoteFormProps) {
  const { toast } = useToast();
  const [content, setContent] = useState(mockIDCardNote.content);
  const [isEditing, setIsEditing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Handle content change
  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    setHasUnsavedChanges(true);
    onDataChange?.();
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setHasUnsavedChanges(false);
      setIsEditing(false);
      
      toast({
        title: 'Note saved',
        description: 'ID card note has been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save ID card note. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle reset
  const handleReset = () => {
    setContent(mockIDCardNote.content);
    setHasUnsavedChanges(false);
    setIsEditing(false);
    
    toast({
      title: 'Changes reset',
      description: 'ID card note has been reset to the last saved version.',
    });
  };

  // Insert formatting
  const insertFormatting = (format: string) => {
    const textarea = document.getElementById('id-card-content') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    let newText = '';
    let cursorOffset = 0;

    switch (format) {
      case 'bold':
        newText = `**${selectedText || 'bold text'}**`;
        cursorOffset = selectedText ? 0 : -2;
        break;
      case 'italic':
        newText = `*${selectedText || 'italic text'}*`;
        cursorOffset = selectedText ? 0 : -1;
        break;
      case 'bullet':
        newText = `• ${selectedText || 'list item'}`;
        cursorOffset = selectedText ? 0 : -9;
        break;
      default:
        return;
    }

    const newContent = content.substring(0, start) + newText + content.substring(end);
    setContent(newContent);
    setHasUnsavedChanges(true);

    // Set cursor position
    setTimeout(() => {
      const newPosition = start + newText.length + cursorOffset;
      textarea.setSelectionRange(newPosition, newPosition);
      textarea.focus();
    }, 0);
  };

  // Render markdown-like content for preview
  const renderPreview = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^• (.+)$/gm, '<div style="margin-left: 12px;">• $1</div>')
      .replace(/\n/g, '<br />');
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
            <IdCard className="h-6 w-6 text-sky-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">ID Card Note</h2>
            <p className="text-slate-600">Configure the note that appears on student ID cards</p>
          </div>
        </div>
        
        {/* Status */}
        <div className="flex items-center gap-2 text-sm text-slate-600">
          <span>Last updated: {new Date(mockIDCardNote.lastUpdated).toLocaleString()}</span>
          <span>•</span>
          <span>by {mockIDCardNote.updatedBy}</span>
          {hasUnsavedChanges && (
            <>
              <span>•</span>
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                Unsaved changes
              </Badge>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Type className="h-5 w-5 text-sky-600" />
                Editor
              </CardTitle>
              <div className="flex items-center gap-2">
                {!isEditing ? (
                  <Button
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="bg-gradient-to-r from-sky-600 to-violet-600"
                  >
                    Edit Note
                  </Button>
                ) : (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleReset}
                      disabled={!hasUnsavedChanges}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reset
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      disabled={!hasUnsavedChanges}
                      className="bg-gradient-to-r from-sky-600 to-violet-600"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                {/* Formatting Toolbar */}
                <div className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => insertFormatting('bold')}
                    title="Bold"
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => insertFormatting('italic')}
                    title="Italic"
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => insertFormatting('bullet')}
                    title="Bullet Point"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <div className="ml-auto text-xs text-slate-500">
                    Use **bold**, *italic*, • bullets
                  </div>
                </div>

                {/* Text Editor */}
                <div className="space-y-2">
                  <Label htmlFor="id-card-content">Note Content</Label>
                  <Textarea
                    id="id-card-content"
                    value={content}
                    onChange={(e) => handleContentChange(e.target.value)}
                    rows={12}
                    placeholder="Enter the note content that will appear on ID cards..."
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-slate-500">
                    Supports basic markdown: **bold**, *italic*, • bullet points
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-4 bg-slate-50 rounded-lg border-l-4 border-sky-500">
                  <div 
                    className="text-sm whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ __html: renderPreview(content) }}
                  />
                </div>
                <p className="text-xs text-slate-500">
                  Click "Edit Note" to modify the content
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-sky-600" />
              ID Card Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Sample ID Card */}
            <div className="bg-gradient-to-br from-sky-500 to-violet-600 p-1 rounded-xl shadow-lg">
              <div className="bg-white rounded-lg p-4 space-y-4">
                {/* Header */}
                <div className="text-center border-b pb-3">
                  <h3 className="font-bold text-lg text-slate-900">GREENWOOD HIGH SCHOOL</h3>
                  <p className="text-sm text-slate-600">Student Identity Card</p>
                </div>

                {/* Student Info */}
                <div className="flex gap-4">
                  <div className="w-16 h-20 bg-slate-200 rounded flex items-center justify-center">
                    <span className="text-xs text-slate-500">Photo</span>
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="text-sm">
                      <span className="font-medium">Name:</span> John Doe
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Class:</span> 10-A
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Roll No:</span> 2024001
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Session:</span> 2024-25
                    </div>
                  </div>
                </div>

                {/* Note Section */}
                <div className="border-t pt-3">
                  <div className="bg-slate-50 p-3 rounded text-xs">
                    <div 
                      className="whitespace-pre-wrap leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: renderPreview(content) }}
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="text-center text-xs text-slate-500 border-t pt-2">
                  Valid for Academic Year 2024-25
                </div>
              </div>
            </div>

            {/* Preview Info */}
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Preview:</strong> This shows how the note will appear on actual ID cards. 
                The note section is highlighted in the gray area.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Help Section */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Formatting Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Bold Text</h4>
              <code className="bg-slate-100 px-2 py-1 rounded">**bold text**</code>
              <p className="text-slate-600 mt-1">Use double asterisks for bold formatting</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Italic Text</h4>
              <code className="bg-slate-100 px-2 py-1 rounded">*italic text*</code>
              <p className="text-slate-600 mt-1">Use single asterisks for italic formatting</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Bullet Points</h4>
              <code className="bg-slate-100 px-2 py-1 rounded">• list item</code>
              <p className="text-slate-600 mt-1">Use bullet symbol for list items</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
