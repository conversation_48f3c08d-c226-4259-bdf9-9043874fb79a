# Testing Guide

This document outlines the testing strategy and best practices for the School Management System frontend.

## 🧪 Testing Stack

- **Test Runner**: Jest
- **Testing Library**: React Testing Library
- **Environment**: jsdom
- **Coverage**: Jest built-in coverage

## 📁 Test Structure

```
__tests__/
├── components/
│   ├── auth/
│   │   └── LoginForm.test.tsx
│   ├── teachers/
│   │   └── TeacherList.test.tsx
│   ├── students/
│   │   └── StudentList.test.tsx
│   └── shared/
│       └── ListCard.test.tsx
├── hooks/
│   └── useAuth.test.tsx
└── lib/
    └── utils.test.ts
```

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests in CI mode
npm run test:ci
```

### Test Patterns

```bash
# Run specific test file
npm test LoginForm.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="renders correctly"

# Run tests for specific component
npm test -- --testPathPattern="components/auth"
```

## 📝 Testing Best Practices

### 1. Test Structure

Use the **Arrange, Act, Assert** pattern:

```typescript
describe('Component', () => {
  it('should do something', () => {
    // Arrange
    const props = { title: 'Test' }
    
    // Act
    render(<Component {...props} />)
    
    // Assert
    expect(screen.getByText('Test')).toBeInTheDocument()
  })
})
```

### 2. Test Naming

- Use descriptive test names that explain the behavior
- Start with "should" or use present tense
- Include the expected outcome

```typescript
// ✅ Good
it('displays error message when login fails')
it('filters students by search term')
it('calls onSubmit with form data when form is valid')

// ❌ Bad
it('test login')
it('search works')
it('form test')
```

### 3. User-Centric Testing

Test from the user's perspective using React Testing Library:

```typescript
// ✅ Good - Test user interactions
const user = userEvent.setup()
const button = screen.getByRole('button', { name: /submit/i })
await user.click(button)

// ❌ Bad - Test implementation details
const button = container.querySelector('.submit-button')
fireEvent.click(button)
```

### 4. Accessibility Testing

Include accessibility checks in your tests:

```typescript
it('has proper accessibility attributes', () => {
  render(<LoginForm />)
  
  const emailInput = screen.getByLabelText(/email/i)
  expect(emailInput).toHaveAttribute('aria-required', 'true')
  expect(emailInput).toHaveAttribute('type', 'email')
})
```

## 🔧 Test Utilities

### Test Wrapper

Use the provided test wrapper for components that need providers:

```typescript
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Usage
render(<Component />, { wrapper: TestWrapper })
```

### Mock Functions

Create reusable mock functions:

```typescript
const mockUseAuth = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  clearError: jest.fn(),
}

beforeEach(() => {
  (useAuth as jest.Mock).mockReturnValue(mockUseAuth)
})
```

## 📊 Coverage Guidelines

### Coverage Targets

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### What to Test

#### ✅ Always Test
- User interactions (clicks, form submissions, navigation)
- Error states and error boundaries
- Loading states
- Data fetching and mutations
- Form validation
- Accessibility features
- Conditional rendering based on props/state

#### ⚠️ Sometimes Test
- Complex business logic
- Utility functions with multiple branches
- Custom hooks with complex state management
- Integration between multiple components

#### ❌ Don't Test
- Third-party library internals
- Simple prop passing
- Styling and CSS classes (unless functional)
- Implementation details

## 🎯 Component Testing Patterns

### Form Testing

```typescript
describe('LoginForm', () => {
  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<LoginForm />)
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)
    
    expect(screen.getByText(/email is required/i)).toBeInTheDocument()
    expect(screen.getByText(/password is required/i)).toBeInTheDocument()
  })
  
  it('submits form with valid data', async () => {
    const mockOnSubmit = jest.fn()
    const user = userEvent.setup()
    
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })
})
```

### List Component Testing

```typescript
describe('TeacherList', () => {
  it('displays loading state', () => {
    mockUseQuery.mockReturnValue({ isLoading: true, data: undefined })
    render(<TeacherList />)
    
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
  })
  
  it('displays error state with retry option', () => {
    const mockRefetch = jest.fn()
    mockUseQuery.mockReturnValue({ 
      isLoading: false, 
      error: new Error('Failed to fetch'),
      refetch: mockRefetch
    })
    
    render(<TeacherList />)
    
    expect(screen.getByText(/failed to fetch/i)).toBeInTheDocument()
    
    const retryButton = screen.getByRole('button', { name: /try again/i })
    fireEvent.click(retryButton)
    
    expect(mockRefetch).toHaveBeenCalled()
  })
})
```

### Hook Testing

```typescript
describe('useAuth', () => {
  it('returns initial state', () => {
    const { result } = renderHook(() => useAuth())
    
    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
  })
  
  it('handles login', async () => {
    const { result } = renderHook(() => useAuth())
    
    await act(async () => {
      await result.current.login({ email: '<EMAIL>', password: 'password' })
    })
    
    expect(result.current.isAuthenticated).toBe(true)
  })
})
```

## 🐛 Debugging Tests

### Common Issues

1. **Async operations not awaited**
   ```typescript
   // ❌ Bad
   fireEvent.click(button)
   expect(screen.getByText('Success')).toBeInTheDocument()
   
   // ✅ Good
   await user.click(button)
   await waitFor(() => {
     expect(screen.getByText('Success')).toBeInTheDocument()
   })
   ```

2. **Testing implementation details**
   ```typescript
   // ❌ Bad
   expect(component.state.isLoading).toBe(true)
   
   // ✅ Good
   expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
   ```

3. **Not cleaning up after tests**
   ```typescript
   afterEach(() => {
     jest.clearAllMocks()
     cleanup()
   })
   ```

### Debug Tools

```typescript
// Debug rendered output
screen.debug()

// Debug specific element
screen.debug(screen.getByRole('button'))

// Log all available queries
screen.logTestingPlaygroundURL()
```

## 📈 Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch pushes
- Release branches

### CI Configuration

```yaml
# .github/workflows/test.yml
- name: Run tests
  run: npm run test:ci
  
- name: Upload coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/lcov.info
```

## 🔄 Test Maintenance

### Regular Tasks

1. **Update snapshots** when UI changes intentionally
2. **Review coverage reports** and add tests for uncovered code
3. **Refactor tests** when components change significantly
4. **Remove obsolete tests** for deleted features

### Performance

- Keep tests fast by mocking external dependencies
- Use `screen.getBy*` queries for elements that should exist
- Use `screen.queryBy*` queries for elements that might not exist
- Avoid unnecessary `waitFor` calls

---

## 📚 Resources

- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing](https://testing-library.com/docs/guide-which-query)
