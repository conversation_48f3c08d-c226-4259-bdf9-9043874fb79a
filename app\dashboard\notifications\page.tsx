'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Bell, BellRing, Clock, Eye, MessageSquare } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ModulePageLayout } from '@/components/ui/module-page-layout';

const mockNotificationStats = {
  totalNotifications: 48,
  unreadNotifications: 12,
  highPriority: 5,
  thisWeek: 18,
};

const mockNotifications = [
  {
    id: '1',
    title: 'Parent-Teacher Conference Reminder',
    message:
      'Reminder: Parent-teacher conferences are scheduled for March 20th. Please confirm your attendance.',
    type: 'Reminder',
    priority: 'High',
    status: 'Unread',
    sender: 'Administration',
    recipient: 'All Parents',
    date: '2024-03-01',
    time: '09:00 AM',
    readBy: 45,
    totalRecipients: 120,
  },
  {
    id: '2',
    title: 'School Closure Due to Weather',
    message:
      'Due to severe weather conditions, the school will be closed tomorrow, March 2nd. All classes are cancelled.',
    type: 'Alert',
    priority: 'Critical',
    status: 'Read',
    sender: 'Principal Office',
    recipient: 'All Students & Parents',
    date: '2024-03-01',
    time: '06:30 AM',
    readBy: 98,
    totalRecipients: 150,
  },
  {
    id: '3',
    title: 'Science Fair Registration Open',
    message:
      'Registration for the annual science fair is now open. Students can register until March 15th.',
    type: 'Announcement',
    priority: 'Medium',
    status: 'Read',
    sender: 'Science Department',
    recipient: 'All Students',
    date: '2024-02-28',
    time: '02:00 PM',
    readBy: 67,
    totalRecipients: 80,
  },
  {
    id: '4',
    title: 'Library Book Return Reminder',
    message:
      'This is a reminder that your library books are due for return by March 5th to avoid late fees.',
    type: 'Reminder',
    priority: 'Low',
    status: 'Unread',
    sender: 'Library',
    recipient: 'Selected Students',
    date: '2024-02-29',
    time: '11:00 AM',
    readBy: 12,
    totalRecipients: 25,
  },
  {
    id: '5',
    title: 'New Lunch Menu Available',
    message:
      'The new lunch menu for March is now available. Check out the healthy and delicious options!',
    type: 'Information',
    priority: 'Low',
    status: 'Read',
    sender: 'Cafeteria',
    recipient: 'All Students & Parents',
    date: '2024-02-27',
    time: '01:00 PM',
    readBy: 89,
    totalRecipients: 150,
  },
];

const notificationTypes = ['All Types', 'Alert', 'Reminder', 'Announcement', 'Information'];
const priorities = ['All Priorities', 'Critical', 'High', 'Medium', 'Low'];
const statuses = ['All Status', 'Read', 'Unread'];

export default function NotificationsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All Types');
  const [selectedPriority, setSelectedPriority] = useState('All Priorities');
  const [selectedStatus, setSelectedStatus] = useState('All Status');

  const filteredNotifications = mockNotifications.filter(notification => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.sender.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'All Types' || notification.type === selectedType;
    const matchesPriority =
      selectedPriority === 'All Priorities' || notification.priority === selectedPriority;
    const matchesStatus = selectedStatus === 'All Status' || notification.status === selectedStatus;

    return matchesSearch && matchesType && matchesPriority && matchesStatus;
  });

  const statsCards = [
    {
      title: 'Total Notifications',
      value: mockNotificationStats.totalNotifications.toString(),
      icon: Bell,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Unread',
      value: mockNotificationStats.unreadNotifications.toString(),
      icon: BellRing,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+3%',
      changeType: 'positive' as const,
    },
    {
      title: 'High Priority',
      value: mockNotificationStats.highPriority.toString(),
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '-2%',
      changeType: 'negative' as const,
    },
    {
      title: 'This Week',
      value: mockNotificationStats.thisWeek.toString(),
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+15%',
      changeType: 'positive' as const,
    },
  ];

  const filters = [
    {
      label: 'Type',
      value: selectedType,
      onChange: setSelectedType,
      options: notificationTypes.map(type => ({ label: type, value: type })),
    },
    {
      label: 'Priority',
      value: selectedPriority,
      onChange: setSelectedPriority,
      options: priorities.map(priority => ({ label: priority, value: priority })),
    },
    {
      label: 'Status',
      value: selectedStatus,
      onChange: setSelectedStatus,
      options: statuses.map(status => ({ label: status, value: status })),
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Alert':
        return 'bg-red-100 text-red-800';
      case 'Reminder':
        return 'bg-blue-100 text-blue-800';
      case 'Announcement':
        return 'bg-purple-100 text-purple-800';
      case 'Information':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <ModulePageLayout
      title='Notification Management'
      description='Manage school announcements, alerts, and communications'
      icon={Bell}
      badge={{ label: 'Demo Data', variant: 'outline' }}
      statsCards={statsCards}
      searchPlaceholder='Search notifications...'
      filters={filters}
      createRoute='/dashboard/notifications/create'
      createLabel='Create Notification'
      searchValue={searchTerm}
      onSearchChange={setSearchTerm}
      totalItems={mockNotifications.length}
      filteredItems={filteredNotifications.length}
    >
      <div className='grid grid-cols-1 gap-4'>
        {filteredNotifications.map(notification => (
          <Card
            key={notification.id}
            className={`hover:shadow-md transition-shadow ${
              notification.status === 'Unread' ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''
            }`}
          >
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4'>
                <div className='flex-1 space-y-3'>
                  <div className='flex items-start justify-between gap-4'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-2'>
                        {notification.status === 'Unread' && (
                          <div className='w-2 h-2 bg-blue-500 rounded-full' />
                        )}
                        <h3
                          className={`text-lg font-semibold ${
                            notification.status === 'Unread' ? 'text-gray-900' : 'text-gray-700'
                          }`}
                        >
                          {notification.title}
                        </h3>
                      </div>
                      <p className='text-gray-600 mb-3 line-clamp-2'>{notification.message}</p>
                      <div className='flex flex-wrap gap-2'>
                        <Badge className={getTypeColor(notification.type)}>
                          {notification.type}
                        </Badge>
                        <Badge className={getPriorityColor(notification.priority)}>
                          {notification.priority}
                        </Badge>
                        {notification.status === 'Unread' && (
                          <Badge variant='outline' className='text-blue-600 border-blue-600'>
                            Unread
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className='flex gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => router.push(`/dashboard/notifications/${notification.id}`)}
                      >
                        <Eye className='w-4 h-4 mr-2' />
                        View
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          router.push(`/dashboard/notifications/${notification.id}/edit`)
                        }
                      >
                        Edit
                      </Button>
                    </div>
                  </div>

                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t text-sm text-gray-600'>
                    <div className='flex items-center gap-2'>
                      <Clock className='w-4 h-4' />
                      <span>
                        {notification.date} at {notification.time}
                      </span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <MessageSquare className='w-4 h-4' />
                      <span>From: {notification.sender}</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Bell className='w-4 h-4' />
                      <span>To: {notification.recipient}</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Eye className='w-4 h-4' />
                      <span>
                        {notification.readBy}/{notification.totalRecipients} read
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredNotifications.length === 0 && (
          <div className='text-center py-12'>
            <Bell className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No notifications found</h3>
            <p className='text-gray-500 mb-6'>
              {searchTerm || selectedType !== 'All Types' || selectedPriority !== 'All Priorities'
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first notification'}
            </p>
            <Button onClick={() => router.push('/dashboard/notifications/create')}>
              Create Notification
            </Button>
          </div>
        )}
      </div>
    </ModulePageLayout>
  );
}
