'use client';

import { BookOpen, Calendar, Clock, GraduationCap, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ModulePageLayout } from '@/components/ui/module-page-layout';

// Mock data for demonstration
const mockClassStats = {
  totalClasses: 24,
  activeClasses: 18,
  totalStudents: 450,
  totalTeachers: 32,
};

const mockClasses = [
  {
    id: 1,
    name: 'Grade 10 - Mathematics',
    subject: 'Mathematics',
    teacher: 'Dr. <PERSON>',
    students: 28,
    schedule: 'Mon, Wed, Fri - 9:00 AM',
    room: 'Room 101',
    status: 'Active',
    semester: 'Fall 2024',
  },
  {
    id: 2,
    name: 'Grade 9 - Science',
    subject: 'Science',
    teacher: 'Prof. <PERSON>',
    students: 25,
    schedule: '<PERSON><PERSON>, Thu - 10:30 AM',
    room: 'Lab 201',
    status: 'Active',
    semester: 'Fall 2024',
  },
  {
    id: 3,
    name: 'Grade 11 - English Literature',
    subject: 'English',
    teacher: 'Ms. <PERSON>',
    students: 22,
    schedule: '<PERSON>, <PERSON><PERSON>, Fri - 2:00 PM',
    room: 'Room 305',
    status: 'Active',
    semester: 'Fall 2024',
  },
  {
    id: 4,
    name: 'Grade 12 - Physics',
    subject: 'Physics',
    teacher: 'Dr. Michael Brown',
    students: 20,
    schedule: 'Tue, Thu - 1:00 PM',
    room: 'Lab 102',
    status: 'Completed',
    semester: 'Spring 2024',
  },
];

export default function ClassesPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [subjectFilter, setSubjectFilter] = useState('All');

  const handleViewClass = (classId: number) => {
    router.push(`/dashboard/classes/${classId}`);
  };

  const handleEditClass = (classId: number) => {
    router.push(`/dashboard/classes/${classId}/edit`);
  };

  const statsCards = [
    {
      title: 'Total Classes',
      value: mockClassStats.totalClasses.toString(),
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Classes',
      value: mockClassStats.activeClasses.toString(),
      icon: GraduationCap,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Students',
      value: mockClassStats.totalStudents.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+3%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Teachers',
      value: mockClassStats.totalTeachers.toString(),
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+1%',
      changeType: 'positive' as const,
    },
  ];

  const filteredClasses = mockClasses.filter(classItem => {
    const matchesSearch =
      classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classItem.teacher.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classItem.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All' || classItem.status === statusFilter;
    const matchesSubject = subjectFilter === 'All' || classItem.subject === subjectFilter;
    return matchesSearch && matchesStatus && matchesSubject;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'text-green-600 bg-green-50';
      case 'Completed':
        return 'text-blue-600 bg-blue-50';
      case 'Cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const filters = [
    {
      label: 'Status',
      options: [
        { label: 'All Status', value: 'All' },
        { label: 'Active', value: 'Active' },
        { label: 'Completed', value: 'Completed' },
        { label: 'Cancelled', value: 'Cancelled' },
      ],
      value: statusFilter,
      onChange: setStatusFilter,
    },
    {
      label: 'Subject',
      options: [
        { label: 'All Subjects', value: 'All' },
        { label: 'Mathematics', value: 'Mathematics' },
        { label: 'Science', value: 'Science' },
        { label: 'English', value: 'English' },
        { label: 'Physics', value: 'Physics' },
      ],
      value: subjectFilter,
      onChange: setSubjectFilter,
    },
  ];

  return (
    <ModulePageLayout
      title='Class Management'
      description='Manage class schedules, subjects, and assignments'
      icon={BookOpen}
      badge={{ label: 'Demo Data', variant: 'outline' }}
      statsCards={statsCards}
      searchPlaceholder='Search classes...'
      filters={filters}
      createRoute='/dashboard/classes/create'
      createLabel='Create Class'
      searchValue={searchTerm}
      onSearchChange={setSearchTerm}
      totalItems={mockClasses.length}
      filteredItems={filteredClasses.length}
    >
      {/* Classes List */}
      <div className='grid grid-cols-1 gap-4'>
        {filteredClasses.map(classItem => (
          <Card key={classItem.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex items-start space-x-4'>
                  <div className='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center'>
                    <BookOpen className='w-6 h-6 text-blue-600' />
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-2'>
                      <h3 className='font-semibold'>{classItem.name}</h3>
                      <Badge variant='outline' className={getStatusColor(classItem.status)}>
                        {classItem.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-muted-foreground mb-2'>
                      Teacher: {classItem.teacher}
                    </p>
                    <div className='flex flex-wrap gap-4 text-sm text-muted-foreground'>
                      <div className='flex items-center'>
                        <Calendar className='w-4 h-4 mr-1' />
                        {classItem.schedule}
                      </div>
                      <div className='flex items-center'>
                        <Users className='w-4 h-4 mr-1' />
                        {classItem.students} students
                      </div>
                      <div className='flex items-center'>
                        <Clock className='w-4 h-4 mr-1' />
                        {classItem.room}
                      </div>
                    </div>
                  </div>
                </div>

                <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
                  <div className='text-center sm:text-right'>
                    <p className='font-semibold'>{classItem.subject}</p>
                    <p className='text-sm text-muted-foreground'>{classItem.semester}</p>
                  </div>

                  <div className='flex space-x-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleViewClass(classItem.id)}
                    >
                      View
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleEditClass(classItem.id)}
                    >
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredClasses.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>📚</div>
            <h3 className='text-lg font-semibold mb-2'>No Classes Found</h3>
            <p className='text-muted-foreground text-center mb-4'>
              {searchTerm || statusFilter !== 'All' || subjectFilter !== 'All'
                ? 'No classes match your current filters.'
                : 'There are no classes created yet.'}
            </p>
            <Button onClick={() => (window.location.href = '/dashboard/classes/create')}>
              <BookOpen className='mr-2 h-4 w-4' />
              Create First Class
            </Button>
          </CardContent>
        </Card>
      )}
    </ModulePageLayout>
  );
}
