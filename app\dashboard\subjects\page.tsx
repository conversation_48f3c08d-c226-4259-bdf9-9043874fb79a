'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BookOpen, 
  Clock, 
  GraduationCap, 
  Plus, 
  Search, 
  Users 
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for demonstration
const mockSubjectStats = {
  totalSubjects: 15,
  activeSubjects: 12,
  totalTeachers: 18,
  totalStudents: 450,
};

const mockSubjects = [
  {
    id: 1,
    name: 'Mathematics',
    code: 'MATH101',
    department: 'Science',
    teacher: 'Dr. <PERSON>',
    students: 45,
    credits: 4,
    schedule: 'Mon, Wed, Fri - 9:00 AM',
    status: 'Active',
  },
  {
    id: 2,
    name: 'Physics',
    code: 'PHYS101',
    department: 'Science',
    teacher: 'Dr. <PERSON>',
    students: 38,
    credits: 4,
    schedule: 'Tu<PERSON>, Thu - 10:30 AM',
    status: 'Active',
  },
  {
    id: 3,
    name: 'English Literature',
    code: 'ENG201',
    department: 'Arts',
    teacher: 'Prof. <PERSON> Davis',
    students: 52,
    credits: 3,
    schedule: 'Mon, Wed - 2:00 PM',
    status: 'Active',
  },
];

export default function SubjectsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');

  const handleAddSubject = () => {
    // TODO: Navigate to add subject page
    console.log('Add subject clicked');
  };

  const handleViewSubject = (subjectId: number) => {
    // TODO: Navigate to subject details
    console.log('View subject:', subjectId);
  };

  const handleEditSubject = (subjectId: number) => {
    // TODO: Navigate to edit subject
    console.log('Edit subject:', subjectId);
  };

  const statsCards = [
    {
      title: 'Total Subjects',
      value: mockSubjectStats.totalSubjects.toString(),
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Subjects',
      value: mockSubjectStats.activeSubjects.toString(),
      icon: GraduationCap,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+1%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Teachers',
      value: mockSubjectStats.totalTeachers.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Enrolled Students',
      value: mockSubjectStats.totalStudents.toString(),
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
  ];

  const filteredSubjects = mockSubjects.filter(subject => {
    const matchesSearch = subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         subject.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = departmentFilter === 'All' || subject.department === departmentFilter;
    const matchesStatus = statusFilter === 'All' || subject.status === statusFilter;
    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const getDepartmentColor = (department: string) => {
    switch (department) {
      case 'Science': return 'text-blue-600 bg-blue-50';
      case 'Arts': return 'text-purple-600 bg-purple-50';
      case 'Commerce': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <PageHeader
        title="Subject Management"
        description="Manage academic subjects and course curriculum"
        icon={BookOpen}
        badge={{ label: 'Demo Data', variant: 'outline' }}
        actions={[
          {
            label: 'Add Subject',
            icon: Plus,
            onClick: handleAddSubject,
          },
        ]}
      />

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search subjects...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select department' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Departments</SelectItem>
                <SelectItem value='Science'>Science</SelectItem>
                <SelectItem value='Arts'>Arts</SelectItem>
                <SelectItem value='Commerce'>Commerce</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Status</SelectItem>
                <SelectItem value='Active'>Active</SelectItem>
                <SelectItem value='Inactive'>Inactive</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex items-center text-sm text-muted-foreground'>
              Showing {filteredSubjects.length} of {mockSubjects.length} subjects
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subjects Grid */}
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
        {filteredSubjects.map(subject => (
          <Card key={subject.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex items-start justify-between mb-4'>
                <div className='flex items-center space-x-3'>
                  <div className='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center'>
                    <BookOpen className='w-6 h-6 text-blue-600' />
                  </div>
                  <div>
                    <h3 className='font-semibold'>{subject.name}</h3>
                    <p className='text-sm text-muted-foreground'>{subject.code}</p>
                  </div>
                </div>
                <Badge 
                  variant='outline' 
                  className={getDepartmentColor(subject.department)}
                >
                  {subject.department}
                </Badge>
              </div>

              <div className='space-y-2 mb-4'>
                <div className='flex items-center text-sm'>
                  <GraduationCap className='w-4 h-4 mr-2 text-muted-foreground' />
                  <span className='text-muted-foreground'>Teacher:</span>
                  <span className='ml-1 font-medium'>{subject.teacher}</span>
                </div>
                
                <div className='flex items-center text-sm'>
                  <Users className='w-4 h-4 mr-2 text-muted-foreground' />
                  <span className='text-muted-foreground'>Students:</span>
                  <span className='ml-1 font-medium'>{subject.students}</span>
                </div>
                
                <div className='flex items-center text-sm'>
                  <Clock className='w-4 h-4 mr-2 text-muted-foreground' />
                  <span className='text-muted-foreground'>Schedule:</span>
                  <span className='ml-1 font-medium text-xs'>{subject.schedule}</span>
                </div>
              </div>

              <div className='flex space-x-2'>
                <Button
                  variant='outline'
                  size='sm'
                  className='flex-1'
                  onClick={() => handleViewSubject(subject.id)}
                >
                  View
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  className='flex-1'
                  onClick={() => handleEditSubject(subject.id)}
                >
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSubjects.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>📚</div>
            <h3 className='text-lg font-semibold mb-2'>No Subjects Found</h3>
            <p className='text-muted-foreground text-center mb-4'>
              {searchTerm || departmentFilter !== 'All' || statusFilter !== 'All'
                ? 'No subjects match your current filters.'
                : 'There are no subjects in the system yet.'}
            </p>
            <Button onClick={handleAddSubject}>
              <Plus className='mr-2 h-4 w-4' />
              Add First Subject
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
