/**
 * Simple Backend Status Checker
 * 
 * This script checks if the FastAPI backend is running and accessible
 */

async function checkBackendStatus() {
  console.log('🔍 Checking Backend Status...\n');

  const endpoints = [
    'http://127.0.0.1:8000',
    'http://127.0.0.1:8000/docs',
    'http://127.0.0.1:8000/api/v1',
    'http://localhost:8000',
    'http://localhost:8000/docs',
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing: ${endpoint}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      const response = await fetch(endpoint, {
        signal: controller.signal,
        method: 'GET'
      });
      
      clearTimeout(timeoutId);
      
      console.log(`✅ ${endpoint}: ${response.status} ${response.statusText}`);
      
      if (response.ok && endpoint.includes('/docs')) {
        console.log('   📚 FastAPI docs are accessible');
        console.log('   🎯 Backend is definitely running!');
        return true;
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`⏰ ${endpoint}: Timeout (5s)`);
      } else {
        console.log(`❌ ${endpoint}: ${error.message}`);
      }
    }
  }

  console.log('\n❌ Backend appears to be down or inaccessible');
  console.log('💡 Try starting the backend with:');
  console.log('   cd backend');
  console.log('   python start_server.py');
  
  return false;
}

// Run the check
checkBackendStatus().then(isRunning => {
  if (isRunning) {
    console.log('\n🎉 Backend is running! You can proceed with frontend testing.');
  } else {
    console.log('\n🚨 Please start the backend before testing the frontend integration.');
  }
}).catch(console.error);
