'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { Building2, Upload } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import {
  useFileUpload,
  useSchoolProfile,
  useUpdateSchoolProfile,
} from '../../hooks/useSettingsQueries';
import { SchoolProfileSchema, type SchoolProfile } from '../../schemas/settings.schemas';
import { FileUpload } from '../FileUpload';

/**
 * SchoolProfileForm Component
 *
 * Form for managing school profile settings including:
 * - Basic school information
 * - Contact details
 * - Logo upload
 * - School description and motto
 */
export function SchoolProfileForm() {
  const { data: schoolProfile, isLoading } = useSchoolProfile();
  const updateMutation = useUpdateSchoolProfile();
  const uploadMutation = useFileUpload();

  const form = useForm<SchoolProfile>({
    resolver: zodResolver(SchoolProfileSchema),
    defaultValues: schoolProfile || {
      name: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      country: '',
      phone: '',
      email: '',
      website: '',
      established_year: new Date().getFullYear(),
      principal_name: '',
      description: '',
      motto: '',
    },
  });

  // Update form when data loads
  React.useEffect(() => {
    if (schoolProfile) {
      form.reset(schoolProfile);
    }
  }, [schoolProfile, form]);

  const onSubmit = async (data: SchoolProfile) => {
    try {
      await updateMutation.mutateAsync(data);
    } catch (error) {
      console.error('Failed to update school profile:', error);
    }
  };

  const handleLogoUpload = async (files: File[]) => {
    if (files.length > 0) {
      try {
        const result = await uploadMutation.mutateAsync({
          file: files[0],
          type: 'school-logo',
        });
        form.setValue('logo', result.url);
      } catch (error) {
        console.error('Failed to upload logo:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className='p-6 space-y-6'>
        <div className='animate-pulse space-y-4'>
          <div className='h-4 bg-slate-200 rounded w-1/4'></div>
          <div className='h-10 bg-slate-200 rounded'></div>
          <div className='h-10 bg-slate-200 rounded'></div>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className='p-6 space-y-6'>
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Building2 className='h-5 w-5 text-sky-600' />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='name'>School Name *</Label>
              <Input id='name' {...form.register('name')} placeholder='Enter school name' />
              {form.formState.errors.name && (
                <p className='text-sm text-red-600'>{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='principal_name'>Principal Name *</Label>
              <Input
                id='principal_name'
                {...form.register('principal_name')}
                placeholder='Enter principal name'
              />
              {form.formState.errors.principal_name && (
                <p className='text-sm text-red-600'>
                  {form.formState.errors.principal_name.message}
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='established_year'>Established Year *</Label>
              <Input
                id='established_year'
                type='number'
                {...form.register('established_year', { valueAsNumber: true })}
                placeholder='Enter established year'
              />
              {form.formState.errors.established_year && (
                <p className='text-sm text-red-600'>
                  {form.formState.errors.established_year.message}
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='motto'>School Motto</Label>
              <Input id='motto' {...form.register('motto')} placeholder='Enter school motto' />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='description'>Description</Label>
            <Textarea
              id='description'
              {...form.register('description')}
              placeholder='Enter school description'
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='email'>Email *</Label>
              <Input
                id='email'
                type='email'
                {...form.register('email')}
                placeholder='Enter email address'
              />
              {form.formState.errors.email && (
                <p className='text-sm text-red-600'>{form.formState.errors.email.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='phone'>Phone *</Label>
              <Input id='phone' {...form.register('phone')} placeholder='Enter phone number' />
              {form.formState.errors.phone && (
                <p className='text-sm text-red-600'>{form.formState.errors.phone.message}</p>
              )}
            </div>

            <div className='space-y-2 md:col-span-2'>
              <Label htmlFor='website'>Website</Label>
              <Input
                id='website'
                type='url'
                {...form.register('website')}
                placeholder='Enter website URL'
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Address */}
      <Card>
        <CardHeader>
          <CardTitle>Address</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='address'>Street Address *</Label>
            <Input id='address' {...form.register('address')} placeholder='Enter street address' />
            {form.formState.errors.address && (
              <p className='text-sm text-red-600'>{form.formState.errors.address.message}</p>
            )}
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='city'>City *</Label>
              <Input id='city' {...form.register('city')} placeholder='Enter city' />
              {form.formState.errors.city && (
                <p className='text-sm text-red-600'>{form.formState.errors.city.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='state'>State *</Label>
              <Input id='state' {...form.register('state')} placeholder='Enter state' />
              {form.formState.errors.state && (
                <p className='text-sm text-red-600'>{form.formState.errors.state.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='postal_code'>Postal Code *</Label>
              <Input
                id='postal_code'
                {...form.register('postal_code')}
                placeholder='Enter postal code'
              />
              {form.formState.errors.postal_code && (
                <p className='text-sm text-red-600'>{form.formState.errors.postal_code.message}</p>
              )}
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='country'>Country *</Label>
            <Input id='country' {...form.register('country')} placeholder='Enter country' />
            {form.formState.errors.country && (
              <p className='text-sm text-red-600'>{form.formState.errors.country.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Logo Upload */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Upload className='h-5 w-5 text-sky-600' />
            School Logo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload
            accept='image/*'
            maxSize={2}
            onFileSelect={handleLogoUpload}
            disabled={uploadMutation.isPending}
          />
          {form.watch('logo') && (
            <div className='mt-4'>
              <img
                src={form.watch('logo')}
                alt='School Logo'
                className='h-20 w-20 object-contain rounded-lg border'
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className='flex justify-end gap-2 pt-4 border-t'>
        <Button
          type='button'
          variant='outline'
          onClick={() => form.reset()}
          disabled={updateMutation.isPending}
        >
          Reset
        </Button>
        <Button
          type='submit'
          disabled={updateMutation.isPending}
          className='bg-gradient-to-r from-sky-600 to-violet-600 hover:from-sky-700 hover:to-violet-700'
        >
          {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </form>
  );
}
