'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CreditCard,
  DollarSign,
  Grid3X3,
  List,
  Plus,
  Search,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

// Demo fee data
const mockFeeRecords = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: 'Grade 10',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Paid',
    paidDate: '2024-01-10',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    class: 'Grade 9',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Pending',
    paidDate: null,
  },
  {
    id: 3,
    studentName: '<PERSON>',
    studentId: 'STU003',
    class: 'Grade 11',
    feeType: 'Lab Fee',
    amount: 300,
    dueDate: '2024-01-20',
    status: 'Overdue',
    paidDate: null,
  },
];

// Calculate stats
const calcStats = (records: typeof mockFeeRecords) => ({
  totalCollected: records.filter(r => r.status === 'Paid').reduce((sum, r) => sum + r.amount, 0),
  totalPending: records.filter(r => r.status === 'Pending').reduce((sum, r) => sum + r.amount, 0),
  totalStudents: new Set(records.map(r => r.studentId)).size,
  collectionRate: (
    (records.filter(r => r.status === 'Paid').length / records.length) *
    100
  ).toFixed(1),
});

export default function StudentFeePage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  const stats = calcStats(mockFeeRecords);

  const filteredRecords = useMemo(() => {
    return mockFeeRecords.filter(record => {
      const matchesSearch =
        record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.studentId.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'All' || record.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [searchTerm, statusFilter]);

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-3xl font-bold'>Student Fee Management</h1>
          <p className='text-muted-foreground'>Manage student fee payments and records</p>
        </div>
        <Button
          className='bg-blue-600 hover:bg-blue-700'
          onClick={() => router.push('/dashboard/student-fee/create')}
        >
          <Plus className='w-4 h-4 mr-2' /> Add Fee Record
        </Button>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        <StatCard
          title='Total Collected'
          value={`$${stats.totalCollected.toLocaleString()}`}
          icon={DollarSign}
          color='text-green-600'
          bg='bg-green-50'
        />
        <StatCard
          title='Pending Amount'
          value={`$${stats.totalPending.toLocaleString()}`}
          icon={TrendingDown}
          color='text-orange-600'
          bg='bg-orange-50'
        />
        <StatCard
          title='Total Students'
          value={stats.totalStudents}
          icon={Users}
          color='text-blue-600'
          bg='bg-blue-50'
        />
        <StatCard
          title='Collection Rate'
          value={`${stats.collectionRate}%`}
          icon={TrendingUp}
          color='text-purple-600'
          bg='bg-purple-50'
        />
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6 flex flex-col lg:flex-row gap-4 justify-between'>
          <div className='relative w-full lg:w-1/3'>
            <Search className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <Input
              placeholder='Search students...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
          <div className='flex gap-2 items-center'>
            <select
              className='border rounded px-3 py-2 text-sm'
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
            >
              <option value='All'>All Status</option>
              <option value='Paid'>Paid</option>
              <option value='Pending'>Pending</option>
              <option value='Overdue'>Overdue</option>
            </select>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size='icon'
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className='h-4 w-4' />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size='icon'
              onClick={() => setViewMode('table')}
            >
              <List className='h-4 w-4' />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      <Tabs value={viewMode} onValueChange={value => setViewMode(value as 'grid' | 'table')}>
        <TabsList className='grid grid-cols-2 w-full lg:w-[300px]'>
          <TabsTrigger value='grid'>Grid View</TabsTrigger>
          <TabsTrigger value='table'>Table View</TabsTrigger>
        </TabsList>

        {/* Grid */}
        <TabsContent value='grid' className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {filteredRecords.map(record => (
            <FeeCard
              key={record.id}
              record={record}
              onView={() => router.push(`/dashboard/student-fee/${record.id}`)}
            />
          ))}
          {filteredRecords.length === 0 && <EmptyState />}
        </TabsContent>

        {/* Table */}
        <TabsContent value='table'>
          <Card>
            <table className='w-full border-collapse'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='p-3 text-left'>Student</th>
                  <th className='p-3'>Class</th>
                  <th className='p-3'>Fee Type</th>
                  <th className='p-3'>Amount</th>
                  <th className='p-3'>Status</th>
                  <th className='p-3'>Due Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredRecords.map(record => (
                  <tr key={record.id} className='border-b'>
                    <td className='p-3'>{record.studentName}</td>
                    <td className='p-3 text-center'>{record.class}</td>
                    <td className='p-3 text-center'>{record.feeType}</td>
                    <td className='p-3 text-center'>${record.amount}</td>
                    <td className='p-3 text-center'>
                      <Badge
                        variant={
                          record.status === 'Paid'
                            ? 'default'
                            : record.status === 'Pending'
                            ? 'secondary'
                            : 'destructive'
                        }
                      >
                        {record.status}
                      </Badge>
                    </td>
                    <td className='p-3 text-center'>{record.dueDate}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Card>
          {filteredRecords.length === 0 && <EmptyState />}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Components
function StatCard({ title, value, icon: Icon, color, bg }: any) {
  return (
    <Card>
      <CardContent className='p-6 flex items-center gap-2'>
        <div className={`p-2 rounded-lg ${bg}`}>
          <Icon className={`h-4 w-4 ${color}`} />
        </div>
        <div>
          <p className='text-sm text-muted-foreground'>{title}</p>
          <p className='text-2xl font-bold'>{value}</p>
        </div>
      </CardContent>
    </Card>
  );
}

function FeeCard({ record, onView }: any) {
  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <div>
          <CardTitle className='text-lg'>{record.studentName}</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {record.studentId} • {record.class}
          </p>
        </div>
        <Badge
          variant={
            record.status === 'Paid'
              ? 'default'
              : record.status === 'Pending'
              ? 'secondary'
              : 'destructive'
          }
        >
          {record.status}
        </Badge>
      </CardHeader>
      <CardContent>
        <p className='font-semibold'>${record.amount}</p>
        <p className='text-sm text-muted-foreground'>{record.feeType}</p>
        <p className='text-xs mt-1'>Due: {record.dueDate}</p>
        <Button variant='outline' size='sm' className='mt-4 w-full' onClick={onView}>
          View Details
        </Button>
      </CardContent>
    </Card>
  );
}

function EmptyState() {
  return (
    <Card>
      <CardContent className='flex flex-col items-center justify-center py-16'>
        <div className='text-6xl mb-4'>💰</div>
        <h3 className='text-lg font-semibold mb-2'>No Fee Records Found</h3>
        <p className='text-muted-foreground text-center mb-4'>
          Adjust your filters or add a new fee record to see data here.
        </p>
      </CardContent>
    </Card>
  );
}

// 'use client';

// import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import { PageHeader } from '@/components/ui/page-header';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import {
//   CreditCard,
//   DollarSign,
//   Plus,
//   Search,
//   TrendingDown,
//   TrendingUp,
//   Users,
// } from 'lucide-react';
// import { useRouter } from 'next/navigation';
// import { useState } from 'react';

// // Mock data for demonstration
// const mockFeeStats = {
//   totalCollected: 125000,
//   totalPending: 35000,
//   totalStudents: 450,
//   collectionRate: 78,
// };

// const mockFeeRecords = [
//   {
//     id: 1,
//     studentName: 'John Doe',
//     studentId: 'STU001',
//     class: 'Grade 10',
//     feeType: 'Tuition Fee',
//     amount: 1500,
//     dueDate: '2024-01-15',
//     status: 'Paid',
//     paidDate: '2024-01-10',
//   },
//   {
//     id: 2,
//     studentName: 'Jane Smith',
//     studentId: 'STU002',
//     class: 'Grade 9',
//     feeType: 'Tuition Fee',
//     amount: 1500,
//     dueDate: '2024-01-15',
//     status: 'Pending',
//     paidDate: null,
//   },
//   {
//     id: 3,
//     studentName: 'Mike Johnson',
//     studentId: 'STU003',
//     class: 'Grade 11',
//     feeType: 'Lab Fee',
//     amount: 300,
//     dueDate: '2024-01-20',
//     status: 'Overdue',
//     paidDate: null,
//   },
// ];

// export default function StudentFeePage() {
//   const router = useRouter();
//   const [searchTerm, setSearchTerm] = useState('');
//   const [statusFilter, setStatusFilter] = useState('All');

//   const handleAddFee = () => {
//     router.push('/dashboard/student-fee/create');
//   };

//   const handleViewFee = (feeId: number) => {
//     // TODO: Navigate to fee details
//     console.log('View fee:', feeId);
//   };

//   const statsCards = [
//     {
//       title: 'Total Collected',
//       value: `$${mockFeeStats.totalCollected.toLocaleString()}`,
//       icon: DollarSign,
//       color: 'text-green-600',
//       bgColor: 'bg-green-50',
//       change: '+12%',
//       changeType: 'positive' as const,
//     },
//     {
//       title: 'Pending Amount',
//       value: `$${mockFeeStats.totalPending.toLocaleString()}`,
//       icon: TrendingDown,
//       color: 'text-orange-600',
//       bgColor: 'bg-orange-50',
//       change: '-5%',
//       changeType: 'negative' as const,
//     },
//     {
//       title: 'Total Students',
//       value: mockFeeStats.totalStudents.toString(),
//       icon: Users,
//       color: 'text-blue-600',
//       bgColor: 'bg-blue-50',
//       change: '+3%',
//       changeType: 'positive' as const,
//     },
//     {
//       title: 'Collection Rate',
//       value: `${mockFeeStats.collectionRate}%`,
//       icon: TrendingUp,
//       color: 'text-purple-600',
//       bgColor: 'bg-purple-50',
//       change: '+8%',
//       changeType: 'positive' as const,
//     },
//   ];

//   const filteredRecords = mockFeeRecords.filter(record => {
//     const matchesSearch =
//       record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       record.studentId.toLowerCase().includes(searchTerm.toLowerCase());
//     const matchesStatus = statusFilter === 'All' || record.status === statusFilter;
//     return matchesSearch && matchesStatus;
//   });

//   return (
//     <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
//       <PageHeader
//         title='Student Fee Management'
//         description='Manage student fee payments and financial records'
//         icon={CreditCard}
//         badge={{ label: 'Demo Data', variant: 'outline' }}
//         actions={[
//           {
//             label: 'Add Fee Record',
//             icon: Plus,
//             onClick: handleAddFee,
//           },
//         ]}
//       />

//       {/* Stats Cards */}
//       <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
//         {statsCards.map((stat, index) => (
//           <Card key={index}>
//             <CardContent className='p-6'>
//               <div className='flex items-center space-x-2'>
//                 <div className={`p-2 rounded-lg ${stat.bgColor}`}>
//                   <stat.icon className={`h-4 w-4 ${stat.color}`} />
//                 </div>
//                 <div>
//                   <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
//                   <p className='text-2xl font-bold'>{stat.value}</p>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         ))}
//       </div>

//       {/* Filters */}
//       <Card>
//         <CardContent className='p-6'>
//           <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
//             <div className='relative'>
//               <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
//               <Input
//                 placeholder='Search students...'
//                 value={searchTerm}
//                 onChange={e => setSearchTerm(e.target.value)}
//                 className='pl-10'
//               />
//             </div>

//             <Select value={statusFilter} onValueChange={setStatusFilter}>
//               <SelectTrigger>
//                 <SelectValue placeholder='Select status' />
//               </SelectTrigger>
//               <SelectContent>
//                 <SelectItem value='All'>All Status</SelectItem>
//                 <SelectItem value='Paid'>Paid</SelectItem>
//                 <SelectItem value='Pending'>Pending</SelectItem>
//                 <SelectItem value='Overdue'>Overdue</SelectItem>
//               </SelectContent>
//             </Select>

//             <div className='flex items-center text-sm text-muted-foreground'>
//               Showing {filteredRecords.length} of {mockFeeRecords.length} records
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       {/* Fee Records */}
//       <div className='grid grid-cols-1 gap-4'>
//         {filteredRecords.map(record => (
//           <Card key={record.id} className='hover:shadow-md transition-shadow'>
//             <CardContent className='p-6'>
//               <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
//                 <div className='flex items-center space-x-4'>
//                   <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center'>
//                     <CreditCard className='w-6 h-6 text-blue-600' />
//                   </div>
//                   <div>
//                     <h3 className='font-semibold'>{record.studentName}</h3>
//                     <p className='text-sm text-muted-foreground'>
//                       {record.studentId} • {record.class}
//                     </p>
//                   </div>
//                 </div>

//                 <div className='flex flex-col sm:flex-row sm:items-center gap-4'>
//                   <div className='text-center sm:text-right'>
//                     <p className='font-semibold'>${record.amount}</p>
//                     <p className='text-sm text-muted-foreground'>{record.feeType}</p>
//                   </div>

//                   <div className='text-center sm:text-right'>
//                     <Badge
//                       variant={
//                         record.status === 'Paid'
//                           ? 'default'
//                           : record.status === 'Pending'
//                           ? 'secondary'
//                           : 'destructive'
//                       }
//                     >
//                       {record.status}
//                     </Badge>
//                     <p className='text-xs text-muted-foreground mt-1'>Due: {record.dueDate}</p>
//                   </div>

//                   <Button variant='outline' size='sm' onClick={() => handleViewFee(record.id)}>
//                     View Details
//                   </Button>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         ))}
//       </div>

//       {filteredRecords.length === 0 && (
//         <Card>
//           <CardContent className='flex flex-col items-center justify-center py-16'>
//             <div className='text-6xl mb-4'>💰</div>
//             <h3 className='text-lg font-semibold mb-2'>No Fee Records Found</h3>
//             <p className='text-muted-foreground text-center mb-4'>
//               {searchTerm || statusFilter !== 'All'
//                 ? 'No fee records match your current filters.'
//                 : 'There are no fee records in the system yet.'}
//             </p>
//             <Button onClick={handleAddFee}>
//               <Plus className='mr-2 h-4 w-4' />
//               Add First Fee Record
//             </Button>
//           </CardContent>
//         </Card>
//       )}
//     </div>
//   );
// }
