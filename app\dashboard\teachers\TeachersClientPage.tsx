'use client';

/**
 * Teachers Client Page - Professional Implementation with Dummy + Real API Toggle
 *
 * Features:
 * - Complete CRUD operations with dummy data
 * - Search and filtering capabilities
 * - Responsive design with professional UI
 * - Easy toggle between dummy and real API
 * - Proper loading, error, and empty states
 * - ListCard component integration
 * - Professional stats dashboard
 */

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { apiClient } from '@/api/apiService';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ListCard } from '@/components/ui/list-card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useQueryBase } from '@/hooks/useQueryBase';
import {
  filterMockTeachers,
  mockDepartments,
  mockTeacherStats,
  paginateMockTeachers,
} from '@/lib/mockTeachers';

// Toggle between dummy data and real API
const USE_DUMMY_DATA = true; // TODO: Set to false when backend is ready

// UI Components

// Icons
import { BookOpen, Plus, Search, UserCheck, Users, UserX } from 'lucide-react';

// Types
import type { Teacher } from '@/types/global';

export default function TeachersClientPage() {
  const router = useRouter();

  // State for filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('All Departments');
  const [selectedStatus, setSelectedStatus] = useState('All');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12;

  // API Query (only used when USE_DUMMY_DATA is false)
  const {
    data: apiTeachers,
    isLoading,
    error,
    refetch,
  } = useQueryBase<Teacher[]>(
    ['teachers', searchTerm, selectedDepartment, selectedStatus],
    () =>
      apiClient.get('/teachers', {
        params: {
          search: searchTerm || undefined,
          department: selectedDepartment !== 'All Departments' ? selectedDepartment : undefined,
          status: selectedStatus !== 'All' ? selectedStatus : undefined,
        },
      }),
    {
      enabled: !USE_DUMMY_DATA,
    }
  );

  // Get filtered data based on toggle
  const getFilteredTeachers = () => {
    if (USE_DUMMY_DATA) {
      const filters: {
        search?: string;
        department?: string;
        status?: string;
        subject?: string;
      } = {};

      if (searchTerm) {
        filters.search = searchTerm;
      }
      if (selectedDepartment !== 'All Departments') {
        filters.department = selectedDepartment;
      }
      if (selectedStatus !== 'All') {
        filters.status = selectedStatus;
      }

      return filterMockTeachers(filters);
    }
    return apiTeachers || [];
  };

  const filteredTeachers = getFilteredTeachers();
  const paginatedResult = paginateMockTeachers(filteredTeachers, currentPage, pageSize);

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleDepartmentChange = (value: string) => {
    setSelectedDepartment(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    setCurrentPage(1);
  };

  const handleAddTeacher = () => {
    router.push('/dashboard/teachers/create');
  };

  const handleViewTeacher = (teacher: Teacher) => {
    router.push(`/dashboard/teachers/${teacher.id}`);
  };

  const handleEditTeacher = (teacher: Teacher) => {
    router.push(`/dashboard/teachers/${teacher.id}/edit`);
  };

  const handleDeleteTeacher = (teacher: Teacher) => {
    // TODO: Implement delete teacher functionality
    console.log('Delete teacher:', teacher);
  };

  // Loading state
  if (!USE_DUMMY_DATA && isLoading) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <Skeleton className='h-8 w-48 mb-2' />
            <Skeleton className='h-4 w-64' />
          </div>
          <Skeleton className='h-10 w-32' />
        </div>

        <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className='p-6'>
                <Skeleton className='h-16 w-full' />
              </CardContent>
            </Card>
          ))}
        </div>

        <Card>
          <CardContent className='p-6'>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className='h-10 w-full' />
              ))}
            </div>
          </CardContent>
        </Card>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardContent className='p-6'>
                <Skeleton className='h-32 w-full' />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (!USE_DUMMY_DATA && error) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <Card className='border-red-200 bg-red-50'>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>⚠️</div>
            <CardTitle className='mb-2 text-red-800'>Error Loading Teachers</CardTitle>
            <CardDescription className='text-red-600 text-center mb-4'>
              {error instanceof Error ? error.message : 'An unexpected error occurred'}
            </CardDescription>
            <Button onClick={() => refetch()} variant='outline' className='border-red-300'>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Statistics cards data
  const statsCards = [
    {
      title: 'Total Teachers',
      value: mockTeacherStats.total.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Teachers',
      value: mockTeacherStats.active.toString(),
      icon: UserCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      title: 'On Leave',
      value: mockTeacherStats.inactive.toString(),
      icon: UserX,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '-1%',
      changeType: 'negative' as const,
    },
    {
      title: 'Departments',
      value: mockDepartments.length.toString(),
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '0%',
      changeType: 'neutral' as const,
    },
  ];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Teachers</h1>
          <p className='text-muted-foreground'>
            Manage and view all teachers in the system
            <Badge variant='outline' className='ml-2'>
              {USE_DUMMY_DATA ? 'Demo Data' : 'Live Data'}
            </Badge>
          </p>
        </div>
        <Button onClick={handleAddTeacher} className='w-full sm:w-auto'>
          <Plus className='mr-2 h-4 w-4' />
          Add Teacher
        </Button>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search teachers...'
                value={searchTerm}
                onChange={e => handleSearch(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={selectedDepartment} onValueChange={handleDepartmentChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select department' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All Departments'>All Departments</SelectItem>
                {mockDepartments.map(dept => (
                  <SelectItem key={dept} value={dept as string}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Status</SelectItem>
                <SelectItem value='ACTIVE'>Active</SelectItem>
                <SelectItem value='INACTIVE'>Inactive</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex items-center text-sm text-muted-foreground'>
              Showing {paginatedResult.data.length} of {filteredTeachers.length} teachers
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Teachers Grid */}
      {paginatedResult.data.length === 0 ? (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>👨‍🏫</div>
            <CardTitle className='mb-2'>No Teachers Found</CardTitle>
            <CardDescription>
              {searchTerm || selectedDepartment !== 'All Departments' || selectedStatus !== 'All'
                ? 'No teachers match your current filters.'
                : 'There are no teachers in the system yet.'}
            </CardDescription>
            <Button onClick={handleAddTeacher} className='mt-4'>
              <Plus className='mr-2 h-4 w-4' />
              Add First Teacher
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
          {paginatedResult.data.map(teacher => (
            <ListCard
              key={teacher.id}
              title={teacher.name}
              subtitle={teacher.subject}
              description={teacher.department || 'No department'}
              status={{
                label: teacher.status || 'INACTIVE',
                variant: teacher.status === 'ACTIVE' ? 'default' : 'secondary',
              }}
              avatar={{
                src: `https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`,
                fallback: teacher.name
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')
                  .toUpperCase(),
              }}
              actions={[
                {
                  label: 'View',
                  onClick: () => handleViewTeacher(teacher),
                  variant: 'default',
                },
                {
                  label: 'Edit',
                  onClick: () => handleEditTeacher(teacher),
                  variant: 'default',
                },
                {
                  label: 'Delete',
                  onClick: () => handleDeleteTeacher(teacher),
                  variant: 'destructive',
                },
              ]}
              metadata={[
                { label: 'Email', value: teacher.email || 'N/A' },
                { label: 'Phone', value: teacher.phone || 'N/A' },
                { label: 'Hire Date', value: teacher.hire_date || 'N/A' },
              ]}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {paginatedResult.totalPages > 1 && (
        <div className='flex flex-col sm:flex-row items-center justify-between gap-4'>
          <p className='text-sm text-muted-foreground'>
            Showing {(currentPage - 1) * pageSize + 1} to{' '}
            {Math.min(currentPage * pageSize, filteredTeachers.length)} of {filteredTeachers.length}{' '}
            teachers
          </p>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className='flex space-x-1'>
              {Array.from({ length: Math.min(5, paginatedResult.totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => setCurrentPage(pageNum)}
                    className='w-10'
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant='outline'
              size='sm'
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, paginatedResult.totalPages))}
              disabled={currentPage === paginatedResult.totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
