import LoginPage from '@/app/(auth)/login/page';
import { useAuth } from '@/hooks/useAuth';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock the useAuth hook
jest.mock('@/hooks/useAuth');
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};

describe('LoginForm', () => {
  const mockLogin = jest.fn();

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: jest.fn(),
      clearError: jest.fn(),
      loginWithRedirect: jest.fn(),
      logoutWithRedirect: jest.fn(),

      updateUser: jest.fn(),
      checkTokenExpiry: jest.fn(),
      setLoading: jest.fn(),
      token: null,
      refreshToken: null,
      tokenExpiry: null,
      isAdmin: false,
      isTeacher: false,
      isStudent: false,
    });

    mockLogin.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form with all required fields', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    // Check for form elements
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: /remember me/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('displays validation errors for empty fields', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Try to submit without filling fields
    await user.click(submitButton);

    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for invalid email format', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Enter invalid email
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);

    // Check for email validation error
    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
    });
  });

  it('calls login function with correct credentials', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Fill in valid credentials
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    // Verify login was called with correct data
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: false,
      });
    });
  });

  it('shows loading state during login', async () => {
    const user = userEvent.setup();

    // Mock loading state
    mockUseAuth.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,
      login: mockLogin,
      logout: jest.fn(),
      clearError: jest.fn(),
      loginWithRedirect: jest.fn(),
      logoutWithRedirect: jest.fn(),
      updateUser: jest.fn(),
      checkTokenExpiry: jest.fn(),
      setLoading: jest.fn(),
      token: null,
      refreshToken: null,
      tokenExpiry: null,
      isAdmin: false,
      isTeacher: false,
      isStudent: false,
    });

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /signing in/i });

    // Button should be disabled and show loading text
    expect(submitButton).toBeDisabled();
    expect(submitButton).toHaveTextContent(/signing in/i);
  });

  it('displays error message when login fails', async () => {
    // Mock error state
    mockUseAuth.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: 'Invalid credentials',
      login: mockLogin,
      logout: jest.fn(),
      clearError: jest.fn(),
      loginWithRedirect: jest.fn(),
      logoutWithRedirect: jest.fn(),
      updateUser: jest.fn(),
      checkTokenExpiry: jest.fn(),
      setLoading: jest.fn(),
      token: null,
      refreshToken: null,
      tokenExpiry: null,
      isAdmin: false,
      isTeacher: false,
      isStudent: false,
    });

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    // Check for error message
    expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
  });

  it('handles remember me checkbox correctly', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const rememberMeCheckbox = screen.getByRole('checkbox', { name: /remember me/i });
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Fill form and check remember me
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(rememberMeCheckbox);
    await user.click(submitButton);

    // Verify login was called with rememberMe: true
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true,
      });
    });
  });

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    // Check for proper labels and ARIA attributes
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toHaveAttribute('type', 'email');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(emailInput).toHaveAttribute('aria-required', 'true');
    expect(passwordInput).toHaveAttribute('aria-required', 'true');
  });
});
