'use client';

import { ArrowL<PERSON><PERSON>, Award, BookOpen, Calendar, Clock, Edit, FileText, User } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Mock data for demonstration
const mockGradeRecords = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: 'Grade 10',
    subject: 'Mathematics',
    examType: 'Final Exam',
    examDate: '2024-02-15',
    totalMarks: 100,
    obtainedMarks: 85,
    percentage: 85,
    grade: 'A',
    remarks: 'Excellent performance in algebra and geometry',
    teacherName: 'Dr. <PERSON>',
    date: '2024-02-20',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    class: 'Grade 9',
    subject: 'Physics',
    examType: 'Mid-term',
    examDate: '2024-02-10',
    totalMarks: 75,
    obtainedMarks: 68,
    percentage: 90.7,
    grade: 'A+',
    remarks: 'Outstanding understanding of concepts',
    teacherName: 'Prof. <PERSON>',
    date: '2024-02-12',
  },
];

interface GradeDetailPageProps {
  params: { id: string };
}

export default function GradeDetailPage({ params }: GradeDetailPageProps) {
  const grade = mockGradeRecords.find(g => g.id === parseInt(params.id));

  if (!grade) {
    notFound();
  }

  const getGradeColor = (gradeValue: string) => {
    if (gradeValue.startsWith('A')) {
      return 'bg-green-100 text-green-800';
    }
    if (gradeValue.startsWith('B')) {
      return 'bg-blue-100 text-blue-800';
    }
    if (gradeValue.startsWith('C')) {
      return 'bg-yellow-100 text-yellow-800';
    }
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/grade'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Grades
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <Award className='w-8 h-8 text-purple-600' />
              Grade Details
            </h1>
            <p className='text-gray-600 mt-1'>View detailed grade information</p>
          </div>
        </div>
        <Link href={`/dashboard/grade/${grade.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Grade
          </Button>
        </Link>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        {/* Main Content */}
        <div className='lg:col-span-2 space-y-6'>
          {/* Student Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='w-5 h-5' />
                Student Information
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Student Name</label>
                  <p className='text-lg font-semibold'>{grade.studentName}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Student ID</label>
                  <p className='text-lg font-semibold'>{grade.studentId}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Class</label>
                  <p className='text-lg font-semibold'>{grade.class}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Subject</label>
                  <p className='text-lg font-semibold'>{grade.subject}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Exam Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='w-5 h-5' />
                Exam Information
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Exam Type</label>
                  <p className='text-lg font-semibold'>{grade.examType}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Exam Date</label>
                  <div className='flex items-center gap-2'>
                    <Calendar className='w-4 h-4 text-gray-500' />
                    <p className='text-lg font-semibold'>{grade.examDate}</p>
                  </div>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Teacher</label>
                  <p className='text-lg font-semibold'>{grade.teacherName}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Graded On</label>
                  <div className='flex items-center gap-2'>
                    <Clock className='w-4 h-4 text-gray-500' />
                    <p className='text-lg font-semibold'>{grade.date}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Remarks */}
          {grade.remarks && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <BookOpen className='w-5 h-5' />
                  Teacher's Remarks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-700 leading-relaxed'>{grade.remarks}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className='space-y-6'>
          {/* Grade Summary */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Award className='w-5 h-5' />
                Grade Summary
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center'>
                <div className={`inline-flex items-center px-4 py-2 rounded-full text-2xl font-bold ${getGradeColor(grade.grade)}`}>
                  {grade.grade}
                </div>
                <p className='text-sm text-muted-foreground mt-2'>Final Grade</p>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>
                  {grade.percentage}%
                </div>
                <p className='text-sm text-muted-foreground'>Score Percentage</p>
              </div>
              <div className='text-center'>
                <div className='text-xl font-semibold text-gray-700'>
                  {grade.obtainedMarks}/{grade.totalMarks}
                </div>
                <p className='text-sm text-muted-foreground'>Marks Obtained</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
