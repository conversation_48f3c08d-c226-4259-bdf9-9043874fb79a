/**
 * Exam Service
 * 
 * Handles all exam-related API calls
 */

import { apiUtils } from '../apiClient';
import type { 
  Exam, 
  ExamFilters, 
  ExamStats,
  PaginatedResponse 
} from '../../types';

export interface CreateExamData {
  title: string;
  subject: string;
  grade: string;
  class: string;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  total_marks: number;
  passing_marks: number;
  room: string;
  teacher_id: string;
  instructions?: string;
  exam_type?: 'MIDTERM' | 'FINAL' | 'QUIZ' | 'PRACTICAL' | 'ASSIGNMENT';
}

export interface UpdateExamData extends Partial<CreateExamData> {
  status?: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'POSTPONED';
}

export class ExamService {
  private static readonly BASE_URL = '/exams';

  static async getExams(query: ExamFilters = {}): Promise<PaginatedResponse<Exam>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<PaginatedResponse<Exam>>(url);
  }

  static async getExam(id: string): Promise<Exam> {
    return apiUtils.get<Exam>(`${this.BASE_URL}/${id}`);
  }

  static async createExam(data: CreateExamData): Promise<Exam> {
    return apiUtils.post<Exam>(this.BASE_URL, data);
  }

  static async updateExam(id: string, data: UpdateExamData): Promise<Exam> {
    return apiUtils.patch<Exam>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteExam(id: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/${id}`);
  }

  static async getExamStats(): Promise<ExamStats> {
    return apiUtils.get<ExamStats>(`${this.BASE_URL}/stats`);
  }

  static async getExamResults(examId: string): Promise<Array<{
    studentId: string;
    studentName: string;
    marksObtained: number;
    percentage: number;
    grade: string;
    status: 'PASS' | 'FAIL';
  }>> {
    return apiUtils.get<Array<{
      studentId: string;
      studentName: string;
      marksObtained: number;
      percentage: number;
      grade: string;
      status: 'PASS' | 'FAIL';
    }>>(`${this.BASE_URL}/${examId}/results`);
  }
}

export const examService = ExamService;
