/**
 * Mock Attendance Data
 *
 * Sample attendance records for development and testing
 */

import { Attendance } from '@/schemas/zodSchemas';

// Sample attendance data
export const mockAttendance: Attendance[] = [
  {
    id: '1',
    student_id: '1',
    student_name: '<PERSON>',
    class_id: '1',
    class_name: '10A',
    date: '2024-01-31',
    status: 'PRESENT',
    remarks: '',
  },
  {
    id: '2',
    student_id: '2',
    student_name: '<PERSON>',
    class_id: '2',
    class_name: '11B',
    date: '2024-01-31',
    status: 'PRESENT',
    remarks: '',
  },
  {
    id: '3',
    student_id: '3',
    student_name: '<PERSON>',
    class_id: '3',
    class_name: '9C',
    date: '2024-01-31',
    status: 'ABSENT',
    remarks: 'Sick leave',
  },
  {
    id: '4',
    student_id: '4',
    student_name: '<PERSON>',
    class_id: '4',
    class_name: '12A',
    date: '2024-01-31',
    status: 'LATE',
    remarks: 'Traffic delay',
  },
  {
    id: '5',
    student_id: '5',
    student_name: '<PERSON>',
    class_id: '5',
    class_name: '10B',
    date: '2024-01-31',
    status: 'PRESENT',
    remarks: '',
  },
];

// Available classes and statuses
export const mockAttendanceClasses = [
  'All Classes',
  '9A',
  '9B',
  '9C',
  '10A',
  '10B',
  '10C',
  '11A',
  '11B',
  '11C',
  '12A',
  '12B',
];

export const mockAttendanceStatuses = ['All Statuses', 'PRESENT', 'ABSENT', 'LATE', 'EXCUSED'];

// Helper functions
export const filterMockAttendance = (filters: {
  search?: string;
  class?: string;
  status?: string;
  date?: string;
}) => {
  let filtered = [...mockAttendance];

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(
      attendance =>
        attendance.student_name?.toLowerCase().includes(searchLower) ||
        attendance.class_name?.toLowerCase().includes(searchLower)
    );
  }

  if (filters.class && filters.class !== 'All Classes') {
    filtered = filtered.filter(attendance => attendance.class_name === filters.class);
  }

  if (filters.status && filters.status !== 'All Statuses') {
    filtered = filtered.filter(attendance => attendance.status === filters.status);
  }

  if (filters.date) {
    filtered = filtered.filter(attendance => attendance.date === filters.date);
  }

  return filtered;
};

export const paginateMockAttendance = (
  attendance: typeof mockAttendance,
  page: number = 1,
  pageSize: number = 10
) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    data: attendance.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      total: attendance.length,
      totalPages: Math.ceil(attendance.length / pageSize),
      hasNext: endIndex < attendance.length,
      hasPrev: page > 1,
    },
  };
};

// Attendance statistics
export const getAttendanceStats = () => {
  const total = mockAttendance.length;
  const present = mockAttendance.filter(a => a.status === 'PRESENT').length;
  const absent = mockAttendance.filter(a => a.status === 'ABSENT').length;
  const late = mockAttendance.filter(a => a.status === 'LATE').length;
  const excused = mockAttendance.filter(a => a.status === 'EXCUSED').length;

  return {
    total,
    present,
    absent,
    late,
    excused,
    presentRate: total > 0 ? Math.round((present / total) * 100) : 0,
    absentRate: total > 0 ? Math.round((absent / total) * 100) : 0,
    lateRate: total > 0 ? Math.round((late / total) * 100) : 0,
    excusedRate: total > 0 ? Math.round((excused / total) * 100) : 0,
  };
};

// Class attendance summary
export const getClassAttendanceSummary = (className: string) => {
  const classAttendance = mockAttendance.filter(a => a.class_name === className);
  return {
    class_name: className,
    total: classAttendance.length,
    present: classAttendance.filter(a => a.status === 'PRESENT').length,
    absent: classAttendance.filter(a => a.status === 'ABSENT').length,
    late: classAttendance.filter(a => a.status === 'LATE').length,
    excused: classAttendance.filter(a => a.status === 'EXCUSED').length,
  };
};

// Get attendance by student
export const getStudentAttendance = (studentId: string) => {
  return mockAttendance.filter(a => a.student_id === studentId);
};

// Get attendance by date range
export const getAttendanceByDateRange = (startDate: string, endDate: string) => {
  return mockAttendance.filter(a => a.date >= startDate && a.date <= endDate);
};

// Mock attendance statistics for dashboard
export const mockAttendanceStats = {
  total: mockAttendance.length,
  present: mockAttendance.filter(a => a.status === 'PRESENT').length,
  absent: mockAttendance.filter(a => a.status === 'ABSENT').length,
  late: mockAttendance.filter(a => a.status === 'LATE').length,
  excused: mockAttendance.filter(a => a.status === 'EXCUSED').length,
  presentRate: Math.round(
    (mockAttendance.filter(a => a.status === 'PRESENT').length / mockAttendance.length) * 100
  ),
  absentRate: Math.round(
    (mockAttendance.filter(a => a.status === 'ABSENT').length / mockAttendance.length) * 100
  ),
  lateRate: Math.round(
    (mockAttendance.filter(a => a.status === 'LATE').length / mockAttendance.length) * 100
  ),
  excusedRate: Math.round(
    (mockAttendance.filter(a => a.status === 'EXCUSED').length / mockAttendance.length) * 100
  ),
};
