/**
 * User Management Query Hooks
 *
 * TanStack Query hooks for user management operations:
 * - User CRUD operations with optimistic updates
 * - Search and filtering
 * - Role and status management
 * - Error handling and loading states
 */

import { useToast } from '@/hooks/use-toast';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { userQueryKeys, usersApi } from '../adapters/users.api';
import type {
  CreateUser,
  UpdateUser,
  UpdateUserStatus,
  User,
  UserListQuery,
  UserListResponse,
  UserRoleType,
} from '../schemas/users.schemas';

/**
 * Hook to fetch users with search and filtering
 */
export function useUsers(query: UserListQuery = {}) {
  return useQuery({
    queryKey: userQueryKeys.list(query),
    queryFn: () => usersApi.getUsers(query),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to fetch a single user by ID
 */
export function useUser(id: string) {
  return useQuery({
    queryKey: userQueryKeys.detail(id),
    queryFn: () => usersApi.getUser(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook to fetch user roles
 */
export function useUserRoles() {
  return useQuery({
    queryKey: userQueryKeys.roles(),
    queryFn: () => usersApi.getRoles(),
    staleTime: 30 * 60 * 1000, // 30 minutes (roles don't change often)
  });
}

/**
 * Hook to fetch user statistics
 */
export function useUserStats() {
  return useQuery({
    queryKey: userQueryKeys.stats(),
    queryFn: () => usersApi.getUserStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to create a new user
 */
export function useCreateUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateUser) => usersApi.createUser(data),
    onSuccess: newUser => {
      // Invalidate and refetch user lists
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.stats() });

      // Add the new user to existing cache if possible
      queryClient.setQueryData(userQueryKeys.detail(newUser.id), newUser);

      toast({
        title: 'User created',
        description: `${newUser.name} has been added successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to create user',
        description: error.message || 'An error occurred while creating the user.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to update a user
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUser }) => usersApi.updateUser(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: userQueryKeys.detail(id) });

      // Snapshot the previous value
      const previousUser = queryClient.getQueryData<User>(userQueryKeys.detail(id));

      // Optimistically update the cache
      if (previousUser) {
        queryClient.setQueryData<User>(userQueryKeys.detail(id), {
          ...previousUser,
          ...data,
          updatedAt: new Date().toISOString(),
        });
      }

      return { previousUser };
    },
    onSuccess: updatedUser => {
      // Update the specific user in cache
      queryClient.setQueryData(userQueryKeys.detail(updatedUser.id), updatedUser);

      // Invalidate user lists to reflect changes
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.stats() });

      toast({
        title: 'User updated',
        description: `${updatedUser.name} has been updated successfully.`,
      });
    },
    onError: (error: Error, { id }, context) => {
      // Rollback optimistic update
      if (context?.previousUser) {
        queryClient.setQueryData(userQueryKeys.detail(id), context.previousUser);
      }

      toast({
        title: 'Failed to update user',
        description: error.message || 'An error occurred while updating the user.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to update user status
 */
export function useUpdateUserStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserStatus }) =>
      usersApi.updateUserStatus(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: userQueryKeys.detail(id) });

      // Snapshot the previous value
      const previousUser = queryClient.getQueryData<User>(userQueryKeys.detail(id));

      // Optimistically update the cache
      if (previousUser) {
        queryClient.setQueryData<User>(userQueryKeys.detail(id), {
          ...previousUser,
          status: data.status,
          updatedAt: new Date().toISOString(),
        });
      }

      return { previousUser };
    },
    onSuccess: updatedUser => {
      // Update the specific user in cache
      queryClient.setQueryData(userQueryKeys.detail(updatedUser.id), updatedUser);

      // Invalidate user lists to reflect changes
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.stats() });

      toast({
        title: 'Status updated',
        description: `${updatedUser.name} is now ${updatedUser.status.toLowerCase()}.`,
      });
    },
    onError: (error: Error, { id }, context) => {
      // Rollback optimistic update
      if (context?.previousUser) {
        queryClient.setQueryData(userQueryKeys.detail(id), context.previousUser);
      }

      toast({
        title: 'Failed to update status',
        description: error.message || 'An error occurred while updating the user status.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to delete a user
 */
export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => usersApi.deleteUser(id),
    onMutate: async id => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: userQueryKeys.detail(id) });

      // Snapshot the previous value
      const previousUser = queryClient.getQueryData<User>(userQueryKeys.detail(id));

      // Optimistically remove from lists
      queryClient.setQueriesData<UserListResponse>({ queryKey: userQueryKeys.lists() }, old => {
        if (!old) return old;
        return {
          ...old,
          users: old.users.filter(user => user.id !== id),
          pagination: {
            ...old.pagination,
            total: old.pagination.total - 1,
          },
        };
      });

      return { previousUser };
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: userQueryKeys.detail(deletedId) });

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userQueryKeys.stats() });

      toast({
        title: 'User deleted',
        description: 'User has been deleted successfully.',
      });
    },
    onError: (error: Error, id, context) => {
      // Rollback optimistic updates
      if (context?.previousUser) {
        queryClient.setQueryData(userQueryKeys.detail(id), context.previousUser);
      }

      // Invalidate to refetch correct data
      queryClient.invalidateQueries({ queryKey: userQueryKeys.lists() });

      toast({
        title: 'Failed to delete user',
        description: error.message || 'An error occurred while deleting the user.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to get all user management mutations
 * Useful for checking loading states across all operations
 */
export function useUserMutations() {
  const createUser = useCreateUser();
  const updateUser = useUpdateUser();
  const updateUserStatus = useUpdateUserStatus();
  const deleteUser = useDeleteUser();

  return {
    createUser,
    updateUser,
    updateUserStatus,
    deleteUser,
    isLoading:
      createUser.isPending ||
      updateUser.isPending ||
      updateUserStatus.isPending ||
      deleteUser.isPending,
  };
}

/**
 * Hook for user search with debouncing
 */
export function useUserSearch(initialQuery: UserListQuery = {}) {
  const [query, setQuery] = React.useState<UserListQuery>(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = React.useState<UserListQuery>(initialQuery);

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  const usersQuery = useUsers(debouncedQuery);

  return {
    ...usersQuery,
    query,
    setQuery,
    isSearching: query.search !== debouncedQuery.search,
  };
}

// Re-export types for convenience
export type {
  CreateUser,
  UpdateUser,
  UpdateUserStatus,
  User,
  UserListQuery,
  UserListResponse,
  UserRoleType,
};
