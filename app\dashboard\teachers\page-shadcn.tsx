/**
 * Teachers Page - shadcn/ui Implementation with Full Responsive Design
 *
 * Features:
 * - Complete shadcn/ui component usage
 * - Responsive design across all breakpoints (sm, md, lg, xl, 2xl)
 * - Professional UI with proper component composition
 * - Tailwind CSS best practices
 * - Accessibility compliant
 */

'use client';

import { apiClient } from '@/api/apiService';
import { useQueryBase } from '@/hooks/useQueryBase';
import {
  filterMockTeachers,
  mockDepartments,
  mockTeacherStats,
  paginateMockTeachers,
} from '@/lib/mockTeachers';
import { useState } from 'react';

// shadcn/ui components
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Icons (you can replace with lucide-react icons)
import { Award, Building, Clock, Grid3X3, List, Plus, Search, Users } from 'lucide-react';

// Toggle between dummy data and real API
const USE_DUMMY_DATA = true; // TODO: Set to false when backend is ready

// Fetch function with filtering and pagination
const fetchTeachers = async (
  params: {
    search?: string;
    department?: string;
    status?: string;
    page?: number;
    pageSize?: number;
  } = {}
) => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 800));

    const filters: {
      search?: string;
      department?: string;
      status?: string;
      subject?: string;
    } = {};

    if (params.search) {
      filters.search = params.search;
    }
    if (params.department) {
      filters.department = params.department;
    }
    if (params.status) {
      filters.status = params.status;
    }

    const filteredTeachers = filterMockTeachers(filters);

    const paginatedResult = paginateMockTeachers(
      filteredTeachers,
      params.page || 1,
      params.pageSize || 12
    );

    return paginatedResult;
  }

  const { data } = await apiClient.get('/teachers', { params });
  return data;
};

export default function TeachersPageShadcn() {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const pageSize = 12;

  // Data fetching
  const {
    data: teachersResult,
    isLoading,
    error,
    refetch,
  } = useQueryBase(
    ['teachers', searchTerm, selectedDepartment, selectedStatus, currentPage],
    () => {
      const filters: {
        search?: string;
        department?: string;
        status?: string;
        page?: number;
        pageSize?: number;
      } = {
        page: currentPage,
        pageSize,
      };

      if (searchTerm) {
        filters.search = searchTerm;
      }
      if (selectedDepartment !== 'all') {
        filters.department = selectedDepartment;
      }
      if (selectedStatus !== 'all') {
        filters.status = selectedStatus;
      }

      return fetchTeachers(filters);
    }
  );

  const teachers = teachersResult?.data || [];
  const totalTeachers = teachersResult?.total || 0;
  const totalPages = teachersResult?.totalPages || 1;

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleDepartmentChange = (value: string) => {
    setSelectedDepartment(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    setCurrentPage(1);
  };

  // Loading state with skeletons
  if (isLoading) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
        {/* Header skeleton */}
        <div className='space-y-2'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-4 w-96' />
        </div>

        {/* Filters skeleton */}
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
          <Skeleton className='h-10 w-full' />
        </div>

        {/* Stats skeleton */}
        <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className='p-6'>
                <div className='flex items-center space-x-2'>
                  <Skeleton className='h-8 w-8 rounded' />
                  <div className='space-y-1'>
                    <Skeleton className='h-4 w-20' />
                    <Skeleton className='h-6 w-16' />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Grid skeleton */}
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className='p-6'>
                <div className='flex items-center space-x-4 mb-4'>
                  <Skeleton className='h-12 w-12 rounded-full' />
                  <div className='space-y-2'>
                    <Skeleton className='h-4 w-24' />
                    <Skeleton className='h-3 w-20' />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Skeleton className='h-3 w-full' />
                  <Skeleton className='h-3 w-3/4' />
                  <Skeleton className='h-3 w-1/2' />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <Card className='max-w-md mx-auto'>
          <CardHeader className='text-center'>
            <div className='mx-auto h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4'>
              <span className='text-red-600 text-2xl'>⚠️</span>
            </div>
            <CardTitle>Error Loading Teachers</CardTitle>
            <CardDescription>
              {error instanceof Error ? error.message : 'An unexpected error occurred'}
            </CardDescription>
          </CardHeader>
          <CardFooter className='flex justify-center'>
            <Button onClick={() => refetch()}>Try Again</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Teachers</h1>
          <p className='text-muted-foreground'>
            Manage and view all teachers in the system
            {USE_DUMMY_DATA && (
              <Badge variant='warning' className='ml-2'>
                Using Dummy Data
              </Badge>
            )}
          </p>
        </div>
        <Button className='w-full sm:w-auto'>
          <Plus className='mr-2 h-4 w-4' />
          Add Teacher
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search teachers...'
                value={searchTerm}
                onChange={e => handleSearch(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={selectedDepartment} onValueChange={handleDepartmentChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select department' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Departments</SelectItem>
                {mockDepartments.slice(1).map(dept => (
                  <SelectItem key={dept} value={dept as string}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Status</SelectItem>
                <SelectItem value='ACTIVE'>Active</SelectItem>
                <SelectItem value='INACTIVE'>Inactive</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex gap-2'>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size='icon'
                onClick={() => setViewMode('grid')}
                className='flex-1 sm:flex-none'
              >
                <Grid3X3 className='h-4 w-4' />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size='icon'
                onClick={() => setViewMode('table')}
                className='flex-1 sm:flex-none'
              >
                <List className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Users className='h-4 w-4 text-blue-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Total</p>
                <p className='text-2xl font-bold'>{mockTeacherStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 bg-green-100 rounded-lg'>
                <Award className='h-4 w-4 text-green-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Active</p>
                <p className='text-2xl font-bold'>{mockTeacherStats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 bg-purple-100 rounded-lg'>
                <Building className='h-4 w-4 text-purple-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Departments</p>
                <p className='text-2xl font-bold'>{mockTeacherStats.departments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 bg-orange-100 rounded-lg'>
                <Clock className='h-4 w-4 text-orange-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Avg. Experience</p>
                <p className='text-2xl font-bold'>{mockTeacherStats.averageExperience}y</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Tabs */}
      <Tabs value={viewMode} onValueChange={value => setViewMode(value as 'grid' | 'table')}>
        <TabsList className='grid w-full grid-cols-2 lg:w-[400px]'>
          <TabsTrigger value='grid'>Grid View</TabsTrigger>
          <TabsTrigger value='table'>Table View</TabsTrigger>
        </TabsList>

        {/* Grid View */}
        <TabsContent value='grid' className='space-y-6'>
          {teachers.length === 0 ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-16'>
                <div className='text-6xl mb-4'>👨‍🏫</div>
                <CardTitle className='mb-2'>No Teachers Found</CardTitle>
                <CardDescription>
                  {searchTerm || selectedDepartment !== 'all' || selectedStatus !== 'all'
                    ? 'No teachers match your current filters.'
                    : 'There are no teachers in the system yet.'}
                </CardDescription>
              </CardContent>
            </Card>
          ) : (
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4'>
              {teachers.map((teacher: any) => (
                <Card key={teacher.id} className='hover:shadow-md transition-shadow'>
                  <CardHeader className='pb-4'>
                    <div className='flex items-center space-x-3'>
                      <Avatar>
                        <AvatarImage
                          src={`https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`}
                        />
                        <AvatarFallback>
                          {teacher.name
                            .split(' ')
                            .map((n: string) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className='flex-1 min-w-0'>
                        <CardTitle className='text-base truncate'>{teacher.name}</CardTitle>
                        <CardDescription className='truncate'>{teacher.subject}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className='space-y-3'>
                    <div className='space-y-2 text-sm'>
                      <div className='flex justify-between'>
                        <span className='text-muted-foreground'>Department:</span>
                        <span className='font-medium truncate ml-2'>{teacher.department}</span>
                      </div>
                      <div className='flex justify-between'>
                        <span className='text-muted-foreground'>Email:</span>
                        <span className='font-medium truncate ml-2'>{teacher.email}</span>
                      </div>
                      <div className='flex justify-between items-center'>
                        <span className='text-muted-foreground'>Status:</span>
                        <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
                          {teacher.status}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className='flex gap-2'>
                    <Button variant='outline' size='sm' className='flex-1'>
                      View
                    </Button>
                    <Button variant='outline' size='sm' className='flex-1'>
                      Edit
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Table View */}
        <TabsContent value='table'>
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher</TableHead>
                  <TableHead className='hidden sm:table-cell'>Department</TableHead>
                  <TableHead className='hidden md:table-cell'>Subject</TableHead>
                  <TableHead className='hidden lg:table-cell'>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachers.map((teacher: any) => (
                  <TableRow key={teacher.id}>
                    <TableCell>
                      <div className='flex items-center space-x-3'>
                        <Avatar className='h-8 w-8'>
                          <AvatarImage
                            src={`https://api.dicebear.com/7.x/initials/svg?seed=${teacher.name}`}
                          />
                          <AvatarFallback className='text-xs'>
                            {teacher.name
                              .split(' ')
                              .map((n: string) => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className='font-medium'>{teacher.name}</div>
                          <div className='text-sm text-muted-foreground sm:hidden'>
                            {teacher.department}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className='hidden sm:table-cell'>{teacher.department}</TableCell>
                    <TableCell className='hidden md:table-cell'>{teacher.subject}</TableCell>
                    <TableCell className='hidden lg:table-cell'>{teacher.email}</TableCell>
                    <TableCell>
                      <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
                        {teacher.status}
                      </Badge>
                    </TableCell>
                    <TableCell className='text-right'>
                      <div className='flex justify-end gap-2'>
                        <Button variant='ghost' size='sm'>
                          View
                        </Button>
                        <Button variant='ghost' size='sm'>
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className='flex flex-col sm:flex-row items-center justify-between gap-4'>
          <p className='text-sm text-muted-foreground'>
            Showing {(currentPage - 1) * pageSize + 1} to{' '}
            {Math.min(currentPage * pageSize, totalTeachers)} of {totalTeachers} teachers
          </p>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className='flex space-x-1'>
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => setCurrentPage(pageNum)}
                    className='w-10'
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant='outline'
              size='sm'
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
