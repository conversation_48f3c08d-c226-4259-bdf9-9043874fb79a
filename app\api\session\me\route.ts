/**
 * Session Me Route Handler
 * 
 * Server-side user fetch for SSR and middleware
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getBackendUrl, AUTH_ENDPOINTS } from '@/lib/auth-config';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const authToken = cookieStore.get('auth_token')?.value;
    
    if (!authToken) {
      return NextResponse.json(
        { detail: 'No authentication token' },
        { status: 401 }
      );
    }
    
    // Fetch user from backend
    const response = await fetch(getBackendUrl(AUTH_ENDPOINTS.me), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      // Clear invalid token
      if (response.status === 401) {
        cookieStore.delete('auth_token');
      }
      
      const errorData = await response.text();
      return NextResponse.json(
        { detail: 'Authentication failed', backend_error: errorData },
        { status: response.status }
      );
    }
    
    const userData = await response.json();
    return NextResponse.json(userData);
    
  } catch (error) {
    console.error('Session me error:', error);
    return NextResponse.json(
      { detail: 'Failed to fetch user data' },
      { status: 500 }
    );
  }
}
