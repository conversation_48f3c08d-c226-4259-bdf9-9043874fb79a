{
  // ===== EDITOR SETTINGS =====
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit",
    "source.sortImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "editor.renderWhitespace": "boundary",
  "editor.rulers": [80, 100, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 100,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "editor.inlineSuggest.enabled": true,
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },

  // ===== FILE SETTINGS =====
  "files.autoSave": "onFocusChange",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.eol": "\n",
  "files.encoding": "utf8",
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.next": true,
    "**/node_modules": true,
    "**/out": true,
    "**/build": true,
    "**/dist": true,
    "**/.env.local": true,
    "**/*.log": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/out/**": true,
    "**/build/**": true,
    "**/dist/**": true
  },

  // ===== LANGUAGE-SPECIFIC SETTINGS =====
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
      "comments": "off",
      "strings": "off",
      "other": "off"
    }
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },

  // ===== TYPESCRIPT SETTINGS =====
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.includeCompletionsForModuleExports": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.inlayHints.parameterNames.enabled": "literals",
  "typescript.inlayHints.parameterTypes.enabled": false,
  "typescript.inlayHints.variableTypes.enabled": false,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.enumMemberValues.enabled": true,

  // ===== ESLINT SETTINGS =====
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.run": "onType",
  "eslint.workingDirectories": ["."],

  // ===== PRETTIER SETTINGS =====
  "prettier.enable": true,
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,

  // ===== EMMET SETTINGS =====
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html",
    "javascript": "html",
    "javascriptreact": "html"
  },
  "emmet.triggerExpansionOnTab": true,
  "emmet.showExpandedAbbreviation": "always",

  // ===== SEARCH SETTINGS =====
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true,
    "**/.next": true,
    "**/out": true,
    "**/build": true,
    "**/dist": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },
  "search.useIgnoreFiles": true,
  "search.useGlobalIgnoreFiles": true,

  // ===== GIT SETTINGS =====
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.autofetchPeriod": 180,
  "git.decorations.enabled": true,
  "git.showPushSuccessNotification": true,

  // ===== EXPLORER SETTINGS =====
  "explorer.confirmDelete": true,
  "explorer.confirmDragAndDrop": true,
  "explorer.compactFolders": false,
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.tsx": "${capture}.ts",
    "tsconfig.json": "tsconfig.*.json",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, bun.lockb",
    ".eslintrc.json": ".eslintignore, .eslintrc.js, .eslintrc.yaml, .eslintrc.yml",
    ".prettierrc.json": ".prettierignore, .prettierrc.js, .prettierrc.yaml, .prettierrc.yml",
    "tailwind.config.js": "tailwind.config.ts, postcss.config.js, postcss.config.ts",
    "next.config.js": "next.config.mjs, next.config.ts, next-env.d.ts",
    ".env": ".env.local, .env.development, .env.production, .env.example"
  },

  // ===== TERMINAL SETTINGS =====
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.fontSize": 14,
  "terminal.integrated.fontFamily": "Consolas, 'Courier New', monospace",
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.cursorStyle": "line",

  // ===== WORKBENCH SETTINGS =====
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.startupEditor": "newUntitledFile",
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": "vs-seti",
  "workbench.tree.indent": 20,
  "workbench.list.smoothScrolling": true,
  "workbench.editor.scrollToSwitchTabs": true,

  // ===== BREADCRUMBS SETTINGS =====
  "breadcrumbs.enabled": true,
  "breadcrumbs.filePath": "on",
  "breadcrumbs.symbolPath": "on",

  // ===== PROBLEMS SETTINGS =====
  "problems.decorations.enabled": true,
  "problems.showCurrentInStatus": true,

  // ===== EXTENSIONS SETTINGS =====
  "extensions.autoUpdate": "onlyEnabledExtensions",
  "extensions.ignoreRecommendations": false,

  // ===== SECURITY SETTINGS =====
  "security.workspace.trust.untrustedFiles": "prompt",
  "security.workspace.trust.banner": "always",
  "security.workspace.trust.startupPrompt": "always",

  // ===== TELEMETRY SETTINGS =====
  "telemetry.telemetryLevel": "error",

  // ===== CUSTOM SETTINGS FOR SCHOOL MANAGEMENT PROJECT =====
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single",
  "editor.linkedEditing": true,
  "editor.semanticHighlighting.enabled": true,
  "editor.unicodeHighlight.ambiguousCharacters": false,
  "editor.unicodeHighlight.invisibleCharacters": false,
  "diffEditor.ignoreTrimWhitespace": false,
  "scm.diffDecorations": "all",
  "debug.console.fontSize": 14,
  "debug.console.fontFamily": "Consolas, 'Courier New', monospace"
}
