# API Configuration - Single Source of Truth
# Copy this file to .env.local and modify as needed

# Development: Uses Next.js rewrites (/api/* → localhost:8000/api/v1/*)
# Production: Uses full URL
NEXT_PUBLIC_API_BASE=http://localhost:8000
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Auth Configuration - Choose 'auth' or 'users' namespace
# Mode A: NEXT_PUBLIC_AUTH_NAMESPACE=auth (uses /api/v1/auth/*)
# Mode B: NEXT_PUBLIC_AUTH_NAMESPACE=users (uses /api/v1/users/auth/*)
NEXT_PUBLIC_AUTH_NAMESPACE=users

NEXT_PUBLIC_APP_URL=http://localhost:3000

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# Backend URLs (for CSP compatibility)
# Both localhost:8000 and 127.0.0.1:8000 are allowed in CSP headers
# But in development, we use rewrites to avoid cross-origin issues
