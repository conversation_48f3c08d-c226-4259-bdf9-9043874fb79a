'use client';

import { Arrow<PERSON><PERSON><PERSON>, CalendarDays, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Mock data for demonstration
const mockEvents = [
  {
    id: 1,
    title: 'Annual Science Fair',
    description: 'Students showcase their innovative science projects and experiments.',
    date: '2024-03-15',
    time: '09:00 AM',
    endTime: '04:00 PM',
    location: 'Main Auditorium',
    category: 'Academic',
    status: 'Upcoming',
    organizer: 'Science Department',
    maxParticipants: 200,
    registeredParticipants: 145,
    requirements: 'All students must register by March 10th',
  },
  {
    id: 2,
    title: 'Parent-Teacher Conference',
    description: 'Individual meetings between parents and teachers to discuss student progress.',
    date: '2024-03-20',
    time: '02:00 PM',
    endTime: '06:00 PM',
    location: 'Classrooms',
    category: 'Meeting',
    status: 'Upcoming',
    organizer: 'Administration',
    maxParticipants: 100,
    registeredParticipants: 78,
    requirements: 'Parents must schedule appointments in advance',
  },
];

interface EventFormData {
  title: string;
  description: string;
  date: string;
  time: string;
  endTime: string;
  location: string;
  category: string;
  status: string;
  organizer: string;
  maxParticipants: string;
  requirements: string;
}

export default function EditEventPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState<EventFormData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const event = mockEvents.find(e => e.id === Number(params.id));
    if (event) {
      setFormData({
        title: event.title,
        description: event.description,
        date: event.date,
        time: event.time,
        endTime: event.endTime,
        location: event.location,
        category: event.category,
        status: event.status,
        organizer: event.organizer,
        maxParticipants: event.maxParticipants.toString(),
        requirements: event.requirements,
      });
    }
  }, [params.id]);

  if (!formData) {
    return <p className='p-6'>Event not found</p>;
  }

  const handleChange = (field: keyof EventFormData, value: string) => {
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated event:', formData);
      router.push(`/dashboard/events/${params.id}`);
    } catch (error) {
      console.error('Error updating event:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const categories = ['Academic', 'Sports', 'Cultural', 'Meeting', 'Workshop', 'Conference'];
  const statuses = ['Upcoming', 'Ongoing', 'Completed', 'Cancelled'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/events/${params.id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Event
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <CalendarDays className='w-8 h-8 text-blue-600' />
            Edit Event
          </h1>
          <p className='text-gray-600 mt-1'>Update event information</p>
        </div>
      </div>

      <div className='space-y-6'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Update the basic event details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='title'>Event Title</Label>
                <Input
                  id='title'
                  value={formData.title}
                  onChange={(e) => handleChange('title', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  rows={4}
                />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <Label htmlFor='category'>Category</Label>
                  <Select value={formData.category} onValueChange={(value) => handleChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor='status'>Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statuses.map(status => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Date & Time */}
        <Card>
          <CardHeader>
            <CardTitle>Schedule</CardTitle>
            <CardDescription>Set the event date and time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div>
                <Label htmlFor='date'>Date</Label>
                <Input
                  id='date'
                  type='date'
                  value={formData.date}
                  onChange={(e) => handleChange('date', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='time'>Start Time</Label>
                <Input
                  id='time'
                  value={formData.time}
                  onChange={(e) => handleChange('time', e.target.value)}
                  placeholder='e.g., 09:00 AM'
                />
              </div>
              <div>
                <Label htmlFor='endTime'>End Time</Label>
                <Input
                  id='endTime'
                  value={formData.endTime}
                  onChange={(e) => handleChange('endTime', e.target.value)}
                  placeholder='e.g., 04:00 PM'
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Location & Organization */}
        <Card>
          <CardHeader>
            <CardTitle>Location & Organization</CardTitle>
            <CardDescription>Set location and organizer details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label htmlFor='location'>Location</Label>
                <Input
                  id='location'
                  value={formData.location}
                  onChange={(e) => handleChange('location', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='organizer'>Organizer</Label>
                <Input
                  id='organizer'
                  value={formData.organizer}
                  onChange={(e) => handleChange('organizer', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='maxParticipants'>Max Participants</Label>
                <Input
                  id='maxParticipants'
                  type='number'
                  value={formData.maxParticipants}
                  onChange={(e) => handleChange('maxParticipants', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>Requirements</CardTitle>
            <CardDescription>Add any special requirements or instructions</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={formData.requirements}
              onChange={(e) => handleChange('requirements', e.target.value)}
              rows={3}
              placeholder='Enter any requirements or special instructions...'
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/events/${params.id}`}>
            <Button variant='outline'>Cancel</Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className='w-4 h-4 mr-2' />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
}
