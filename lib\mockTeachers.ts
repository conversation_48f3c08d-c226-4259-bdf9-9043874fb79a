/**
 * Mock Teachers Data for Development
 *
 * Realistic teacher data for testing UI components and functionality
 * This will be replaced with real API calls when backend is ready
 */

import { Teacher } from '@/types/global';

export const mockTeachers: Teacher[] = [
  {
    id: '1',
    name: '<PERSON>',
    subject: 'Mathematics',
    email: '<EMAIL>',
    department: 'Mathematics',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2020-08-15',
  },
  {
    id: '2',
    name: '<PERSON>',
    subject: 'Physics',
    email: '<EMAIL>',
    department: 'Science',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2019-09-01',
  },
  {
    id: '3',
    name: '<PERSON>',
    subject: 'English Literature',
    email: '<EMAIL>',
    department: 'English',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2021-01-10',
  },
  {
    id: '4',
    name: '<PERSON>',
    subject: 'Chemistry',
    email: '<EMAIL>',
    department: 'Science',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2018-07-20',
  },
  {
    id: '5',
    name: '<PERSON>',
    subject: 'History',
    email: '<EMAIL>',
    department: 'Social Studies',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2020-03-15',
  },
  {
    id: '6',
    name: 'Robert Taylor',
    subject: 'Physical Education',
    email: '<EMAIL>',
    department: 'Physical Education',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2017-08-01',
  },
  {
    id: '7',
    name: 'Jennifer Martinez',
    subject: 'Spanish',
    email: '<EMAIL>',
    department: 'Foreign Languages',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2019-02-14',
  },
  {
    id: '8',
    name: 'Christopher Lee',
    subject: 'Computer Science',
    email: '<EMAIL>',
    department: 'Technology',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2021-09-01',
  },
  {
    id: '9',
    name: 'Amanda White',
    subject: 'Art',
    email: '<EMAIL>',
    department: 'Arts',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2020-01-20',
  },
  {
    id: '10',
    name: 'James Garcia',
    subject: 'Biology',
    email: '<EMAIL>',
    department: 'Science',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2018-11-05',
  },
  {
    id: '11',
    name: 'Maria Rodriguez',
    subject: 'Music',
    email: '<EMAIL>',
    department: 'Arts',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2019-08-12',
  },
  {
    id: '12',
    name: 'Thomas Clark',
    subject: 'Geography',
    email: '<EMAIL>',
    department: 'Social Studies',
    phone: '(*************',
    status: 'INACTIVE',
    hire_date: '2016-05-30',
  },
  {
    id: '13',
    name: 'Patricia Lewis',
    subject: 'French',
    email: '<EMAIL>',
    department: 'Foreign Languages',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2020-10-01',
  },
  {
    id: '14',
    name: 'Daniel Walker',
    subject: 'Economics',
    email: '<EMAIL>',
    department: 'Social Studies',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2021-03-15',
  },
  {
    id: '15',
    name: 'Karen Hall',
    subject: 'Psychology',
    email: '<EMAIL>',
    department: 'Social Studies',
    phone: '(*************',
    status: 'ACTIVE',
    hire_date: '2019-06-01',
  },
];

// Mock teacher statistics
export const mockTeacherStats = {
  total: mockTeachers.length,
  active: mockTeachers.filter(t => t.status === 'ACTIVE').length,
  inactive: mockTeachers.filter(t => t.status === 'INACTIVE').length,
  departments: [...new Set(mockTeachers.map(t => t.department))].length,
  averageExperience: Math.round(
    mockTeachers.reduce((acc, teacher) => {
      const years = new Date().getFullYear() - new Date(teacher.hire_date!).getFullYear();
      return acc + years;
    }, 0) / mockTeachers.length
  ),
};

// Mock departments for filtering
export const mockDepartments = [
  'All Departments',
  ...Array.from(new Set(mockTeachers.map(t => t.department).filter(Boolean))).sort(),
];

// Mock teacher subjects for filtering
export const mockSubjects = [
  'All Subjects',
  ...Array.from(new Set(mockTeachers.map(t => t.subject))).sort(),
];

// Helper function to get teacher by ID
export const getMockTeacherById = (id: string): Teacher | undefined => {
  return mockTeachers.find(teacher => teacher.id === id);
};

// Helper function to filter teachers
export const filterMockTeachers = (filters: {
  search?: string;
  department?: string;
  status?: string;
  subject?: string;
}) => {
  return mockTeachers.filter(teacher => {
    const matchesSearch =
      !filters.search ||
      teacher.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      teacher.email?.toLowerCase().includes(filters.search.toLowerCase()) ||
      teacher.subject.toLowerCase().includes(filters.search.toLowerCase());

    const matchesDepartment =
      !filters.department ||
      filters.department === 'All Departments' ||
      teacher.department === filters.department;

    const matchesStatus =
      !filters.status || filters.status === 'All' || teacher.status === filters.status;

    const matchesSubject =
      !filters.subject || filters.subject === 'All Subjects' || teacher.subject === filters.subject;

    return matchesSearch && matchesDepartment && matchesStatus && matchesSubject;
  });
};

// Helper function to paginate teachers
export const paginateMockTeachers = (
  teachers: Teacher[],
  page: number = 1,
  pageSize: number = 10
) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const totalPages = Math.ceil(teachers.length / pageSize);

  return {
    data: teachers.slice(startIndex, endIndex),
    total: teachers.length,
    page,
    pageSize,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
};

// CRUD operations for mock data (for development)
let mutableMockTeachers = [...mockTeachers];

export const createMockTeacher = (
  teacherData: Omit<Teacher, 'id' | 'created_at' | 'updated_at'>
): Teacher => {
  const newTeacher: Teacher = {
    id: Math.random().toString(36).substr(2, 9),
    ...teacherData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  mutableMockTeachers.push(newTeacher);
  return newTeacher;
};

export const updateMockTeacher = (id: string, updates: Partial<Teacher>): Teacher | null => {
  const index = mutableMockTeachers.findIndex(teacher => teacher.id === id);
  if (index === -1) return null;

  // Filter out undefined values from updates
  const filteredUpdates = Object.fromEntries(
    Object.entries(updates).filter(([_, value]) => value !== undefined)
  );

  const updatedTeacher: Teacher = {
    ...mutableMockTeachers[index],
    ...filteredUpdates,
    updated_at: new Date().toISOString(),
  };

  mutableMockTeachers[index] = updatedTeacher;
  return updatedTeacher;
};

export const deleteMockTeacher = (id: string): boolean => {
  const index = mutableMockTeachers.findIndex(teacher => teacher.id === id);
  if (index === -1) return false;

  mutableMockTeachers.splice(index, 1);
  return true;
};

export const resetMockTeachers = (): void => {
  mutableMockTeachers = [...mockTeachers];
};

export const getMutableMockTeachers = (): Teacher[] => {
  return [...mutableMockTeachers];
};
