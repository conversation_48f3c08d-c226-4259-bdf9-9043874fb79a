'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  AlertCircle,
  Bell,
  CheckCircle,
  Clock,
  Database,
  Shield,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';

// Store imports demonstrating best practices
import {
  useNotificationActions,
  useNotificationSelectors,
  useStoreActions,
  useStoreCommunication,
  useStoreComposition,
  useStoreDevelopmentTools,
  useStorePerformance,
  useStudentActions,
  useStudentSelectors,
} from '@/stores';

/**
 * State Management Demo Component
 *
 * Demonstrates comprehensive state management patterns:
 * - Store composition and cross-store communication
 * - Optimized selectors and action patterns
 * - Real-time updates and notifications
 * - Performance monitoring and debugging
 * - Development tools integration
 */
export function StateManagementDemo() {
  const [activeDemo, setActiveDemo] = useState<string>('composition');

  // Store composition - unified access to multiple stores
  const { isAuthenticated, user, studentCount, unreadCount, hasErrors, isLoading, isReady } =
    useStoreComposition();

  // Store actions - unified access to all actions
  const { fetchStudents, createStudent, fetchNotifications, markAsRead, connectNotifications } =
    useStoreActions();

  // Cross-store communication
  const { notifyStudentCreated, notifyStudentUpdated, notifyError } = useStoreCommunication();

  // Performance monitoring
  const { getStoreStats, logStoreStats } = useStorePerformance();

  // Development tools
  const { populateTestData, clearAllData } = useStoreDevelopmentTools();

  // Individual store selectors (optimized)
  const students = useStudentSelectors.filteredStudents();
  const selectedStudents = useStudentSelectors.selectedStudents();
  const studentsLoading = useStudentSelectors.isLoading();

  const notifications = useNotificationSelectors.filteredNotifications();
  const activeToasts = useNotificationSelectors.activeToasts();
  const notificationsLoading = useNotificationSelectors.isLoading();

  // Individual store actions
  const selectStudent = useStudentActions.selectStudent();
  const clearSelection = useStudentActions.clearSelection();
  const showToast = useNotificationActions.showToast();
  const dismissNotification = useNotificationActions.dismissNotification();

  // Demo functions
  const handleCreateTestStudent = async () => {
    try {
      const testStudent = {
        student_id: `TEST${Date.now()}`,
        first_name: 'Test',
        last_name: 'Student',
        email: `test${Date.now()}@school.edu`,
        phone: '+1234567890',
        class_name: 'Demo Class',
        grade_level: '10th Grade',
        status: 'ACTIVE' as const,
        enrollment_date: new Date().toISOString().split('T')[0],
      };

      const newStudent = await createStudent(testStudent);
      notifyStudentCreated(`${newStudent.first_name} ${newStudent.last_name}`);
    } catch (error) {
      notifyError('Creation Failed', 'Failed to create test student');
    }
  };

  const handleShowTestNotification = () => {
    const testNotification = {
      id: `demo_${Date.now()}`,
      title: 'Demo Notification',
      message: 'This is a demonstration of the notification system with real-time updates.',
      type: 'info' as const,
      priority: 'medium' as const,
      category: 'system' as const,
      createdAt: new Date().toISOString(),
      persistent: false,
      actionable: true,
      autoClose: true,
      closeDelay: 5000,
      actions: [
        {
          id: 'dismiss',
          label: 'Dismiss',
          action: 'dismiss',
          style: 'secondary' as const,
        },
      ],
    };

    showToast(testNotification);
  };

  const handlePerformanceTest = () => {
    const stats = getStoreStats();
    logStoreStats();

    notifyError(
      'Performance Stats',
      `Students: ${stats.students.count}, Notifications: ${stats.notifications.count}`,
      'system'
    );
  };

  // Initialize stores on mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchStudents();
      fetchNotifications();
      connectNotifications();
    }
  }, [isAuthenticated, fetchStudents, fetchNotifications, connectNotifications]);

  if (!isAuthenticated) {
    return (
      <Card className='w-full max-w-4xl mx-auto'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Shield className='w-5 h-5' />
            Authentication Required
          </CardTitle>
          <CardDescription>Please log in to access the state management demo.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className='w-full max-w-6xl mx-auto space-y-6'>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Database className='w-6 h-6' />
            🧠 State Management Demo
          </CardTitle>
          <CardDescription>
            Comprehensive demonstration of Zustand best practices with domain-specific stores,
            middleware, and cross-store communication patterns.
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Status Overview */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-6'>
            <div className='flex items-center space-x-2'>
              <Users className='w-4 h-4 text-blue-600' />
              <span className='text-sm font-medium'>Students: {studentCount}</span>
            </div>
            <div className='flex items-center space-x-2'>
              <Bell className='w-4 h-4 text-orange-600' />
              <span className='text-sm font-medium'>Notifications: {unreadCount}</span>
            </div>
            <div className='flex items-center space-x-2'>
              {isReady ? (
                <CheckCircle className='w-4 h-4 text-green-600' />
              ) : (
                <Clock className='w-4 h-4 text-yellow-600' />
              )}
              <span className='text-sm font-medium'>{isReady ? 'Ready' : 'Loading'}</span>
            </div>
            <div className='flex items-center space-x-2'>
              {hasErrors ? (
                <AlertCircle className='w-4 h-4 text-red-600' />
              ) : (
                <CheckCircle className='w-4 h-4 text-green-600' />
              )}
              <span className='text-sm font-medium'>{hasErrors ? 'Has Errors' : 'No Errors'}</span>
            </div>
          </div>

          {/* Quick Actions */}
          <div className='flex flex-wrap gap-2'>
            <Button onClick={handleCreateTestStudent} size='sm' variant='outline'>
              <Users className='w-4 h-4 mr-2' />
              Create Test Student
            </Button>
            <Button onClick={handleShowTestNotification} size='sm' variant='outline'>
              <Bell className='w-4 h-4 mr-2' />
              Show Test Notification
            </Button>
            <Button onClick={handlePerformanceTest} size='sm' variant='outline'>
              <Activity className='w-4 h-4 mr-2' />
              Performance Stats
            </Button>
            {process.env.NODE_ENV === 'development' && (
              <>
                <Button onClick={populateTestData} size='sm' variant='outline'>
                  <Database className='w-4 h-4 mr-2' />
                  Populate Test Data
                </Button>
                <Button onClick={clearAllData} size='sm' variant='outline'>
                  Clear All Data
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Demo Tabs */}
      <Tabs value={activeDemo} onValueChange={setActiveDemo}>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='composition'>Store Composition</TabsTrigger>
          <TabsTrigger value='students'>Student Store</TabsTrigger>
          <TabsTrigger value='notifications'>Notifications</TabsTrigger>
          <TabsTrigger value='performance'>Performance</TabsTrigger>
        </TabsList>

        {/* Store Composition Demo */}
        <TabsContent value='composition' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Store Composition Patterns</CardTitle>
              <CardDescription>
                Unified access to multiple stores with cross-store communication
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <h4 className='font-semibold'>Current User</h4>
                  <div className='bg-gray-50 p-3 rounded'>
                    <p className='text-sm'>
                      <strong>Name:</strong> {user ? `${user.first_name} ${user.last_name}` : 'N/A'}
                    </p>
                    <p className='text-sm'>
                      <strong>Email:</strong> {user?.email || 'N/A'}
                    </p>
                    <p className='text-sm'>
                      <strong>Role:</strong> {user?.role || 'N/A'}
                    </p>
                  </div>
                </div>

                <div className='space-y-2'>
                  <h4 className='font-semibold'>Store Status</h4>
                  <div className='space-y-1'>
                    <Badge variant={isReady ? 'default' : 'secondary'}>
                      {isReady ? 'All Stores Ready' : 'Initializing'}
                    </Badge>
                    <Badge variant={hasErrors ? 'destructive' : 'default'}>
                      {hasErrors ? 'Has Errors' : 'No Errors'}
                    </Badge>
                    <Badge variant={isLoading ? 'secondary' : 'default'}>
                      {isLoading ? 'Loading' : 'Idle'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Student Store Demo */}
        <TabsContent value='students' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Student Store Management</CardTitle>
              <CardDescription>
                Normalized data structure with optimistic updates and bulk operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>Total Students: {students.length}</p>
                    <p className='text-sm text-gray-600'>Selected: {selectedStudents.length}</p>
                  </div>
                  <div className='space-x-2'>
                    <Button
                      onClick={() => students.slice(0, 3).forEach(s => selectStudent(s.id))}
                      size='sm'
                      variant='outline'
                    >
                      Select First 3
                    </Button>
                    <Button onClick={clearSelection} size='sm' variant='outline'>
                      Clear Selection
                    </Button>
                  </div>
                </div>

                {studentsLoading ? (
                  <div className='text-center py-4'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
                    <p className='text-sm text-gray-500 mt-2'>Loading students...</p>
                  </div>
                ) : (
                  <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
                    {students.slice(0, 6).map(student => (
                      <div
                        key={student.id}
                        className={`p-3 border rounded cursor-pointer transition-colors ${
                          selectedStudents.some(s => s.id === student.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => selectStudent(student.id)}
                      >
                        <p className='font-medium text-sm'>
                          {student.first_name} {student.last_name}
                        </p>
                        <p className='text-xs text-gray-500'>{student.email}</p>
                        <p className='text-xs text-gray-500'>{student.class_name}</p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Demo */}
        <TabsContent value='notifications' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Notification Management</CardTitle>
              <CardDescription>
                Real-time notifications with preferences and smart filtering
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>
                      Total Notifications: {notifications.length}
                    </p>
                    <p className='text-sm text-gray-600'>Active Toasts: {activeToasts.length}</p>
                  </div>
                  <Button onClick={handleShowTestNotification} size='sm'>
                    <Bell className='w-4 h-4 mr-2' />
                    Show Test Toast
                  </Button>
                </div>

                {notificationsLoading ? (
                  <div className='text-center py-4'>
                    <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto'></div>
                    <p className='text-sm text-gray-500 mt-2'>Loading notifications...</p>
                  </div>
                ) : (
                  <div className='space-y-2'>
                    {notifications.slice(0, 5).map(notification => (
                      <div key={notification.id} className='p-3 border rounded bg-gray-50'>
                        <div className='flex items-start justify-between'>
                          <div className='flex-1'>
                            <p className='font-medium text-sm'>{notification.title}</p>
                            <p className='text-xs text-gray-600'>{notification.message}</p>
                            <div className='flex items-center space-x-2 mt-1'>
                              <Badge variant='outline' className='text-xs'>
                                {notification.type}
                              </Badge>
                              <Badge variant='outline' className='text-xs'>
                                {notification.category}
                              </Badge>
                            </div>
                          </div>
                          <Button
                            onClick={() => dismissNotification(notification.id)}
                            size='sm'
                            variant='ghost'
                          >
                            ×
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Demo */}
        <TabsContent value='performance' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Performance Monitoring</CardTitle>
              <CardDescription>Real-time performance metrics and store statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <Button onClick={logStoreStats} className='w-full'>
                  <Activity className='w-4 h-4 mr-2' />
                  Log Performance Stats to Console
                </Button>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='bg-blue-50 p-4 rounded'>
                    <h4 className='font-semibold text-blue-800'>Auth Store</h4>
                    <p className='text-sm text-blue-600'>
                      Authenticated: {isAuthenticated ? 'Yes' : 'No'}
                    </p>
                    <p className='text-sm text-blue-600'>User Loaded: {user ? 'Yes' : 'No'}</p>
                  </div>

                  <div className='bg-green-50 p-4 rounded'>
                    <h4 className='font-semibold text-green-800'>Student Store</h4>
                    <p className='text-sm text-green-600'>Students: {students.length}</p>
                    <p className='text-sm text-green-600'>Selected: {selectedStudents.length}</p>
                  </div>

                  <div className='bg-orange-50 p-4 rounded'>
                    <h4 className='font-semibold text-orange-800'>Notification Store</h4>
                    <p className='text-sm text-orange-600'>Total: {notifications.length}</p>
                    <p className='text-sm text-orange-600'>Unread: {unreadCount}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
