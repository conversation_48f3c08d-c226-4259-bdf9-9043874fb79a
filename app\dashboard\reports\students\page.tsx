'use client';

/**
 * Students Report Page - Comprehensive Student Data Export
 *
 * Features:
 * - Filterable student list
 * - CSV, PDF, and Excel export
 * - Advanced filtering options
 * - Real-time data updates
 */

import { Badge } from '@/components/ui/badge';
import { <PERSON>ton } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bulkExport, EXPORT_CONFIGS, formatDateForExport } from '@/lib/export-utils';
import { Download, FileSpreadsheet, FileText, Search, Users } from 'lucide-react';
import { useState } from 'react';

// Mock student data for reports
const mockStudentsData = [
  {
    id: '1',
    name: '<PERSON>',
    studentId: 'STU001',
    grade: 'Grade 10',
    class: '10-A',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    enrollmentDate: '2023-09-01',
    guardianName: '<PERSON>',
    guardianPhone: '+****************',
    address: '123 Main St, City, State 12345',
  },
  {
    id: '2',
    name: 'Bob Smith',
    studentId: 'STU002',
    grade: 'Grade 10',
    class: '10-B',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    enrollmentDate: '2023-09-01',
    guardianName: 'Mary Smith',
    guardianPhone: '+****************',
    address: '456 Oak Ave, City, State 12345',
  },
  {
    id: '3',
    name: 'Carol Davis',
    studentId: 'STU003',
    grade: 'Grade 11',
    class: '11-A',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    enrollmentDate: '2022-09-01',
    guardianName: 'James Davis',
    guardianPhone: '+****************',
    address: '789 Pine Rd, City, State 12345',
  },
  {
    id: '4',
    name: 'David Wilson',
    studentId: 'STU004',
    grade: 'Grade 11',
    class: '11-B',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Inactive',
    enrollmentDate: '2022-09-01',
    guardianName: 'Linda Wilson',
    guardianPhone: '+****************',
    address: '321 Elm St, City, State 12345',
  },
  {
    id: '5',
    name: 'Eva Brown',
    studentId: 'STU005',
    grade: 'Grade 12',
    class: '12-A',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'Active',
    enrollmentDate: '2021-09-01',
    guardianName: 'Michael Brown',
    guardianPhone: '+****************',
    address: '654 Maple Dr, City, State 12345',
  },
];

const grades = ['All Grades', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
const classes = ['All Classes', '9-A', '9-B', '10-A', '10-B', '11-A', '11-B', '12-A', '12-B'];
const statuses = ['All Status', 'Active', 'Inactive', 'Graduated', 'Transferred'];

export default function StudentsReportPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('All Grades');
  const [selectedClass, setSelectedClass] = useState('All Classes');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [isExporting, setIsExporting] = useState(false);

  // Filter students based on search and filters
  const filteredStudents = mockStudentsData.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGrade = selectedGrade === 'All Grades' || student.grade === selectedGrade;
    const matchesClass = selectedClass === 'All Classes' || student.class === selectedClass;
    const matchesStatus = selectedStatus === 'All Status' || student.status === selectedStatus;
    
    return matchesSearch && matchesGrade && matchesClass && matchesStatus;
  });

  // Export handlers
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    
    try {
      const config = {
        ...EXPORT_CONFIGS.students,
        filename: `${EXPORT_CONFIGS.students.filename}-${new Date().toISOString().split('T')[0]}`,
        transformations: {
          enrollmentDate: formatDateForExport,
        }
      };
      
      await bulkExport(filteredStudents, format, config);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Inactive':
        return 'bg-red-100 text-red-800';
      case 'Graduated':
        return 'bg-blue-100 text-blue-800';
      case 'Transferred':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Students Report'
        description='Generate and export comprehensive student data reports'
        icon={Users}
        badge={{ label: `${filteredStudents.length} students`, variant: 'outline' }}
      />

      {/* Export Actions */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <h3 className='text-lg font-semibold mb-2'>Export Options</h3>
              <p className='text-sm text-muted-foreground'>
                Download student data in your preferred format
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => handleExport('csv')}
                disabled={isExporting || filteredStudents.length === 0}
              >
                <FileText className='w-4 h-4 mr-2' />
                Export CSV
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('excel')}
                disabled={isExporting || filteredStudents.length === 0}
              >
                <FileSpreadsheet className='w-4 h-4 mr-2' />
                Export Excel
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('pdf')}
                disabled={isExporting || filteredStudents.length === 0}
              >
                <Download className='w-4 h-4 mr-2' />
                Export PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Grade Filter */}
            <Select value={selectedGrade} onValueChange={setSelectedGrade}>
              <SelectTrigger>
                <SelectValue placeholder='Select grade' />
              </SelectTrigger>
              <SelectContent>
                {grades.map(grade => (
                  <SelectItem key={grade} value={grade}>
                    {grade}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Class Filter */}
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger>
                <SelectValue placeholder='Select class' />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls} value={cls}>
                    {cls}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className='mt-4 text-sm text-muted-foreground'>
            Showing {filteredStudents.length} of {mockStudentsData.length} students
          </div>
        </CardContent>
      </Card>

      {/* Students List */}
      <div className='space-y-4'>
        {filteredStudents.map(student => (
          <Card key={student.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    <h3 className='text-lg font-semibold'>{student.name}</h3>
                    <Badge className={getStatusColor(student.status)}>
                      {student.status}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 text-sm text-muted-foreground'>
                    <p>ID: {student.studentId}</p>
                    <p>Grade: {student.grade}</p>
                    <p>Class: {student.class}</p>
                    <p>Email: {student.email}</p>
                    <p>Phone: {student.phone}</p>
                    <p>Enrolled: {formatDateForExport(student.enrollmentDate)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredStudents.length === 0 && (
          <div className='text-center py-12'>
            <Users className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No students found</h3>
            <p className='text-gray-500'>
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
