'use client';

/**
 * Attendance Report Page - Comprehensive Attendance Data Export
 *
 * Features:
 * - Daily, weekly, monthly attendance reports
 * - CSV, PDF, and Excel export
 * - Date range filtering
 * - Attendance analytics
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bulkExport, EXPORT_CONFIGS, formatDateForExport } from '@/lib/export-utils';
import { Calendar, CheckCircle, Download, FileSpreadsheet, FileText, Search, XCircle } from 'lucide-react';
import { useState } from 'react';

// Mock attendance data for reports
const mockAttendanceData = [
  {
    id: '1',
    date: '2024-03-01',
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: '10-A',
    status: 'Present',
    timeIn: '08:00 AM',
    timeOut: '03:00 PM',
    remarks: '',
  },
  {
    id: '2',
    date: '2024-03-01',
    studentName: 'Bob Smith',
    studentId: 'STU002',
    class: '10-B',
    status: 'Absent',
    timeIn: '',
    timeOut: '',
    remarks: 'Sick leave',
  },
  {
    id: '3',
    date: '2024-03-01',
    studentName: 'Carol Davis',
    studentId: 'STU003',
    class: '11-A',
    status: 'Late',
    timeIn: '08:30 AM',
    timeOut: '03:00 PM',
    remarks: 'Traffic delay',
  },
  {
    id: '4',
    date: '2024-02-29',
    studentName: 'Alice Johnson',
    studentId: 'STU001',
    class: '10-A',
    status: 'Present',
    timeIn: '07:55 AM',
    timeOut: '03:00 PM',
    remarks: '',
  },
  {
    id: '5',
    date: '2024-02-29',
    studentName: 'David Wilson',
    studentId: 'STU004',
    class: '11-B',
    status: 'Present',
    timeIn: '08:05 AM',
    timeOut: '03:00 PM',
    remarks: '',
  },
];

const classes = ['All Classes', '9-A', '9-B', '10-A', '10-B', '11-A', '11-B', '12-A', '12-B'];
const statuses = ['All Status', 'Present', 'Absent', 'Late', 'Excused'];
const dateRanges = ['Today', 'This Week', 'This Month', 'Last Month', 'Custom Range'];

export default function AttendanceReportPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('All Classes');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [selectedDateRange, setSelectedDateRange] = useState('This Month');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isExporting, setIsExporting] = useState(false);

  // Filter attendance based on search and filters
  const filteredAttendance = mockAttendanceData.filter(record => {
    const matchesSearch = record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === 'All Classes' || record.class === selectedClass;
    const matchesStatus = selectedStatus === 'All Status' || record.status === selectedStatus;
    
    // Date filtering logic would go here based on selectedDateRange
    
    return matchesSearch && matchesClass && matchesStatus;
  });

  // Calculate attendance statistics
  const attendanceStats = {
    totalRecords: filteredAttendance.length,
    presentCount: filteredAttendance.filter(r => r.status === 'Present').length,
    absentCount: filteredAttendance.filter(r => r.status === 'Absent').length,
    lateCount: filteredAttendance.filter(r => r.status === 'Late').length,
    attendanceRate: filteredAttendance.length > 0 
      ? Math.round((filteredAttendance.filter(r => r.status === 'Present').length / filteredAttendance.length) * 100)
      : 0,
  };

  // Export handlers
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    
    try {
      const config = {
        ...EXPORT_CONFIGS.attendance,
        filename: `${EXPORT_CONFIGS.attendance.filename}-${new Date().toISOString().split('T')[0]}`,
        transformations: {
          date: formatDateForExport,
        }
      };
      
      await bulkExport(filteredAttendance, format, config);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present':
        return 'bg-green-100 text-green-800';
      case 'Absent':
        return 'bg-red-100 text-red-800';
      case 'Late':
        return 'bg-yellow-100 text-yellow-800';
      case 'Excused':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Present':
        return <CheckCircle className='w-4 h-4 text-green-600' />;
      case 'Absent':
        return <XCircle className='w-4 h-4 text-red-600' />;
      case 'Late':
        return <Calendar className='w-4 h-4 text-yellow-600' />;
      default:
        return <Calendar className='w-4 h-4 text-gray-600' />;
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Attendance Report'
        description='Generate and export comprehensive attendance data reports'
        icon={Calendar}
        badge={{ label: `${filteredAttendance.length} records`, variant: 'outline' }}
      />

      {/* Attendance Statistics */}
      <div className='grid grid-cols-2 lg:grid-cols-5 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-blue-600'>{attendanceStats.totalRecords}</p>
              <p className='text-sm text-muted-foreground'>Total Records</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-green-600'>{attendanceStats.presentCount}</p>
              <p className='text-sm text-muted-foreground'>Present</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-red-600'>{attendanceStats.absentCount}</p>
              <p className='text-sm text-muted-foreground'>Absent</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-yellow-600'>{attendanceStats.lateCount}</p>
              <p className='text-sm text-muted-foreground'>Late</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-purple-600'>{attendanceStats.attendanceRate}%</p>
              <p className='text-sm text-muted-foreground'>Attendance Rate</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <h3 className='text-lg font-semibold mb-2'>Export Options</h3>
              <p className='text-sm text-muted-foreground'>
                Download attendance data in your preferred format
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => handleExport('csv')}
                disabled={isExporting || filteredAttendance.length === 0}
              >
                <FileText className='w-4 h-4 mr-2' />
                Export CSV
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('excel')}
                disabled={isExporting || filteredAttendance.length === 0}
              >
                <FileSpreadsheet className='w-4 h-4 mr-2' />
                Export Excel
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('pdf')}
                disabled={isExporting || filteredAttendance.length === 0}
              >
                <Download className='w-4 h-4 mr-2' />
                Export PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Class Filter */}
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger>
                <SelectValue placeholder='Select class' />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls} value={cls}>
                    {cls}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Date Range Filter */}
            <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
              <SelectTrigger>
                <SelectValue placeholder='Select date range' />
              </SelectTrigger>
              <SelectContent>
                {dateRanges.map(range => (
                  <SelectItem key={range} value={range}>
                    {range}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom Date Range */}
          {selectedDateRange === 'Custom Range' && (
            <div className='grid grid-cols-2 gap-4 mt-4'>
              <div>
                <label className='text-sm font-medium mb-2 block'>Start Date</label>
                <Input
                  type='date'
                  value={startDate}
                  onChange={e => setStartDate(e.target.value)}
                />
              </div>
              <div>
                <label className='text-sm font-medium mb-2 block'>End Date</label>
                <Input
                  type='date'
                  value={endDate}
                  onChange={e => setEndDate(e.target.value)}
                />
              </div>
            </div>
          )}
          
          <div className='mt-4 text-sm text-muted-foreground'>
            Showing {filteredAttendance.length} attendance records
          </div>
        </CardContent>
      </Card>

      {/* Attendance Records */}
      <div className='space-y-4'>
        {filteredAttendance.map(record => (
          <Card key={record.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    {getStatusIcon(record.status)}
                    <h3 className='text-lg font-semibold'>{record.studentName}</h3>
                    <Badge className={getStatusColor(record.status)}>
                      {record.status}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-muted-foreground'>
                    <p>ID: {record.studentId}</p>
                    <p>Class: {record.class}</p>
                    <p>Date: {formatDateForExport(record.date)}</p>
                    <p>Time In: {record.timeIn || 'N/A'}</p>
                    <p>Time Out: {record.timeOut || 'N/A'}</p>
                    {record.remarks && <p>Remarks: {record.remarks}</p>}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredAttendance.length === 0 && (
          <div className='text-center py-12'>
            <Calendar className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No attendance records found</h3>
            <p className='text-gray-500'>
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
