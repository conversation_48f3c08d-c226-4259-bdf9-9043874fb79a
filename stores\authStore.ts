/**
 * Authentication Store - Zustand (Simplified for Cookie-based Auth)
 *
 * Simplified authentication state management:
 * - Basic user state (actual auth handled by React Query + cookies)
 * - UI state management
 * - Backward compatibility with existing components
 * - No token management (handled by httpOnly cookies)
 */

import { create } from 'zustand';

// Simplified types for backward compatibility
export interface User {
  id: string;
  email: string;
  name?: string;
  username: string;
  first_name?: string;
  last_name?: string;
  role?: 'ADMIN' | 'TEACHER' | 'STUDENT';
  avatar?: string;
  permissions?: string[];
  is_active: boolean;
  roles?: string[];
}

export interface AuthState {
  // Simplified state
  user: User | null;
  isLoading: boolean;
  error: string | null;

  // Simplified actions (actual auth handled by React Query)
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  logout: () => void;
  clearError: () => void;

  // Legacy compatibility methods
  setAuthData: (token: string, user: User) => void;
  initialize: () => void;
}

// Legacy types for backward compatibility
export interface LoginCredentials {
  email?: string;
  username?: string;
  password: string;
  rememberMe?: boolean;
}

// Helper function to convert user data for backward compatibility
const normalizeUser = (user: any): User => {
  return {
    id: user.id,
    email: user.email,
    username: user.username,
    name: user.name || [user.first_name, user.last_name].filter(Boolean).join(' ') || user.username,
    first_name: user.first_name,
    last_name: user.last_name,
    role: (user.roles?.[0]?.toUpperCase() as 'ADMIN' | 'TEACHER' | 'STUDENT') || 'STUDENT',
    avatar: user.avatar,
    permissions: user.permissions || [],
    is_active: user.is_active,
    roles: user.roles,
  };
};

// Create the simplified auth store
export const useAuthStore = create<AuthState>()((set, get) => ({
  // Initial state
  user: null,
  isLoading: false,
  error: null,

  // Actions
  setUser: (user: User | null) => {
    set({ user: user ? normalizeUser(user) : null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  logout: () => {
    set({
      user: null,
      error: null,
      isLoading: false,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  // Legacy compatibility methods
  setAuthData: (token: string, user: User) => {
    // For backward compatibility, just set the user
    set({ user: normalizeUser(user), isLoading: false, error: null });
  },

  initialize: () => {
    // For backward compatibility
    set({ isLoading: false });
  },
}));

// Selectors for backward compatibility
export const useAuth = () => {
  const { user, isLoading, error, setUser, logout, setAuthData, clearError, initialize } =
    useAuthStore();

  return {
    // Computed values for backward compatibility
    isAuthenticated: !!user,
    user,
    isLoading,
    error,

    // Actions
    setUser,
    logout,
    setAuthData,
    clearError,
    initialize,

    // Legacy methods (no-op for compatibility)
    login: async () => {
      throw new Error('Use React Query useLogin hook instead');
    },
    refreshAuth: async () => {},
    updateUser: (userData: Partial<User>) => {
      if (user) {
        setUser({ ...user, ...userData });
      }
    },
    checkTokenExpiry: () => !!user,
    setLoading: useAuthStore.getState().setLoading,
  };
};

export const useAuthUser = () => useAuthStore(state => state.user);
export const useAuthLoading = () => useAuthStore(state => state.isLoading);
export const useAuthError = () => useAuthStore(state => state.error);

// Auth utilities for backward compatibility
export const getAuthToken = () => null; // Tokens are in httpOnly cookies now
export const isAuthenticated = () => !!useAuthStore.getState().user;
export const getCurrentUser = () => useAuthStore.getState().user;
