/**
 * Users Admin React Query Hooks
 * 
 * Stable query keys and proper cache management for user management
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import * as usersService from '@/services/users';
import type {
  UserListParams,
  AdminUpdateUserPayload,
  UseUsersOptions,
  UseUserOptions,
} from '@/types/auth';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const usersKeys = {
  all: ['users'] as const,
  lists: () => [...usersKeys.all, 'list'] as const,
  list: (params?: UserListParams) => [...usersKeys.lists(), params] as const,
  details: () => [...usersKeys.all, 'detail'] as const,
  detail: (id: string) => [...usersKeys.details(), id] as const,
  stats: () => [...usersKeys.all, 'stats'] as const,
} as const;

// ============================================================================
// USERS QUERY HOOKS
// ============================================================================

/**
 * List users query with filtering and pagination
 */
export function useUsers(options?: UseUsersOptions) {
  return useQuery({
    queryKey: usersKeys.list(options?.params),
    queryFn: () => usersService.listUsers(options?.params),
    enabled: options?.enabled,
    staleTime: 30_000, // 30 seconds
  });
}

/**
 * Get single user query
 */
export function useUser(options: UseUserOptions) {
  return useQuery({
    queryKey: usersKeys.detail(options.id),
    queryFn: () => usersService.getUser(options.id),
    enabled: options.enabled && !!options.id,
    staleTime: 60_000, // 1 minute
  });
}

/**
 * Get user statistics query
 */
export function useUserStats() {
  return useQuery({
    queryKey: usersKeys.stats(),
    queryFn: usersService.getUserStats,
    staleTime: 60_000, // 1 minute
  });
}

// ============================================================================
// USERS MUTATION HOOKS
// ============================================================================

/**
 * Update user mutation
 */
export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: AdminUpdateUserPayload }) =>
      usersService.updateUser(id, data),
    onSuccess: (updatedUser, { id }) => {
      // Update the specific user in cache
      queryClient.setQueryData(usersKeys.detail(id), updatedUser);
      
      // Invalidate users list to refetch with updated data
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
      
      // Invalidate stats if they might be affected
      queryClient.invalidateQueries({ queryKey: usersKeys.stats() });
    },
  });
}

/**
 * Deactivate user mutation
 */
export function useDeactivateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: usersService.deactivateUser,
    onSuccess: (result, userId) => {
      // Optimistically update the user in cache
      queryClient.setQueryData(usersKeys.detail(userId), (oldUser: any) => {
        if (oldUser) {
          return { ...oldUser, is_active: false };
        }
        return oldUser;
      });
      
      // Invalidate users list to refetch with updated data
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: usersKeys.stats() });
    },
  });
}

/**
 * Activate user mutation
 */
export function useActivateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: usersService.activateUser,
    onSuccess: (result, userId) => {
      // Optimistically update the user in cache
      queryClient.setQueryData(usersKeys.detail(userId), (oldUser: any) => {
        if (oldUser) {
          return { ...oldUser, is_active: true };
        }
        return oldUser;
      });
      
      // Invalidate users list to refetch with updated data
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: usersKeys.stats() });
    },
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Toggle user activation status
 */
export function useToggleUserActivation() {
  const activateUser = useActivateUser();
  const deactivateUser = useDeactivateUser();
  
  return useMutation({
    mutationFn: ({ userId, isActive }: { userId: string; isActive: boolean }) => {
      if (isActive) {
        return deactivateUser.mutateAsync(userId);
      } else {
        return activateUser.mutateAsync(userId);
      }
    },
  });
}

/**
 * Prefetch user details
 */
export function usePrefetchUser() {
  const queryClient = useQueryClient();
  
  return (userId: string) => {
    queryClient.prefetchQuery({
      queryKey: usersKeys.detail(userId),
      queryFn: () => usersService.getUser(userId),
      staleTime: 60_000,
    });
  };
}
