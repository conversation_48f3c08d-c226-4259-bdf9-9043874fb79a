'use client';

/**
 * Exams Report Page - Comprehensive Examination Data Export
 *
 * Features:
 * - Exam schedules and results reports
 * - CSV, PDF, and Excel export
 * - Performance analytics
 * - Subject-wise analysis
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bulkExport, EXPORT_CONFIGS, formatDateForExport } from '@/lib/export-utils';
import { BookOpen, Download, FileSpreadsheet, FileText, Search, Trophy } from 'lucide-react';
import { useState } from 'react';

// Mock exams data for reports
const mockExamsData = [
  {
    id: '1',
    examName: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'Grade 10',
    date: '2024-03-15',
    duration: '2 hours',
    totalMarks: 100,
    studentsCount: 45,
    averageScore: 78.5,
    highestScore: 95,
    lowestScore: 42,
    passRate: 88,
    teacher: 'Dr. <PERSON>',
    status: 'Completed',
  },
  {
    id: '2',
    examName: 'Physics Final Exam',
    subject: 'Physics',
    grade: 'Grade 11',
    date: '2024-03-20',
    duration: '3 hours',
    totalMarks: 100,
    studentsCount: 38,
    averageScore: 72.3,
    highestScore: 92,
    lowestScore: 35,
    passRate: 82,
    teacher: 'Prof. Michael Chen',
    status: 'Completed',
  },
  {
    id: '3',
    examName: 'English Literature Essay',
    subject: 'English',
    grade: 'Grade 12',
    date: '2024-03-25',
    duration: '2.5 hours',
    totalMarks: 100,
    studentsCount: 32,
    averageScore: 81.2,
    highestScore: 98,
    lowestScore: 58,
    passRate: 94,
    teacher: 'Ms. Emily Davis',
    status: 'Completed',
  },
  {
    id: '4',
    examName: 'Chemistry Lab Test',
    subject: 'Chemistry',
    grade: 'Grade 11',
    date: '2024-04-01',
    duration: '1.5 hours',
    totalMarks: 50,
    studentsCount: 40,
    averageScore: 0,
    highestScore: 0,
    lowestScore: 0,
    passRate: 0,
    teacher: 'Dr. Robert Wilson',
    status: 'Scheduled',
  },
  {
    id: '5',
    examName: 'Biology Quiz',
    subject: 'Biology',
    grade: 'Grade 10',
    date: '2024-04-05',
    duration: '1 hour',
    totalMarks: 25,
    studentsCount: 42,
    averageScore: 0,
    highestScore: 0,
    lowestScore: 0,
    passRate: 0,
    teacher: 'Ms. Lisa Anderson',
    status: 'Scheduled',
  },
];

const subjects = ['All Subjects', 'Mathematics', 'Physics', 'English', 'Chemistry', 'Biology', 'History'];
const grades = ['All Grades', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
const statuses = ['All Status', 'Scheduled', 'Ongoing', 'Completed', 'Cancelled'];

export default function ExamsReportPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All Subjects');
  const [selectedGrade, setSelectedGrade] = useState('All Grades');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [isExporting, setIsExporting] = useState(false);

  // Filter exams based on search and filters
  const filteredExams = mockExamsData.filter(exam => {
    const matchesSearch = exam.examName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         exam.teacher.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'All Subjects' || exam.subject === selectedSubject;
    const matchesGrade = selectedGrade === 'All Grades' || exam.grade === selectedGrade;
    const matchesStatus = selectedStatus === 'All Status' || exam.status === selectedStatus;
    
    return matchesSearch && matchesSubject && matchesGrade && matchesStatus;
  });

  // Calculate exam statistics
  const examStats = {
    totalExams: filteredExams.length,
    completedExams: filteredExams.filter(e => e.status === 'Completed').length,
    scheduledExams: filteredExams.filter(e => e.status === 'Scheduled').length,
    totalStudents: filteredExams.reduce((sum, exam) => sum + exam.studentsCount, 0),
    averagePassRate: filteredExams.filter(e => e.status === 'Completed').length > 0
      ? Math.round(filteredExams.filter(e => e.status === 'Completed').reduce((sum, exam) => sum + exam.passRate, 0) / filteredExams.filter(e => e.status === 'Completed').length)
      : 0,
  };

  // Export handlers
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    
    try {
      const config = {
        ...EXPORT_CONFIGS.exams,
        filename: `${EXPORT_CONFIGS.exams.filename}-${new Date().toISOString().split('T')[0]}`,
        transformations: {
          date: formatDateForExport,
        }
      };
      
      await bulkExport(filteredExams, format, config);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Ongoing':
        return 'bg-blue-100 text-blue-800';
      case 'Scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceColor = (passRate: number) => {
    if (passRate >= 90) return 'text-green-600';
    if (passRate >= 75) return 'text-blue-600';
    if (passRate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Examinations Report'
        description='Generate and export comprehensive examination data reports'
        icon={BookOpen}
        badge={{ label: `${filteredExams.length} exams`, variant: 'outline' }}
      />

      {/* Exam Statistics */}
      <div className='grid grid-cols-2 lg:grid-cols-5 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-blue-600'>{examStats.totalExams}</p>
              <p className='text-sm text-muted-foreground'>Total Exams</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-green-600'>{examStats.completedExams}</p>
              <p className='text-sm text-muted-foreground'>Completed</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-yellow-600'>{examStats.scheduledExams}</p>
              <p className='text-sm text-muted-foreground'>Scheduled</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-purple-600'>{examStats.totalStudents}</p>
              <p className='text-sm text-muted-foreground'>Total Students</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-orange-600'>{examStats.averagePassRate}%</p>
              <p className='text-sm text-muted-foreground'>Avg Pass Rate</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <h3 className='text-lg font-semibold mb-2'>Export Options</h3>
              <p className='text-sm text-muted-foreground'>
                Download examination data in your preferred format
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => handleExport('csv')}
                disabled={isExporting || filteredExams.length === 0}
              >
                <FileText className='w-4 h-4 mr-2' />
                Export CSV
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('excel')}
                disabled={isExporting || filteredExams.length === 0}
              >
                <FileSpreadsheet className='w-4 h-4 mr-2' />
                Export Excel
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('pdf')}
                disabled={isExporting || filteredExams.length === 0}
              >
                <Download className='w-4 h-4 mr-2' />
                Export PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search exams...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Subject Filter */}
            <Select value={selectedSubject} onValueChange={setSelectedSubject}>
              <SelectTrigger>
                <SelectValue placeholder='Select subject' />
              </SelectTrigger>
              <SelectContent>
                {subjects.map(subject => (
                  <SelectItem key={subject} value={subject}>
                    {subject}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Grade Filter */}
            <Select value={selectedGrade} onValueChange={setSelectedGrade}>
              <SelectTrigger>
                <SelectValue placeholder='Select grade' />
              </SelectTrigger>
              <SelectContent>
                {grades.map(grade => (
                  <SelectItem key={grade} value={grade}>
                    {grade}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className='mt-4 text-sm text-muted-foreground'>
            Showing {filteredExams.length} examination records
          </div>
        </CardContent>
      </Card>

      {/* Exam Records */}
      <div className='space-y-4'>
        {filteredExams.map(exam => (
          <Card key={exam.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    <BookOpen className='w-5 h-5 text-blue-600' />
                    <h3 className='text-lg font-semibold'>{exam.examName}</h3>
                    <Badge className={getStatusColor(exam.status)}>
                      {exam.status}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-muted-foreground mb-3'>
                    <p>Subject: {exam.subject}</p>
                    <p>Grade: {exam.grade}</p>
                    <p>Date: {formatDateForExport(exam.date)}</p>
                    <p>Duration: {exam.duration}</p>
                    <p>Total Marks: {exam.totalMarks}</p>
                    <p>Students: {exam.studentsCount}</p>
                    <p>Teacher: {exam.teacher}</p>
                  </div>
                  
                  {exam.status === 'Completed' && (
                    <div className='grid grid-cols-2 lg:grid-cols-4 gap-4 pt-3 border-t'>
                      <div className='text-center'>
                        <p className='text-lg font-semibold text-blue-600'>{exam.averageScore.toFixed(1)}</p>
                        <p className='text-xs text-muted-foreground'>Average Score</p>
                      </div>
                      <div className='text-center'>
                        <p className='text-lg font-semibold text-green-600'>{exam.highestScore}</p>
                        <p className='text-xs text-muted-foreground'>Highest Score</p>
                      </div>
                      <div className='text-center'>
                        <p className='text-lg font-semibold text-red-600'>{exam.lowestScore}</p>
                        <p className='text-xs text-muted-foreground'>Lowest Score</p>
                      </div>
                      <div className='text-center'>
                        <p className={`text-lg font-semibold ${getPerformanceColor(exam.passRate)}`}>
                          {exam.passRate}%
                        </p>
                        <p className='text-xs text-muted-foreground'>Pass Rate</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredExams.length === 0 && (
          <div className='text-center py-12'>
            <BookOpen className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No examination records found</h3>
            <p className='text-gray-500'>
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
