'use client';

/**
 * Results Error Page - Professional Error Boundary
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useEffect } from 'react';

export default function ResultsError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Results page error:', error);
  }, [error]);

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      <Card className='max-w-md mx-auto'>
        <CardContent className='p-8 text-center'>
          <AlertTriangle className='w-16 h-16 text-red-500 mx-auto mb-4' />
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Something went wrong!
          </h2>
          <p className='text-gray-600 mb-6'>
            We encountered an error while loading the results page. Please try again.
          </p>
          <div className='space-y-3'>
            <Button onClick={reset} className='w-full'>
              <RefreshCw className='w-4 h-4 mr-2' />
              Try again
            </Button>
            <Button variant='outline' onClick={() => window.location.href = '/dashboard'} className='w-full'>
              Go to Dashboard
            </Button>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <details className='mt-4 text-left'>
              <summary className='cursor-pointer text-sm text-gray-500'>
                Error Details (Development)
              </summary>
              <pre className='mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto'>
                {error.message}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
