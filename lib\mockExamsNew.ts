/**
 * Mock Exams Data
 *
 * Comprehensive mock data for exams with:
 * - Exam scheduling and details
 * - Subject and class associations
 * - Marks and grading information
 * - Status tracking
 */

import { Exam } from '@/schemas/zodSchemas';

// Sample exam data
export const mockExams: Exam[] = [
  {
    id: '1',
    title: 'Mathematics Mid-Term Exam',
    subject_id: '1',
    subject_name: 'Mathematics',
    class_id: '4',
    class_name: '10A',
    exam_date: '2024-02-15',
    total_marks: 100,
    passing_marks: 40,
    status: 'SCHEDULED',
  },
  {
    id: '2',
    title: 'English Literature Quiz',
    subject_id: '2',
    subject_name: 'English',
    class_id: '4',
    class_name: '10A',
    exam_date: '2024-02-10',
    total_marks: 50,
    passing_marks: 20,
    status: 'COMPLETED',
  },
  {
    id: '3',
    title: 'Science Practical Exam',
    subject_id: '3',
    subject_name: 'Science',
    class_id: '5',
    class_name: '10B',
    exam_date: '2024-02-20',
    total_marks: 75,
    passing_marks: 30,
    status: 'SCHEDULED',
  },
  {
    id: '4',
    title: 'History Final Exam',
    subject_id: '4',
    subject_name: 'History',
    class_id: '6',
    class_name: '11A',
    exam_date: '2024-02-25',
    total_marks: 100,
    passing_marks: 35,
    status: 'SCHEDULED',
  },
  {
    id: '5',
    title: 'Physics Unit Test',
    subject_id: '5',
    subject_name: 'Physics',
    class_id: '8',
    class_name: '12A',
    exam_date: '2024-02-08',
    total_marks: 80,
    passing_marks: 32,
    status: 'COMPLETED',
  },
  {
    id: '6',
    title: 'Chemistry Lab Test',
    subject_id: '6',
    subject_name: 'Chemistry',
    class_id: '8',
    class_name: '12A',
    exam_date: '2024-02-12',
    total_marks: 60,
    passing_marks: 24,
    status: 'COMPLETED',
  },
  {
    id: '7',
    title: 'Geography Assessment',
    subject_id: '7',
    subject_name: 'Geography',
    class_id: '3',
    class_name: '9C',
    exam_date: '2024-02-18',
    total_marks: 70,
    passing_marks: 28,
    status: 'SCHEDULED',
  },
  {
    id: '8',
    title: 'Computer Science Project',
    subject_id: '8',
    subject_name: 'Computer Science',
    class_id: '7',
    class_name: '11B',
    exam_date: '2024-02-22',
    total_marks: 100,
    passing_marks: 50,
    status: 'SCHEDULED',
  },
];

// Available exam statuses
export const mockExamStatuses = ['All Statuses', 'SCHEDULED', 'COMPLETED', 'CANCELLED'];

// Available subjects for exams
export const mockExamSubjects = [
  'All Subjects',
  'Mathematics',
  'English',
  'Science',
  'History',
  'Physics',
  'Chemistry',
  'Geography',
  'Computer Science',
];

// Available grades for exams
export const mockExamGrades = ['All Grades', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];

// Helper functions
export const filterMockExams = (filters: {
  search?: string;
  subject?: string;
  class?: string;
  status?: string;
}) => {
  let filtered = [...mockExams];

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(
      exam =>
        exam.title?.toLowerCase().includes(searchLower) ||
        exam.subject_name?.toLowerCase().includes(searchLower) ||
        exam.class_name?.toLowerCase().includes(searchLower)
    );
  }

  if (filters.subject && filters.subject !== 'All Subjects') {
    filtered = filtered.filter(exam => exam.subject_name === filters.subject);
  }

  if (filters.class && filters.class !== 'All Classes') {
    filtered = filtered.filter(exam => exam.class_name === filters.class);
  }

  if (filters.status && filters.status !== 'All Statuses') {
    filtered = filtered.filter(exam => exam.status === filters.status);
  }

  return filtered;
};

export const paginateMockExams = (
  exams: typeof mockExams,
  page: number = 1,
  pageSize: number = 10
) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    data: exams.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      total: exams.length,
      totalPages: Math.ceil(exams.length / pageSize),
      hasNext: endIndex < exams.length,
      hasPrev: page > 1,
    },
  };
};

// Exam statistics
export const mockExamStats = {
  total: mockExams.length,
  scheduled: mockExams.filter(e => e.status === 'SCHEDULED').length,
  completed: mockExams.filter(e => e.status === 'COMPLETED').length,
  cancelled: mockExams.filter(e => e.status === 'CANCELLED').length,
  totalMarks: mockExams.reduce((sum, e) => sum + e.total_marks, 0),
  averageMarks: Math.round(mockExams.reduce((sum, e) => sum + e.total_marks, 0) / mockExams.length),
  passingRate: Math.round(
    (mockExams.reduce((sum, e) => sum + e.passing_marks, 0) /
      mockExams.reduce((sum, e) => sum + e.total_marks, 0)) *
      100
  ),
};

// Get exam by ID
export const getExamById = (id: string) => {
  return mockExams.find(exam => exam.id === id);
};

// Get exams by subject
export const getExamsBySubject = (subjectName: string) => {
  return mockExams.filter(exam => exam.subject_name === subjectName);
};

// Get exams by class
export const getExamsByClass = (className: string) => {
  return mockExams.filter(exam => exam.class_name === className);
};

// Get upcoming exams
export const getUpcomingExams = () => {
  const today = new Date().toISOString().split('T')[0];
  return mockExams
    .filter(exam => exam.exam_date >= today && exam.status === 'SCHEDULED')
    .sort((a, b) => a.exam_date.localeCompare(b.exam_date));
};
