'use client';

import { ModuleError } from '@/components/ui/module-error';

interface AttendanceErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function AttendanceError({ error, reset }: AttendanceErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Attendance"
      moduleIcon="✅"
      backHref="/dashboard"
    />
  );
}
