'use client';

/**
 * Student Create Page
 *
 * Features:
 * - Comprehensive student creation form
 * - Form validation with error handling
 * - Step-by-step wizard interface
 * - Image upload for profile picture
 * - CSV import functionality
 * - Responsive design
 * - Authentication handling
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Calendar,
  CheckCircle,
  GraduationCap,
  Mail,
  MapPin,
  Phone,
  Save,
  Upload,
  User,
  <PERSON>,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface StudentFormData {
  username: string;
  name: string;
  surname: string;
  admission_number: string;
  parent_id: string | null;
  class_id: string;
  grade_id: string;
  password: string;
  email: string;
  phone: string;
  address: string;
  sex: string;
  date_of_birth: string;
  guardian_name: string;
  guardian_phone: string;
  profile_picture?: string;
}

interface Parent {
  id: string;
  name: string;
}

interface Class {
  id: string;
  name: string;
}

interface Grade {
  id: string;
  name: string;
}

const initialFormData: StudentFormData = {
  username: '',
  name: '',
  surname: '',
  admission_number: '',
  parent_id: null,
  class_id: '',
  grade_id: '',
  password: '',
  email: '',
  phone: '',
  address: '',
  sex: 'OTHER',
  date_of_birth: '',
  guardian_name: '',
  guardian_phone: '',
};

const steps = [
  { id: 1, title: 'Personal Info', description: 'Basic student information' },
  { id: 2, title: 'Academic Info', description: 'Class and grade details' },
  { id: 3, title: 'Contact Info', description: 'Parent and emergency contacts' },
  { id: 4, title: 'Review', description: 'Review and submit' },
];

export default function StudentCreatePage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<StudentFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [parents, setParents] = useState<Parent[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [grades, setGrades] = useState<Grade[]>([]);
  const [token, setToken] = useState<string | null>(null);

  // Fetch authentication token from localStorage or your auth mechanism
  useEffect(() => {
    const storedToken = localStorage.getItem('auth_token');
    if (storedToken) {
      setToken(storedToken);
    } else {
      setError('Please log in to create a student');
      router.push('/login');
    }
  }, [router]);

  // Fetch parents, classes, and grades for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      if (!token) return;
      try {
        const [parentsRes, classesRes, gradesRes] = await Promise.all([
          fetch('/api/v1/parents', {
            headers: { Authorization: `Bearer ${token}` },
          }),
          fetch('/api/v1/classes', {
            headers: { Authorization: `Bearer ${token}` },
          }),
          fetch('/api/v1/grades', {
            headers: { Authorization: `Bearer ${token}` },
          }),
        ]);

        if (parentsRes.status === 401 || classesRes.status === 401 || gradesRes.status === 401) {
          setError('Authentication failed. Please log in again.');
          router.push('/login');
          return;
        }

        const parentsData = await parentsRes.json();
        const classesData = await classesRes.json();
        const gradesData = await gradesRes.json();

        setParents(parentsData.items || []);
        setClasses(classesData.items || []);
        setGrades(gradesData.items || []);
      } catch (err) {
        setError('Failed to fetch data for form');
        toast.error('Failed to fetch data');
      }
    };

    fetchData();
  }, [token, router]);

  const handleInputChange = (field: keyof StudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null);
  };

  const validateStep = (step: number): string | null => {
    switch (step) {
      case 1:
        if (!formData.username.trim()) return 'Username is required';
        if (!formData.name.trim()) return 'First name is required';
        if (!formData.surname.trim()) return 'Last name is required';
        if (!formData.admission_number.trim()) return 'Admission number is required';
        if (!formData.email.trim()) return 'Email is required';
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) return 'Invalid email format';
        if (!formData.phone.trim()) return 'Phone number is required';
        if (!formData.date_of_birth) return 'Date of birth is required';
        if (!formData.password.trim()) return 'Password is required';
        break;
      case 2:
        if (!formData.class_id) return 'Class is required';
        if (!formData.grade_id) return 'Grade level is required';
        break;
      case 3:
        if (!formData.guardian_name.trim()) return 'Guardian name is required';
        if (!formData.guardian_phone.trim()) return 'Guardian phone is required';
        break;
    }
    return null;
  };

  const handleNext = () => {
    const validationError = validateStep(currentStep);
    if (validationError) {
      setError(validationError);
      return;
    }
    setError(null);
    setCurrentStep(prev => Math.min(prev + 1, steps.length));
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
    setError(null);
  };

  const handleSubmit = async () => {
    const validationError = validateStep(3);
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!token) {
      setError('Authentication required. Please log in.');
      router.push('/login');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/students/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          username: formData.username,
          name: formData.name,
          surname: formData.surname,
          admission_number: formData.admission_number,
          parent_id: formData.parent_id || null,
          class_id: parseInt(formData.class_id),
          grade_id: parseInt(formData.grade_id),
          password: formData.password,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          sex: formData.sex,
          date_of_birth: formData.date_of_birth,
          guardian_name: formData.guardian_name,
          guardian_phone: formData.guardian_phone,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to create student');
      }

      toast.success('Student created successfully');
      router.push('/dashboard/students');
    } catch (err: any) {
      setError(err.message || 'Failed to create student');
      toast.error(err.message || 'Failed to create student');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCsvImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!token) {
      setError('Authentication required. Please log in.');
      router.push('/login');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/students/import', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to import students');
      }

      const result = await response.json();
      toast.success(`Imported ${result.created} students, updated ${result.updated}`);
      if (result.errors.length > 0) {
        toast.warning(`Encountered ${result.errors.length} errors during import`);
      }
      router.push('/dashboard/students');
    } catch (err: any) {
      setError(err.message || 'Failed to import students');
      toast.error(err.message || 'Failed to import students');
    } finally {
      setIsSubmitting(false);
    }
  };

  const progress = (currentStep / steps.length) * 100;

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button variant='outline' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Create New Student</h1>
            <p className='text-gray-600'>Add a new student to the system</p>
          </div>
        </div>
        <Button variant='outline' as='label'>
          <Upload className='w-4 h-4 mr-2' />
          Import CSV
          <input
            type='file'
            accept='.csv'
            className='hidden'
            onChange={handleCsvImport}
            disabled={isSubmitting}
          />
        </Button>
      </div>

      {/* Progress */}
      <Card>
        <CardContent className='p-6'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium text-gray-600'>
                Step {currentStep} of {steps.length}
              </span>
              <span className='text-sm text-gray-600'>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className='h-2' />
            <div className='flex justify-between text-xs text-gray-500'>
              {steps.map(step => (
                <div
                  key={step.id}
                  className={`text-center ${currentStep >= step.id ? 'text-blue-600' : ''}`}
                >
                  <div className='font-medium'>{step.title}</div>
                  <div>{step.description}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form Steps */}
      <div className='max-w-4xl mx-auto'>
        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='w-5 h-5' />
                Personal Information
              </CardTitle>
              <CardDescription>Enter the student's basic personal details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Profile Picture */}
              <div className='flex items-center space-x-6'>
                <Avatar className='w-24 h-24'>
                  <AvatarImage src={formData.profile_picture} />
                  <AvatarFallback className='text-2xl bg-gradient-to-br from-blue-500 to-purple-600 text-white'>
                    {formData.name[0] || 'S'}
                    {formData.surname[0] || 'T'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Button variant='outline' as='label'>
                    <Upload className='w-4 h-4 mr-2' />
                    Upload Photo
                    <input
                      type='file'
                      accept='image/*'
                      className='hidden'
                      onChange={e => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const url = URL.createObjectURL(file);
                          setFormData(prev => ({ ...prev, profile_picture: url }));
                        }
                      }}
                    />
                  </Button>
                  <p className='text-sm text-gray-600 mt-2'>
                    Optional: Square image, at least 200x200px
                  </p>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='username'>Username *</Label>
                  <Input
                    id='username'
                    value={formData.username}
                    onChange={e => handleInputChange('username', e.target.value)}
                    placeholder='Enter username'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='admission_number'>Admission Number *</Label>
                  <Input
                    id='admission_number'
                    value={formData.admission_number}
                    onChange={e => handleInputChange('admission_number', e.target.value)}
                    placeholder='e.g., ADM2024001'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='name'>First Name *</Label>
                  <Input
                    id='name'
                    value={formData.name}
                    onChange={e => handleInputChange('name', e.target.value)}
                    placeholder='Enter first name'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='surname'>Last Name *</Label>
                  <Input
                    id='surname'
                    value={formData.surname}
                    onChange={e => handleInputChange('surname', e.target.value)}
                    placeholder='Enter last name'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='email'>Email *</Label>
                  <div className='relative'>
                    <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='email'
                      type='email'
                      value={formData.email}
                      onChange={e => handleInputChange('email', e.target.value)}
                      placeholder='<EMAIL>'
                      className='pl-10'
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='phone'>Phone Number *</Label>
                  <div className='relative'>
                    <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='phone'
                      type='tel'
                      value={formData.phone}
                      onChange={e => handleInputChange('phone', e.target.value)}
                      placeholder='+****************'
                      className='pl-10'
                    />
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='date_of_birth'>Date of Birth *</Label>
                  <div className='relative'>
                    <Calendar className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='date_of_birth'
                      type='date'
                      value={formData.date_of_birth}
                      onChange={e => handleInputChange('date_of_birth', e.target.value)}
                      className='pl-10'
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='sex'>Gender</Label>
                  <Select
                    value={formData.sex}
                    onValueChange={value => handleInputChange('sex', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select gender' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='MALE'>Male</SelectItem>
                      <SelectItem value='FEMALE'>Female</SelectItem>
                      <SelectItem value='OTHER'>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password'>Password *</Label>
                <Input
                  id='password'
                  type='password'
                  value={formData.password}
                  onChange={e => handleInputChange('password', e.target.value)}
                  placeholder='Enter password'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='address'>Address</Label>
                <div className='relative'>
                  <MapPin className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Textarea
                    id='address'
                    value={formData.address}
                    onChange={e => handleInputChange('address', e.target.value)}
                    placeholder='Enter full address'
                    className='pl-10'
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Academic Information */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <GraduationCap className='w-5 h-5' />
                Academic Information
              </CardTitle>
              <CardDescription>Enter the student's academic details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='class_id'>Class *</Label>
                  <Select
                    value={formData.class_id}
                    onValueChange={value => handleInputChange('class_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select class' />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='grade_id'>Grade Level *</Label>
                  <Select
                    value={formData.grade_id}
                    onValueChange={value => handleInputChange('grade_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select grade level' />
                    </SelectTrigger>
                    <SelectContent>
                      {grades.map(grade => (
                        <SelectItem key={grade.id} value={grade.id}>
                          {grade.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Contact Information */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                Contact Information
              </CardTitle>
              <CardDescription>Enter parent/guardian and emergency contact details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='parent_id'>Parent/Guardian</Label>
                  <Select
                    value={formData.parent_id || ''}
                    onValueChange={value =>
                      handleInputChange('parent_id', value === '' ? null : value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select parent/guardian' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>None</SelectItem>
                      {parents.map(parent => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='guardian_name'>Guardian Name *</Label>
                  <Input
                    id='guardian_name'
                    value={formData.guardian_name}
                    onChange={e => handleInputChange('guardian_name', e.target.value)}
                    placeholder='Enter guardian name'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='guardian_phone'>Guardian Phone *</Label>
                  <div className='relative'>
                    <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='guardian_phone'
                      type='tel'
                      value={formData.guardian_phone}
                      onChange={e => handleInputChange('guardian_phone', e.target.value)}
                      placeholder='+****************'
                      className='pl-10'
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Review */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <CheckCircle className='w-5 h-5' />
                Review Information
              </CardTitle>
              <CardDescription>Please review all information before submitting</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h4 className='font-semibold mb-2'>Personal Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Username:</strong> {formData.username}
                    </p>
                    <p>
                      <strong>Admission Number:</strong> {formData.admission_number}
                    </p>
                    <p>
                      <strong>Name:</strong> {formData.name} {formData.surname}
                    </p>
                    <p>
                      <strong>Email:</strong> {formData.email}
                    </p>
                    <p>
                      <strong>Phone:</strong> {formData.phone}
                    </p>
                    <p>
                      <strong>Date of Birth:</strong> {formData.date_of_birth}
                    </p>
                    <p>
                      <strong>Gender:</strong> {formData.sex}
                    </p>
                    {formData.address && (
                      <p>
                        <strong>Address:</strong> {formData.address}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className='font-semibold mb-2'>Academic Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Class:</strong>{' '}
                      {classes.find(c => c.id === formData.class_id)?.name || 'N/A'}
                    </p>
                    <p>
                      <strong>Grade Level:</strong>{' '}
                      {grades.find(g => g.id === formData.grade_id)?.name || 'N/A'}
                    </p>
                  </div>

                  <h4 className='font-semibold mb-2 mt-4'>Contact Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Parent:</strong>{' '}
                      {parents.find(p => p.id === formData.parent_id)?.name || 'None'}
                    </p>
                    <p>
                      <strong>Guardian Name:</strong> {formData.guardian_name}
                    </p>
                    <p>
                      <strong>Guardian Phone:</strong> {formData.guardian_phone}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className='flex items-center justify-between pt-6'>
          <Button variant='outline' onClick={handlePrevious} disabled={currentStep === 1}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Previous
          </Button>

          {currentStep < steps.length ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className='w-4 h-4 ml-2' />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isSubmitting || !token}>
              {isSubmitting ? (
                <div className='flex items-center space-x-2'>
                  <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
                  <span>Creating...</span>
                </div>
              ) : (
                <div className='flex items-center space-x-2'>
                  <Save className='w-4 h-4' />
                  <span>Create Student</span>
                </div>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
// 'use client';

// /**
//  * Student Create Page
//  *
//  * Features:
//  * - Comprehensive student creation form
//  * - Form validation with error handling
//  * - Step-by-step wizard interface
//  * - Image upload for profile picture
//  * - Responsive design
//  */

// import { Alert, AlertDescription } from '@/components/ui/alert';
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import { Progress } from '@/components/ui/progress';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import { Textarea } from '@/components/ui/textarea';
// import {
//   AlertCircle,
//   ArrowLeft,
//   ArrowRight,
//   Calendar,
//   CheckCircle,
//   GraduationCap,
//   Mail,
//   MapPin,
//   Phone,
//   Save,
//   Upload,
//   User,
//   Users,
// } from 'lucide-react';
// import { useRouter } from 'next/navigation';
// import { useState } from 'react';
// import { toast } from 'sonner';

// interface StudentFormData {
//   first_name: string;
//   last_name: string;
//   email: string;
//   phone: string;
//   date_of_birth: string;
//   address: string;
//   class_name: string;
//   grade_level: string;
//   status: string;
//   parent_name: string;
//   parent_email: string;
//   parent_phone: string;
//   emergency_contact: string;
//   profile_picture?: string;
// }

// const initialFormData: StudentFormData = {
//   first_name: '',
//   last_name: '',
//   email: '',
//   phone: '',
//   date_of_birth: '',
//   address: '',
//   class_name: '',
//   grade_level: '',
//   status: 'ACTIVE',
//   parent_name: '',
//   parent_email: '',
//   parent_phone: '',
//   emergency_contact: '',
// };

// const steps = [
//   { id: 1, title: 'Personal Info', description: 'Basic student information' },
//   { id: 2, title: 'Academic Info', description: 'Class and grade details' },
//   { id: 3, title: 'Contact Info', description: 'Parent and emergency contacts' },
//   { id: 4, title: 'Review', description: 'Review and submit' },
// ];

// export default function StudentCreatePage() {
//   const router = useRouter();
//   const [currentStep, setCurrentStep] = useState(1);
//   const [formData, setFormData] = useState<StudentFormData>(initialFormData);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [error, setError] = useState<string | null>(null);

//   const handleInputChange = (field: keyof StudentFormData, value: string) => {
//     setFormData(prev => ({ ...prev, [field]: value }));
//     if (error) setError(null);
//   };

//   const validateStep = (step: number): string | null => {
//     switch (step) {
//       case 1:
//         if (!formData.first_name.trim()) return 'First name is required';
//         if (!formData.last_name.trim()) return 'Last name is required';
//         if (!formData.email.trim()) return 'Email is required';
//         if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) return 'Invalid email format';
//         if (!formData.phone.trim()) return 'Phone number is required';
//         if (!formData.date_of_birth) return 'Date of birth is required';
//         break;
//       case 2:
//         if (!formData.class_name.trim()) return 'Class is required';
//         if (!formData.grade_level.trim()) return 'Grade level is required';
//         break;
//       case 3:
//         if (!formData.parent_name.trim()) return 'Parent name is required';
//         if (!formData.parent_email.trim()) return 'Parent email is required';
//         if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.parent_email))
//           return 'Invalid parent email format';
//         break;
//     }
//     return null;
//   };

//   const handleNext = () => {
//     const validationError = validateStep(currentStep);
//     if (validationError) {
//       setError(validationError);
//       return;
//     }

//     setError(null);
//     setCurrentStep(prev => Math.min(prev + 1, steps.length));
//   };

//   const handlePrevious = () => {
//     setCurrentStep(prev => Math.max(prev - 1, 1));
//     setError(null);
//   };

//   const handleSubmit = async () => {
//     const validationError = validateStep(3); // Validate all required fields
//     if (validationError) {
//       setError(validationError);
//       return;
//     }

//     setIsSubmitting(true);
//     setError(null);

//     try {
//       // TODO: Replace with actual API call
//       await new Promise(resolve => setTimeout(resolve, 2000));

//       toast.success('Student created successfully');
//       router.push('/dashboard/students');
//     } catch (err) {
//       setError('Failed to create student');
//       toast.error('Failed to create student');
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const progress = (currentStep / steps.length) * 100;

//   return (
//     <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
//       {/* Header */}
//       <div className='flex items-center justify-between'>
//         <div className='flex items-center space-x-4'>
//           <Button variant='outline' size='sm' onClick={() => router.back()}>
//             <ArrowLeft className='w-4 h-4 mr-2' />
//             Back
//           </Button>
//           <div>
//             <h1 className='text-3xl font-bold text-gray-900'>Create New Student</h1>
//             <p className='text-gray-600'>Add a new student to the system</p>
//           </div>
//         </div>
//       </div>

//       {/* Progress */}
//       <Card>
//         <CardContent className='p-6'>
//           <div className='space-y-4'>
//             <div className='flex items-center justify-between'>
//               <span className='text-sm font-medium text-gray-600'>
//                 Step {currentStep} of {steps.length}
//               </span>
//               <span className='text-sm text-gray-600'>{Math.round(progress)}% Complete</span>
//             </div>
//             <Progress value={progress} className='h-2' />
//             <div className='flex justify-between text-xs text-gray-500'>
//               {steps.map(step => (
//                 <div
//                   key={step.id}
//                   className={`text-center ${currentStep >= step.id ? 'text-blue-600' : ''}`}
//                 >
//                   <div className='font-medium'>{step.title}</div>
//                   <div>{step.description}</div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       {/* Error Alert */}
//       {error && (
//         <Alert variant='destructive'>
//           <AlertCircle className='h-4 w-4' />
//           <AlertDescription>{error}</AlertDescription>
//         </Alert>
//       )}

//       {/* Form Steps */}
//       <div className='max-w-4xl mx-auto'>
//         {/* Step 1: Personal Information */}
//         {currentStep === 1 && (
//           <Card>
//             <CardHeader>
//               <CardTitle className='flex items-center gap-2'>
//                 <User className='w-5 h-5' />
//                 Personal Information
//               </CardTitle>
//               <CardDescription>Enter the student's basic personal details</CardDescription>
//             </CardHeader>
//             <CardContent className='space-y-6'>
//               {/* Profile Picture */}
//               <div className='flex items-center space-x-6'>
//                 <Avatar className='w-24 h-24'>
//                   <AvatarImage src={formData.profile_picture} />
//                   <AvatarFallback className='text-2xl bg-gradient-to-br from-blue-500 to-purple-600 text-white'>
//                     {formData.first_name[0] || 'S'}
//                     {formData.last_name[0] || 'T'}
//                   </AvatarFallback>
//                 </Avatar>
//                 <div>
//                   <Button variant='outline'>
//                     <Upload className='w-4 h-4 mr-2' />
//                     Upload Photo
//                   </Button>
//                   <p className='text-sm text-gray-600 mt-2'>
//                     Optional: Square image, at least 200x200px
//                   </p>
//                 </div>
//               </div>

//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='first_name'>First Name *</Label>
//                   <Input
//                     id='first_name'
//                     value={formData.first_name}
//                     onChange={e => handleInputChange('first_name', e.target.value)}
//                     placeholder='Enter first name'
//                   />
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='last_name'>Last Name *</Label>
//                   <Input
//                     id='last_name'
//                     value={formData.last_name}
//                     onChange={e => handleInputChange('last_name', e.target.value)}
//                     placeholder='Enter last name'
//                   />
//                 </div>
//               </div>

//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='email'>Email *</Label>
//                   <div className='relative'>
//                     <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                     <Input
//                       id='email'
//                       type='email'
//                       value={formData.email}
//                       onChange={e => handleInputChange('email', e.target.value)}
//                       placeholder='<EMAIL>'
//                       className='pl-10'
//                     />
//                   </div>
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='phone'>Phone Number *</Label>
//                   <div className='relative'>
//                     <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                     <Input
//                       id='phone'
//                       type='tel'
//                       value={formData.phone}
//                       onChange={e => handleInputChange('phone', e.target.value)}
//                       placeholder='+****************'
//                       className='pl-10'
//                     />
//                   </div>
//                 </div>
//               </div>

//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='date_of_birth'>Date of Birth *</Label>
//                   <div className='relative'>
//                     <Calendar className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                     <Input
//                       id='date_of_birth'
//                       type='date'
//                       value={formData.date_of_birth}
//                       onChange={e => handleInputChange('date_of_birth', e.target.value)}
//                       className='pl-10'
//                     />
//                   </div>
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='status'>Status</Label>
//                   <Select
//                     value={formData.status}
//                     onValueChange={value => handleInputChange('status', value)}
//                   >
//                     <SelectTrigger>
//                       <SelectValue />
//                     </SelectTrigger>
//                     <SelectContent>
//                       <SelectItem value='ACTIVE'>Active</SelectItem>
//                       <SelectItem value='INACTIVE'>Inactive</SelectItem>
//                     </SelectContent>
//                   </Select>
//                 </div>
//               </div>

//               <div className='space-y-2'>
//                 <Label htmlFor='address'>Address</Label>
//                 <div className='relative'>
//                   <MapPin className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                   <Textarea
//                     id='address'
//                     value={formData.address}
//                     onChange={e => handleInputChange('address', e.target.value)}
//                     placeholder='Enter full address'
//                     className='pl-10'
//                     rows={3}
//                   />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         )}

//         {/* Step 2: Academic Information */}
//         {currentStep === 2 && (
//           <Card>
//             <CardHeader>
//               <CardTitle className='flex items-center gap-2'>
//                 <GraduationCap className='w-5 h-5' />
//                 Academic Information
//               </CardTitle>
//               <CardDescription>Enter the student's academic details</CardDescription>
//             </CardHeader>
//             <CardContent className='space-y-6'>
//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='class_name'>Class *</Label>
//                   <Input
//                     id='class_name'
//                     value={formData.class_name}
//                     onChange={e => handleInputChange('class_name', e.target.value)}
//                     placeholder='e.g., Grade 10A'
//                   />
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='grade_level'>Grade Level *</Label>
//                   <Select
//                     value={formData.grade_level}
//                     onValueChange={value => handleInputChange('grade_level', value)}
//                   >
//                     <SelectTrigger>
//                       <SelectValue placeholder='Select grade level' />
//                     </SelectTrigger>
//                     <SelectContent>
//                       <SelectItem value='9th Grade'>9th Grade</SelectItem>
//                       <SelectItem value='10th Grade'>10th Grade</SelectItem>
//                       <SelectItem value='11th Grade'>11th Grade</SelectItem>
//                       <SelectItem value='12th Grade'>12th Grade</SelectItem>
//                     </SelectContent>
//                   </Select>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         )}

//         {/* Step 3: Contact Information */}
//         {currentStep === 3 && (
//           <Card>
//             <CardHeader>
//               <CardTitle className='flex items-center gap-2'>
//                 <Users className='w-5 h-5' />
//                 Contact Information
//               </CardTitle>
//               <CardDescription>Enter parent/guardian and emergency contact details</CardDescription>
//             </CardHeader>
//             <CardContent className='space-y-6'>
//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='parent_name'>Parent/Guardian Name *</Label>
//                   <Input
//                     id='parent_name'
//                     value={formData.parent_name}
//                     onChange={e => handleInputChange('parent_name', e.target.value)}
//                     placeholder='Enter parent/guardian name'
//                   />
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='parent_email'>Parent Email *</Label>
//                   <div className='relative'>
//                     <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                     <Input
//                       id='parent_email'
//                       type='email'
//                       value={formData.parent_email}
//                       onChange={e => handleInputChange('parent_email', e.target.value)}
//                       placeholder='<EMAIL>'
//                       className='pl-10'
//                     />
//                   </div>
//                 </div>
//               </div>

//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div className='space-y-2'>
//                   <Label htmlFor='parent_phone'>Parent Phone</Label>
//                   <div className='relative'>
//                     <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
//                     <Input
//                       id='parent_phone'
//                       type='tel'
//                       value={formData.parent_phone}
//                       onChange={e => handleInputChange('parent_phone', e.target.value)}
//                       placeholder='+****************'
//                       className='pl-10'
//                     />
//                   </div>
//                 </div>
//                 <div className='space-y-2'>
//                   <Label htmlFor='emergency_contact'>Emergency Contact</Label>
//                   <Input
//                     id='emergency_contact'
//                     value={formData.emergency_contact}
//                     onChange={e => handleInputChange('emergency_contact', e.target.value)}
//                     placeholder='Name - Phone Number'
//                   />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         )}

//         {/* Step 4: Review */}
//         {currentStep === 4 && (
//           <Card>
//             <CardHeader>
//               <CardTitle className='flex items-center gap-2'>
//                 <CheckCircle className='w-5 h-5' />
//                 Review Information
//               </CardTitle>
//               <CardDescription>Please review all information before submitting</CardDescription>
//             </CardHeader>
//             <CardContent className='space-y-6'>
//               <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
//                 <div>
//                   <h4 className='font-semibold mb-2'>Personal Information</h4>
//                   <div className='space-y-1 text-sm'>
//                     <p>
//                       <strong>Name:</strong> {formData.first_name} {formData.last_name}
//                     </p>
//                     <p>
//                       <strong>Email:</strong> {formData.email}
//                     </p>
//                     <p>
//                       <strong>Phone:</strong> {formData.phone}
//                     </p>
//                     <p>
//                       <strong>Date of Birth:</strong> {formData.date_of_birth}
//                     </p>
//                     <p>
//                       <strong>Status:</strong> {formData.status}
//                     </p>
//                     {formData.address && (
//                       <p>
//                         <strong>Address:</strong> {formData.address}
//                       </p>
//                     )}
//                   </div>
//                 </div>

//                 <div>
//                   <h4 className='font-semibold mb-2'>Academic Information</h4>
//                   <div className='space-y-1 text-sm'>
//                     <p>
//                       <strong>Class:</strong> {formData.class_name}
//                     </p>
//                     <p>
//                       <strong>Grade Level:</strong> {formData.grade_level}
//                     </p>
//                   </div>

//                   <h4 className='font-semibold mb-2 mt-4'>Contact Information</h4>
//                   <div className='space-y-1 text-sm'>
//                     <p>
//                       <strong>Parent:</strong> {formData.parent_name}
//                     </p>
//                     <p>
//                       <strong>Parent Email:</strong> {formData.parent_email}
//                     </p>
//                     {formData.parent_phone && (
//                       <p>
//                         <strong>Parent Phone:</strong> {formData.parent_phone}
//                       </p>
//                     )}
//                     {formData.emergency_contact && (
//                       <p>
//                         <strong>Emergency:</strong> {formData.emergency_contact}
//                       </p>
//                     )}
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         )}

//         {/* Navigation Buttons */}
//         <div className='flex items-center justify-between pt-6'>
//           <Button variant='outline' onClick={handlePrevious} disabled={currentStep === 1}>
//             <ArrowLeft className='w-4 h-4 mr-2' />
//             Previous
//           </Button>

//           {currentStep < steps.length ? (
//             <Button onClick={handleNext}>
//               Next
//               <ArrowRight className='w-4 h-4 ml-2' />
//             </Button>
//           ) : (
//             <Button onClick={handleSubmit} disabled={isSubmitting}>
//               {isSubmitting ? (
//                 <div className='flex items-center space-x-2'>
//                   <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
//                   <span>Creating...</span>
//                 </div>
//               ) : (
//                 <div className='flex items-center space-x-2'>
//                   <Save className='w-4 h-4' />
//                   <span>Create Student</span>
//                 </div>
//               )}
//             </Button>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }
