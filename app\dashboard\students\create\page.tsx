'use client';

/**
 * Student Create Page
 *
 * Features:
 * - Comprehensive student creation form
 * - Form validation with error handling
 * - Step-by-step wizard interface
 * - Image upload for profile picture
 * - CSV import functionality
 * - Responsive design
 * - Authentication handling
 * - Fixed 307 redirect issue by using StudentService
 */

import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Calendar,
  CheckCircle,
  GraduationCap,
  Mail,
  MapPin,
  Phone,
  Save,
  Upload,
  User,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { StudentService } from '@/api/services/studentService';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Import proper types and services
import { useAuthStore } from '@/lib/authStore';
import type { StudentCreate } from '@/types';

// Updated form data interface to match StudentCreate
interface StudentFormData {
  reg_no: string;
  first_name: string;
  last_name: string;
  gender: 'male' | 'female' | 'other';
  dob: string;
  class_id: string;
  section_id: string; // This maps to grade_id in the backend
  guardian_name: string;
  guardian_phone: string;
  address: string;
  email: string;
  password: string;
  parent_id: string;
  profile_picture?: string;
}

interface Parent {
  id: string;
  name: string;
}

interface Class {
  id: string;
  name: string;
}

interface Grade {
  id: string;
  name: string;
}

const initialFormData: StudentFormData = {
  reg_no: '',
  first_name: '',
  last_name: '',
  gender: 'other',
  dob: '',
  class_id: '',
  section_id: '',
  password: '',
  email: '',
  address: '',
  guardian_name: '',
  guardian_phone: '',
  parent_id: '',
};

const steps = [
  { id: 1, title: 'Personal Info', description: 'Basic student information' },
  { id: 2, title: 'Academic Info', description: 'Class and grade details' },
  { id: 3, title: 'Contact Info', description: 'Parent and emergency contacts' },
  { id: 4, title: 'Review', description: 'Review and submit' },
];

export default function StudentCreatePage() {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<StudentFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [parents, setParents] = useState<Parent[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [grades, setGrades] = useState<Grade[]>([]);

  // Check authentication
  useEffect(() => {
    if (!isAuthenticated) {
      setError('Please log in to create a student');
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  // Fetch parents, classes, and grades for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      if (!isAuthenticated) {
        return;
      }

      try {
        const [parentsRes, classesRes, gradesRes] = await Promise.all([
          fetch('/api/v1/parents'),
          fetch('/api/v1/classes'),
          fetch('/api/v1/grades'),
        ]);

        if (parentsRes.status === 401 || classesRes.status === 401 || gradesRes.status === 401) {
          setError('Authentication failed. Please log in again.');
          router.push('/login');
          return;
        }

        const parentsData = await parentsRes.json();
        const classesData = await classesRes.json();
        const gradesData = await gradesRes.json();

        setParents(parentsData.items || []);
        setClasses(classesData.items || []);
        setGrades(gradesData.items || []);
      } catch (err) {
        console.error('Failed to fetch form data:', err);
        setError('Failed to fetch data for form');
        toast.error('Failed to fetch data');
      }
    };

    fetchData();
  }, [isAuthenticated, router]);

  const handleInputChange = (field: keyof StudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) {
      setError(null);
    }
  };

  const validateStep = (step: number): string | null => {
    switch (step) {
      case 1:
        if (!formData.reg_no.trim()) {
          return 'Registration number is required';
        }
        if (!formData.first_name.trim()) {
          return 'First name is required';
        }
        if (!formData.last_name.trim()) {
          return 'Last name is required';
        }
        if (!formData.email.trim()) {
          return 'Email is required';
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          return 'Invalid email format';
        }
        if (!formData.dob) {
          return 'Date of birth is required';
        }
        if (!formData.password.trim()) {
          return 'Password is required';
        }
        if (formData.password.length < 8) {
          return 'Password must be at least 8 characters';
        }
        break;
      case 2:
        if (!formData.class_id) {
          return 'Class is required';
        }
        if (!formData.section_id) {
          return 'Grade level is required';
        }
        break;
      case 3:
        if (!formData.guardian_name.trim()) {
          return 'Guardian name is required';
        }
        if (!formData.guardian_phone.trim()) {
          return 'Guardian phone is required';
        }
        if (!formData.parent_id.trim()) {
          return 'Parent selection is required';
        }
        break;
    }
    return null;
  };

  const handleNext = () => {
    const validationError = validateStep(currentStep);
    if (validationError) {
      setError(validationError);
      return;
    }
    setError(null);
    setCurrentStep(prev => Math.min(prev + 1, steps.length));
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
    setError(null);
  };

  const handleSubmit = async () => {
    const validationError = validateStep(3);
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!isAuthenticated) {
      setError('Authentication required. Please log in.');
      router.push('/login');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Transform form data to StudentCreate format
      const studentData: StudentCreate = {
        reg_no: formData.reg_no,
        first_name: formData.first_name,
        last_name: formData.last_name,
        gender: formData.gender,
        dob: formData.dob,
        class_id: formData.class_id,
        section_id: formData.section_id,
        guardian_name: formData.guardian_name,
        guardian_phone: formData.guardian_phone,
        address: formData.address,
        email: formData.email,
        password: formData.password,
        parent_id: formData.parent_id,
      };

      console.log('Creating student with data:', { ...studentData, password: '[REDACTED]' });

      // Use StudentService instead of direct fetch - this fixes the 307 redirect issue
      const createdStudent = await StudentService.createStudent(studentData);

      console.log('Student created successfully:', createdStudent);

      // Show detailed success message
      toast.success(
        `Student ${createdStudent.first_name} ${createdStudent.last_name} created successfully!`,
        {
          description: `Registration: ${createdStudent.reg_no} | ID: ${createdStudent.id}`,
          duration: 5000,
        }
      );

      // Reset form and navigate after a brief delay to show success
      setTimeout(() => {
        setFormData(initialFormData);
        setCurrentStep(1);
        router.push('/dashboard/students');
      }, 1500);
    } catch (err: any) {
      console.error('Student creation failed:', err);

      // Enhanced error handling with specific messages
      let errorMessage = 'Failed to create student';

      if (err.message) {
        if (err.message.includes('307') || err.message.includes('redirect')) {
          errorMessage = 'API endpoint issue detected. Please contact support if this persists.';
        } else if (err.message.includes('registration number')) {
          errorMessage = 'A student with this registration number already exists';
        } else if (err.message.includes('email')) {
          errorMessage = 'A student with this email already exists';
        } else if (err.message.includes('username')) {
          errorMessage = 'A student with this username already exists';
        } else if (err.message.includes('validation')) {
          errorMessage = 'Please check all required fields and try again';
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCsvImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    if (!isAuthenticated) {
      setError('Authentication required. Please log in.');
      router.push('/login');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/students/import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to import students');
      }

      const result = await response.json();
      toast.success(`Imported ${result.created} students, updated ${result.updated}`);
      if (result.errors.length > 0) {
        toast.warning(`Encountered ${result.errors.length} errors during import`);
      }
      router.push('/dashboard/students');
    } catch (err: any) {
      console.error('CSV import failed:', err);
      const errorMessage = err.message || 'Failed to import students';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const progress = (currentStep / steps.length) * 100;

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button variant='outline' size='sm' onClick={() => router.back()}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Create New Student</h1>
            <p className='text-gray-600'>Add a new student to the system</p>
          </div>
        </div>
        <label>
          <Button variant='outline' type='button'>
            <Upload className='w-4 h-4 mr-2' />
            Import CSV
          </Button>
          <input
            type='file'
            accept='.csv'
            className='hidden'
            onChange={handleCsvImport}
            disabled={isSubmitting}
          />
        </label>
      </div>

      {/* Progress */}
      <Card>
        <CardContent className='p-6'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium text-gray-600'>
                Step {currentStep} of {steps.length}
              </span>
              <span className='text-sm text-gray-600'>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className='h-2' />
            <div className='flex justify-between text-xs text-gray-500'>
              {steps.map(step => (
                <div
                  key={step.id}
                  className={`text-center ${currentStep >= step.id ? 'text-blue-600' : ''}`}
                >
                  <div className='font-medium'>{step.title}</div>
                  <div>{step.description}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form Steps */}
      <div className='max-w-4xl mx-auto'>
        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='w-5 h-5' />
                Personal Information
              </CardTitle>
              <CardDescription>Enter the student's basic personal details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Profile Picture */}
              <div className='flex items-center space-x-6'>
                <Avatar className='w-24 h-24'>
                  <AvatarImage src={formData.profile_picture} />
                  <AvatarFallback className='text-2xl bg-gradient-to-br from-blue-500 to-purple-600 text-white'>
                    {formData.first_name[0] || 'S'}
                    {formData.last_name[0] || 'T'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <label>
                    <Button variant='outline' type='button'>
                      <Upload className='w-4 h-4 mr-2' />
                      Upload Photo
                    </Button>
                    <input
                      type='file'
                      accept='image/*'
                      className='hidden'
                      onChange={e => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const url = URL.createObjectURL(file);
                          setFormData(prev => ({ ...prev, profile_picture: url }));
                        }
                      }}
                    />
                  </label>
                  <p className='text-sm text-gray-600 mt-2'>
                    Optional: Square image, at least 200x200px
                  </p>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='reg_no'>Registration Number *</Label>
                  <Input
                    id='reg_no'
                    value={formData.reg_no}
                    onChange={e => handleInputChange('reg_no', e.target.value)}
                    placeholder='e.g., ADM2024001'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='parent_id'>Parent *</Label>
                  <Select
                    value={formData.parent_id}
                    onValueChange={value => handleInputChange('parent_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select parent/guardian' />
                    </SelectTrigger>
                    <SelectContent>
                      {parents.map(parent => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='first_name'>First Name *</Label>
                  <Input
                    id='first_name'
                    value={formData.first_name}
                    onChange={e => handleInputChange('first_name', e.target.value)}
                    placeholder='Enter first name'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='last_name'>Last Name *</Label>
                  <Input
                    id='last_name'
                    value={formData.last_name}
                    onChange={e => handleInputChange('last_name', e.target.value)}
                    placeholder='Enter last name'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='email'>Email *</Label>
                  <div className='relative'>
                    <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='email'
                      type='email'
                      value={formData.email}
                      onChange={e => handleInputChange('email', e.target.value)}
                      placeholder='<EMAIL>'
                      className='pl-10'
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='password'>Password *</Label>
                  <Input
                    id='password'
                    type='password'
                    value={formData.password}
                    onChange={e => handleInputChange('password', e.target.value)}
                    placeholder='Enter password (min 8 characters)'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='dob'>Date of Birth *</Label>
                  <div className='relative'>
                    <Calendar className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='dob'
                      type='date'
                      value={formData.dob}
                      onChange={e => handleInputChange('dob', e.target.value)}
                      className='pl-10'
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='gender'>Gender</Label>
                  <Select
                    value={formData.gender}
                    onValueChange={value =>
                      handleInputChange('gender', value as 'male' | 'female' | 'other')
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select gender' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='male'>Male</SelectItem>
                      <SelectItem value='female'>Female</SelectItem>
                      <SelectItem value='other'>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='address'>Address</Label>
                <div className='relative'>
                  <MapPin className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Textarea
                    id='address'
                    value={formData.address}
                    onChange={e => handleInputChange('address', e.target.value)}
                    placeholder='Enter full address'
                    className='pl-10'
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Academic Information */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <GraduationCap className='w-5 h-5' />
                Academic Information
              </CardTitle>
              <CardDescription>Enter the student's academic details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='class_id'>Class *</Label>
                  <Select
                    value={formData.class_id}
                    onValueChange={value => handleInputChange('class_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select class' />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='section_id'>Grade Level *</Label>
                  <Select
                    value={formData.section_id}
                    onValueChange={value => handleInputChange('section_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select grade level' />
                    </SelectTrigger>
                    <SelectContent>
                      {grades.map(grade => (
                        <SelectItem key={grade.id} value={grade.id}>
                          {grade.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Contact Information */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                Contact Information
              </CardTitle>
              <CardDescription>Enter parent/guardian and emergency contact details</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='parent_id'>Parent/Guardian</Label>
                  <Select
                    value={formData.parent_id}
                    onValueChange={value => handleInputChange('parent_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select parent/guardian' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>None</SelectItem>
                      {parents.map(parent => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='guardian_name'>Guardian Name *</Label>
                  <Input
                    id='guardian_name'
                    value={formData.guardian_name}
                    onChange={e => handleInputChange('guardian_name', e.target.value)}
                    placeholder='Enter guardian name'
                  />
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='guardian_phone'>Guardian Phone *</Label>
                  <div className='relative'>
                    <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                    <Input
                      id='guardian_phone'
                      type='tel'
                      value={formData.guardian_phone}
                      onChange={e => handleInputChange('guardian_phone', e.target.value)}
                      placeholder='+****************'
                      className='pl-10'
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Review */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <CheckCircle className='w-5 h-5' />
                Review Information
              </CardTitle>
              <CardDescription>Please review all information before submitting</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h4 className='font-semibold mb-2'>Personal Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Registration Number:</strong> {formData.reg_no}
                    </p>
                    <p>
                      <strong>Name:</strong> {formData.first_name} {formData.last_name}
                    </p>
                    <p>
                      <strong>Email:</strong> {formData.email}
                    </p>
                    <p>
                      <strong>Date of Birth:</strong> {formData.dob}
                    </p>
                    <p>
                      <strong>Gender:</strong> {formData.gender}
                    </p>
                    {formData.address && (
                      <p>
                        <strong>Address:</strong> {formData.address}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className='font-semibold mb-2'>Academic Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Class:</strong>{' '}
                      {classes.find(c => c.id === formData.class_id)?.name || 'N/A'}
                    </p>
                    <p>
                      <strong>Grade Level:</strong>{' '}
                      {grades.find(g => g.id === formData.section_id)?.name || 'N/A'}
                    </p>
                  </div>

                  <h4 className='font-semibold mb-2 mt-4'>Contact Information</h4>
                  <div className='space-y-1 text-sm'>
                    <p>
                      <strong>Parent:</strong>{' '}
                      {parents.find(p => p.id === formData.parent_id)?.name || 'None'}
                    </p>
                    <p>
                      <strong>Guardian Name:</strong> {formData.guardian_name}
                    </p>
                    <p>
                      <strong>Guardian Phone:</strong> {formData.guardian_phone}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className='flex items-center justify-between pt-6'>
          <Button variant='outline' onClick={handlePrevious} disabled={currentStep === 1}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Previous
          </Button>

          {currentStep < steps.length ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className='w-4 h-4 ml-2' />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isSubmitting || !isAuthenticated}>
              {isSubmitting ? (
                <div className='flex items-center space-x-2'>
                  <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
                  <span>Creating...</span>
                </div>
              ) : (
                <div className='flex items-center space-x-2'>
                  <Save className='w-4 h-4' />
                  <span>Create Student</span>
                </div>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
