/**
 * Teacher React Query Hooks
 *
 * Comprehensive hooks for teacher data management with:
 * - Query hooks for data fetching
 * - Mutation hooks for data modification
 * - Optimistic updates
 * - Cache management
 * - Error handling
 * - Loading states
 */

import { teacherService, type TeacherFilters } from '@/api/services/teacherService';
import { Teacher, TeacherCreate, TeacherUpdate } from '@/schemas/zodSchemas';
import { useQueryClient } from '@tanstack/react-query';
import { useMutationBase } from './useMutationBase';
import { useQueryBase } from './useQueryBase';

// Query Keys
export const teacherKeys = {
  all: ['teachers'] as const,
  lists: () => [...teacherKeys.all, 'list'] as const,
  list: (filters: TeacherFilters) => [...teacherKeys.lists(), filters] as const,
  details: () => [...teacherKeys.all, 'detail'] as const,
  detail: (id: string) => [...teacherKeys.details(), id] as const,
  stats: () => [...teacherKeys.all, 'stats'] as const,
  search: (query: string) => [...teacherKeys.all, 'search', query] as const,
  byDepartment: (department: string) => [...teacherKeys.all, 'department', department] as const,
};

// Query Hooks
export function useTeachers(filters: TeacherFilters = {}) {
  return useQueryBase(
    ['teachers', 'list', JSON.stringify(filters)],
    () => teacherService.getTeachers(filters),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

export function useTeacher(id: string, enabled = true) {
  return useQueryBase([...teacherKeys.detail(id)], () => teacherService.getTeacher(id), {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTeacherStats() {
  return useQueryBase([...teacherKeys.stats()], () => teacherService.getTeacherStats(), {
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useSearchTeachers(query: string, limit = 10) {
  return useQueryBase(
    [...teacherKeys.search(query)],
    () => teacherService.searchTeachers(query, limit),
    {
      enabled: query.length >= 2, // Only search with 2+ characters
      staleTime: 30 * 1000, // 30 seconds
    }
  );
}

export function useTeachersByDepartment(department: string) {
  return useQueryBase(
    [...teacherKeys.byDepartment(department)],
    () => teacherService.getTeachersByDepartment(department),
    {
      enabled: !!department,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Mutation Hooks
export function useCreateTeacher() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (teacherData: TeacherCreate) => teacherService.createTeacher(teacherData),
    {
      successMessage: 'Teacher created successfully!',
      errorMessage: 'Failed to create teacher',
      invalidateQueries: [[...teacherKeys.lists()], [...teacherKeys.stats()]],
      onSuccess: newTeacher => {
        // Add to cache optimistically
        queryClient.setQueryData(teacherKeys.detail(newTeacher.id), newTeacher);
      },
    }
  );
}

export function useUpdateTeacher() {
  const queryClient = useQueryClient();

  return useMutationBase(
    ({ id, data }: { id: string; data: TeacherUpdate }) => teacherService.updateTeacher(id, data),
    {
      successMessage: 'Teacher updated successfully!',
      errorMessage: 'Failed to update teacher',
      invalidateQueries: [[...teacherKeys.lists()], [...teacherKeys.stats()]],
      onSuccess: (updatedTeacher, { id }) => {
        // Update cache
        queryClient.setQueryData(teacherKeys.detail(id), updatedTeacher);
      },
    }
  );
}

export function useDeleteTeacher() {
  const queryClient = useQueryClient();

  return useMutationBase((id: string) => teacherService.deleteTeacher(id), {
    successMessage: 'Teacher deleted successfully!',
    errorMessage: 'Failed to delete teacher',
    invalidateQueries: [[...teacherKeys.lists()], [...teacherKeys.stats()]],
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: teacherKeys.detail(id) });
    },
  });
}

export function useBulkUpdateTeachers() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (updates: Array<{ id: string; data: TeacherUpdate }>) =>
      teacherService.bulkUpdateTeachers(updates),
    {
      successMessage: 'Teachers updated successfully!',
      errorMessage: 'Failed to update teachers',
      invalidateQueries: [[...teacherKeys.lists()], [...teacherKeys.stats()]],
      onSuccess: updatedTeachers => {
        // Update individual teacher caches
        updatedTeachers.forEach(teacher => {
          queryClient.setQueryData(teacherKeys.detail(teacher.id), teacher);
        });
      },
    }
  );
}

export function useBulkDeleteTeachers() {
  const queryClient = useQueryClient();

  return useMutationBase((ids: string[]) => teacherService.bulkDeleteTeachers(ids), {
    successMessage: 'Teachers deleted successfully!',
    errorMessage: 'Failed to delete teachers',
    invalidateQueries: [[...teacherKeys.lists()], [...teacherKeys.stats()]],
    onSuccess: (_, ids) => {
      // Remove from cache
      ids.forEach(id => {
        queryClient.removeQueries({ queryKey: teacherKeys.detail(id) });
      });
    },
  });
}

// Utility hooks
export function useTeacherCache() {
  const queryClient = useQueryClient();

  return {
    // Prefetch teacher data
    prefetchTeacher: (id: string) => {
      return queryClient.prefetchQuery({
        queryKey: teacherKeys.detail(id),
        queryFn: () => teacherService.getTeacher(id),
        staleTime: 5 * 60 * 1000,
      });
    },

    // Prefetch teachers list
    prefetchTeachers: (filters: TeacherFilters = {}) => {
      return queryClient.prefetchQuery({
        queryKey: teacherKeys.list(filters),
        queryFn: () => teacherService.getTeachers(filters),
        staleTime: 2 * 60 * 1000,
      });
    },

    // Invalidate all teacher queries
    invalidateTeachers: () => {
      return queryClient.invalidateQueries({ queryKey: teacherKeys.all });
    },

    // Clear teacher cache
    clearTeacherCache: () => {
      queryClient.removeQueries({ queryKey: teacherKeys.all });
    },

    // Get cached teacher data
    getCachedTeacher: (id: string): Teacher | undefined => {
      return queryClient.getQueryData(teacherKeys.detail(id));
    },

    // Set teacher data in cache
    setCachedTeacher: (id: string, teacher: Teacher) => {
      queryClient.setQueryData(teacherKeys.detail(id), teacher);
    },

    // Optimistically update teacher
    optimisticUpdateTeacher: (id: string, updates: Partial<Teacher>) => {
      queryClient.setQueryData(teacherKeys.detail(id), (old: Teacher | undefined) => {
        if (!old) return old;
        return { ...old, ...updates };
      });
    },
  };
}

// Export all hooks for convenience
export const teacherHooks = {
  // Queries
  useTeachers,
  useTeacher,
  useTeacherStats,
  useSearchTeachers,
  useTeachersByDepartment,

  // Mutations
  useCreateTeacher,
  useUpdateTeacher,
  useDeleteTeacher,
  useBulkUpdateTeachers,
  useBulkDeleteTeachers,

  // Utilities
  useTeacherCache,

  // Keys
  teacherKeys,
};
