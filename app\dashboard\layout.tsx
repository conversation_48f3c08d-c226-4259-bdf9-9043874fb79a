/**
 * Dashboard Layout — Premium, Accessible, Responsive
 * - Gradient brand header (sky→violet)
 * - Desktop sidebar always visible; mobile collapsible
 * - Active route highlight (supports nested routes)
 * - Keyboard & screen-reader friendly
 * - Subtle transitions, soft shadows
 * - Keeps your existing routes and components intact
 */

'use client';

import {
  BarChart3,
  Bell,
  BookOpen,
  Calendar,
  CalendarDays,
  CheckCircle,
  DollarSign,
  FolderOpen,
  GraduationCap,
  Home,
  LogOut,
  Megaphone,
  Menu,
  Settings,
  Users,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMemo, useState } from 'react';

import { PerformanceMonitor } from '@/components/dev/PerformanceMonitor';
// import { AuthInitializer } from '@/components/auth/AuthInitializer';
// import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';

// Flat menu stays as-is to avoid breaking routes; active state handles nested paths
const menuItems = [
  { name: 'Dashboard', href: '/dashboard', icon: Home, color: 'text-blue-600' },
  { name: 'Students', href: '/dashboard/students', icon: GraduationCap, color: 'text-green-600' },
  { name: 'Teachers', href: '/dashboard/teachers', icon: Users, color: 'text-purple-600' },
  { name: 'Classes', href: '/dashboard/classes', icon: BookOpen, color: 'text-orange-600' },
  {
    name: 'Attendance',
    href: '/dashboard/attendance',
    icon: CheckCircle,
    color: 'text-emerald-600',
  },
  { name: 'Results', href: '/dashboard/results', icon: BarChart3, color: 'text-blue-500' },
  { name: 'Media', href: '/dashboard/media', icon: FolderOpen, color: 'text-pink-600' },
  {
    name: 'Notifications',
    href: '/dashboard/notifications',
    icon: Megaphone,
    color: 'text-yellow-600',
  },
  { name: 'Exams', href: '/dashboard/exams', icon: Calendar, color: 'text-red-600' },
  {
    name: 'Student Fee',
    href: '/dashboard/student-fee',
    icon: DollarSign,
    color: 'text-green-500',
  },
  { name: 'Grade', href: '/dashboard/grade', icon: BarChart3, color: 'text-indigo-600' },
  { name: 'Events', href: '/dashboard/events', icon: CalendarDays, color: 'text-violet-600' },
  { name: 'Reports', href: '/dashboard/reports', icon: BarChart3, color: 'text-teal-600' },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings, color: 'text-gray-600' },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return <DashboardContent>{children}</DashboardContent>;
}

function DashboardContent({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [q, setQ] = useState('');
  const pathname = usePathname() || '/dashboard';
  // const { user, logout } = useAuthStore();

  const handleLogout = async () => {
    try {
      // await logout();
      console.log('Logout clicked');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // search filter (non-destructive)
  const filtered = useMemo(() => {
    const s = q.trim().toLowerCase();
    if (!s) {
      return menuItems;
    }
    return menuItems.filter(m => m.name.toLowerCase().includes(s));
  }, [q]);

  const isActive = (href: string) => pathname === href || pathname.startsWith(`${href}/`);

  return (
    <div className='flex h-screen bg-gradient-to-b from-gray-50 to-white'>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 z-40 bg-black/40 backdrop-blur-[1px] lg:hidden'
          onClick={() => setSidebarOpen(false)}
          aria-hidden
        />
      )}

      {/* Sidebar */}
      <aside
        aria-label='Primary'
        className={[
          // width & base
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-2xl border-r border-slate-100',
          // slide-in/out only on mobile
          'transform transition-transform duration-300 ease-in-out',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full',
          // desktop default visible
          'lg:translate-x-0 lg:static',
          // layout
          'flex flex-col',
        ].join(' ')}
      >
        {/* Brand header — required gradient */}
        <div className='h-16 px-4 flex items-center justify-between bg-gradient-to-r from-sky-600 to-violet-600 text-white rounded-tr-lg rounded-br-lg shadow-sm'>
          <div className='flex items-center gap-3'>
            <div className='p-2 bg-white/10 rounded-md'>
              <GraduationCap className='h-6 w-6' aria-hidden />
            </div>
            <div className='leading-tight'>
              <p className='text-sm font-semibold'>SchoolPro</p>
              <p className='text-xs/4 opacity-85'>Admin</p>
            </div>
          </div>
          <Button
            variant='ghost'
            size='icon'
            className='lg:hidden text-white hover:bg-white/10 focus-visible:ring-white'
            onClick={() => setSidebarOpen(false)}
            aria-label='Close sidebar'
          >
            <X className='h-5 w-5' />
          </Button>
        </div>

        {/* Search */}
        <div className='px-3 py-3 border-b border-slate-100'>
          <label htmlFor='sidebar-search' className='sr-only'>
            Search menu
          </label>
          <Input
            id='sidebar-search'
            placeholder='Search menu…'
            value={q}
            onChange={e => setQ(e.target.value)}
          />
        </div>

        {/* Nav */}
        <nav role='navigation' className='flex-1 overflow-y-auto px-2 py-4 space-y-1 pb-28'>
          {filtered.map(item => {
            const active = isActive(item.href);
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setSidebarOpen(false)}
                aria-current={active ? 'page' : undefined}
                className={[
                  'group flex items-center gap-3 px-3 py-2 rounded-md text-sm outline-none',
                  'transition-all duration-200 focus-visible:ring-2 focus-visible:ring-sky-400',
                  active
                    ? 'bg-gradient-to-r from-sky-50 to-violet-50 text-sky-800 ring-1 ring-sky-100 shadow'
                    : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900',
                ].join(' ')}
              >
                <item.icon
                  className={['h-4 w-4', active ? 'text-sky-600' : item.color].join(' ')}
                  aria-hidden
                />
                <span className='truncate'>{item.name}</span>
              </Link>
            );
          })}
        </nav>

        {/* Profile */}
        <div className='absolute bottom-0 left-0 right-0 p-4 border-t border-slate-100 bg-white'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='ghost'
                className='w-full justify-start p-2 focus-visible:ring-sky-400'
              >
                <Avatar className='h-8 w-8 mr-3'>
                  <AvatarImage src='' alt='User avatar' />
                  <AvatarFallback>DU</AvatarFallback>
                </Avatar>
                <div className='flex-1 text-left'>
                  <p className='text-sm font-medium text-slate-900'>Demo User</p>
                  <p className='text-xs text-slate-500'>Administrator</p>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-56'>
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href='/dashboard/settings' className='w-full'>
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className='mr-2 h-4 w-4' />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </aside>

      {/* Main area */}
      <div className='flex-1 flex flex-col min-w-0'>
        <header className='bg-white border-b border-slate-200 shadow-sm'>
          <div className='flex items-center justify-between h-16 px-4'>
            <Button
              variant='ghost'
              size='icon'
              className='lg:hidden'
              onClick={() => setSidebarOpen(true)}
              aria-label='Open sidebar'
            >
              <Menu className='h-6 w-6' />
            </Button>

            <div className='ml-auto flex items-center gap-1'>
              <Button
                variant='ghost'
                size='icon'
                aria-label='Notifications'
                className='hover:bg-slate-50'
              >
                <Bell className='h-5 w-5' />
              </Button>
            </div>
          </div>
        </header>

        <main className='flex-1 overflow-y-auto p-6'>{children}</main>
      </div>

      {/* Dev-only Performance Monitor */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </div>
  );
}
