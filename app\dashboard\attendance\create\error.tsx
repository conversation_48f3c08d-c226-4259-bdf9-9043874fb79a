'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateAttendanceErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateAttendanceError({ error, reset }: CreateAttendanceErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Take Attendance"
      moduleIcon="📋"
      backHref="/dashboard/attendance"
    />
  );
}
