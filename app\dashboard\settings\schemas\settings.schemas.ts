/**
 * Settings Validation Schemas
 * 
 * Zod schemas for all 8 settings tabs:
 * - School Profile
 * - Users & Roles  
 * - Fees & Billing
 * - Academic Structure
 * - Exam <PERSON>rms
 * - Notifications
 * - ID Card Note
 * - Sessions
 */

import { z } from 'zod';

// Common validation patterns
const emailSchema = z.string().email('Please enter a valid email address');
const phoneSchema = z.string().min(10, 'Please enter a valid phone number');
const urlSchema = z.string().url('Please enter a valid URL').optional().or(z.literal(''));

// 1. School Profile Schema
export const SchoolProfileSchema = z.object({
  name: z.string().min(1, 'School name is required').max(100, 'School name must be less than 100 characters'),
  address: z.string().min(1, 'Address is required').max(200, 'Address must be less than 200 characters'),
  city: z.string().min(1, 'City is required').max(50, 'City must be less than 50 characters'),
  state: z.string().min(1, 'State is required').max(50, 'State must be less than 50 characters'),
  postal_code: z.string().min(1, 'Postal code is required').max(10, 'Postal code must be less than 10 characters'),
  country: z.string().min(1, 'Country is required').max(50, 'Country must be less than 50 characters'),
  phone: phoneSchema,
  email: emailSchema,
  website: urlSchema,
  logo: z.string().optional(),
  established_year: z.number().min(1800, 'Year must be after 1800').max(new Date().getFullYear(), 'Year cannot be in the future'),
  principal_name: z.string().min(1, 'Principal name is required').max(100, 'Principal name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  motto: z.string().max(200, 'Motto must be less than 200 characters').optional(),
});

// 2. Users & Roles Schema
export const UserRolePermissionSchema = z.object({
  role_name: z.string().min(1, 'Role name is required'),
  permissions: z.array(z.string()),
  description: z.string().optional(),
});

export const UsersRolesSchema = z.object({
  default_student_role: z.string().min(1, 'Default student role is required'),
  default_teacher_role: z.string().min(1, 'Default teacher role is required'),
  default_parent_role: z.string().min(1, 'Default parent role is required'),
  custom_roles: z.array(UserRolePermissionSchema),
  max_login_attempts: z.number().min(1, 'Must be at least 1').max(10, 'Must be at most 10'),
  session_timeout: z.number().min(15, 'Must be at least 15 minutes').max(480, 'Must be at most 8 hours'),
  password_policy: z.object({
    min_length: z.number().min(6, 'Must be at least 6 characters').max(20, 'Must be at most 20 characters'),
    require_uppercase: z.boolean(),
    require_lowercase: z.boolean(),
    require_numbers: z.boolean(),
    require_symbols: z.boolean(),
    password_expiry_days: z.number().min(0, 'Must be 0 or more').max(365, 'Must be at most 365 days'),
  }),
});

// 3. Fees & Billing Schema
export const FeeStructureSchema = z.object({
  name: z.string().min(1, 'Fee name is required'),
  amount: z.number().min(0, 'Amount must be positive'),
  frequency: z.enum(['MONTHLY', 'QUARTERLY', 'ANNUALLY', 'ONE_TIME']),
  due_date: z.string().min(1, 'Due date is required'),
  late_fee: z.number().min(0, 'Late fee must be positive').optional(),
  grace_period_days: z.number().min(0, 'Grace period must be positive').optional(),
});

export const FeesSchema = z.object({
  currency: z.string().min(1, 'Currency is required'),
  fee_structures: z.array(FeeStructureSchema),
  payment_methods: z.array(z.enum(['CASH', 'BANK_TRANSFER', 'ONLINE', 'CHEQUE', 'CARD'])),
  late_fee_policy: z.object({
    enabled: z.boolean(),
    percentage: z.number().min(0, 'Percentage must be positive').max(100, 'Percentage must be at most 100').optional(),
    fixed_amount: z.number().min(0, 'Fixed amount must be positive').optional(),
  }),
  discount_policy: z.object({
    sibling_discount: z.number().min(0, 'Discount must be positive').max(100, 'Discount must be at most 100').optional(),
    early_payment_discount: z.number().min(0, 'Discount must be positive').max(100, 'Discount must be at most 100').optional(),
    scholarship_enabled: z.boolean(),
  }),
});

// 4. Academic Structure Schema
export const GradeLevelSchema = z.object({
  name: z.string().min(1, 'Grade name is required'),
  level: z.number().min(1, 'Level must be positive'),
  age_range: z.object({
    min: z.number().min(3, 'Minimum age must be at least 3'),
    max: z.number().max(25, 'Maximum age must be at most 25'),
  }),
  subjects: z.array(z.string()),
});

export const AcademicStructureSchema = z.object({
  academic_year_start: z.string().min(1, 'Academic year start is required'),
  academic_year_end: z.string().min(1, 'Academic year end is required'),
  grade_levels: z.array(GradeLevelSchema),
  class_duration_minutes: z.number().min(30, 'Class duration must be at least 30 minutes').max(180, 'Class duration must be at most 180 minutes'),
  break_duration_minutes: z.number().min(5, 'Break duration must be at least 5 minutes').max(60, 'Break duration must be at most 60 minutes'),
  working_days: z.array(z.enum(['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'])),
  school_start_time: z.string().min(1, 'School start time is required'),
  school_end_time: z.string().min(1, 'School end time is required'),
  terms_per_year: z.number().min(1, 'Must have at least 1 term').max(4, 'Must have at most 4 terms'),
});

// 5. Exam Terms Schema
export const ExamTermSchema = z.object({
  name: z.string().min(1, 'Term name is required'),
  start_date: z.string().min(1, 'Start date is required'),
  end_date: z.string().min(1, 'End date is required'),
  exam_start_date: z.string().min(1, 'Exam start date is required'),
  exam_end_date: z.string().min(1, 'Exam end date is required'),
  result_publish_date: z.string().min(1, 'Result publish date is required'),
  weightage: z.number().min(0, 'Weightage must be positive').max(100, 'Weightage must be at most 100'),
});

export const ExamTermsSchema = z.object({
  terms: z.array(ExamTermSchema),
  grading_system: z.object({
    type: z.enum(['PERCENTAGE', 'GPA', 'LETTER_GRADE']),
    passing_marks: z.number().min(0, 'Passing marks must be positive').max(100, 'Passing marks must be at most 100'),
    grade_ranges: z.array(z.object({
      grade: z.string().min(1, 'Grade is required'),
      min_marks: z.number().min(0, 'Minimum marks must be positive'),
      max_marks: z.number().max(100, 'Maximum marks must be at most 100'),
    })),
  }),
  exam_rules: z.object({
    allow_reexam: z.boolean(),
    max_reexam_attempts: z.number().min(1, 'Must allow at least 1 reexam').max(5, 'Must allow at most 5 reexams').optional(),
    attendance_requirement: z.number().min(0, 'Attendance requirement must be positive').max(100, 'Attendance requirement must be at most 100'),
  }),
});

// 6. Notifications Schema
export const NotificationChannelSchema = z.object({
  type: z.enum(['EMAIL', 'SMS', 'PUSH', 'IN_APP']),
  enabled: z.boolean(),
  settings: z.record(z.any()).optional(),
});

export const NotificationsSchema = z.object({
  channels: z.array(NotificationChannelSchema),
  email_settings: z.object({
    smtp_host: z.string().min(1, 'SMTP host is required'),
    smtp_port: z.number().min(1, 'SMTP port is required').max(65535, 'Invalid port number'),
    smtp_username: z.string().min(1, 'SMTP username is required'),
    smtp_password: z.string().min(1, 'SMTP password is required'),
    from_email: emailSchema,
    from_name: z.string().min(1, 'From name is required'),
  }),
  sms_settings: z.object({
    provider: z.enum(['TWILIO', 'AWS_SNS', 'CUSTOM']),
    api_key: z.string().min(1, 'API key is required'),
    sender_id: z.string().min(1, 'Sender ID is required'),
  }),
  notification_types: z.array(z.object({
    type: z.string().min(1, 'Notification type is required'),
    enabled: z.boolean(),
    channels: z.array(z.enum(['EMAIL', 'SMS', 'PUSH', 'IN_APP'])),
    template: z.string().optional(),
  })),
});

// 7. ID Card Note Schema
export const IDCardNoteSchema = z.object({
  note_text: z.string().max(500, 'Note must be less than 500 characters').optional(),
  show_on_student_card: z.boolean(),
  show_on_teacher_card: z.boolean(),
  show_on_staff_card: z.boolean(),
  font_size: z.enum(['SMALL', 'MEDIUM', 'LARGE']),
  position: z.enum(['TOP', 'BOTTOM', 'BACK']),
  required_fields: z.array(z.enum(['PHOTO', 'NAME', 'ID', 'CLASS', 'CONTACT', 'ADDRESS', 'EMERGENCY_CONTACT'])),
  card_template: z.enum(['TEMPLATE_1', 'TEMPLATE_2', 'TEMPLATE_3', 'CUSTOM']),
  background_color: z.string().optional(),
  text_color: z.string().optional(),
});

// 8. Sessions Schema
export const SessionSchema = z.object({
  name: z.string().min(1, 'Session name is required'),
  start_date: z.string().min(1, 'Start date is required'),
  end_date: z.string().min(1, 'End date is required'),
  is_current: z.boolean(),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
});

export const SessionsSchema = z.object({
  sessions: z.array(SessionSchema),
  auto_promote_students: z.boolean(),
  session_transition_date: z.string().min(1, 'Session transition date is required'),
  archive_old_sessions: z.boolean(),
  archive_after_years: z.number().min(1, 'Must archive after at least 1 year').max(10, 'Must archive within 10 years').optional(),
});

// Export types
export type SchoolProfile = z.infer<typeof SchoolProfileSchema>;
export type UsersRoles = z.infer<typeof UsersRolesSchema>;
export type Fees = z.infer<typeof FeesSchema>;
export type AcademicStructure = z.infer<typeof AcademicStructureSchema>;
export type ExamTerms = z.infer<typeof ExamTermsSchema>;
export type Notifications = z.infer<typeof NotificationsSchema>;
export type IDCardNote = z.infer<typeof IDCardNoteSchema>;
export type Sessions = z.infer<typeof SessionsSchema>;

// Combined settings type
export type SettingsData = {
  schoolProfile: SchoolProfile;
  usersRoles: UsersRoles;
  fees: Fees;
  academicStructure: AcademicStructure;
  examTerms: ExamTerms;
  notifications: Notifications;
  idCardNote: IDCardNote;
  sessions: Sessions;
};
