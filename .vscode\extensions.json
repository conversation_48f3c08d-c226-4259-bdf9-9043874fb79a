{
  "recommendations": [
    // ===== ESSENTIAL EXTENSIONS =====
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",

    // ===== REACT & NEXT.JS EXTENSIONS =====
    "dsznajder.es7-react-js-snippets",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "zignd.html-css-class-completion",

    // ===== TYPESCRIPT EXTENSIONS =====
    "ms-vscode.typescript-hero",
    "usernamehw.errorlens",
    "yoavbls.pretty-ts-errors",

    // ===== CODE QUALITY EXTENSIONS =====
    "streetsidesoftware.code-spell-checker",
    "wix.vscode-import-cost",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-npm-script",

    // ===== GIT EXTENSIONS =====
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",

    // ===== PRODUCTIVITY EXTENSIONS =====
    "ms-vscode.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "alefragnani.bookmarks",
    "ms-vscode.hexeditor",

    // ===== MARKDOWN EXTENSIONS =====
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid",

    // ===== THEME & UI EXTENSIONS =====
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "ms-vscode.theme-tomorrowkit",

    // ===== UTILITY EXTENSIONS =====
    "ms-vscode.live-server",
    "ritwickdey.liveserver",
    "humao.rest-client",
    "rangav.vscode-thunder-client",

    // ===== DOCKER & DEPLOYMENT =====
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",

    // ===== DATABASE EXTENSIONS =====
    "ms-mssql.mssql",
    "cweijan.vscode-postgresql-client2",

    // ===== TESTING EXTENSIONS =====
    "orta.vscode-jest",
    "ms-playwright.playwright",

    // ===== ACCESSIBILITY EXTENSIONS =====
    "deque-systems.vscode-axe-linter",

    // ===== PERFORMANCE EXTENSIONS =====
    "ms-vscode.vscode-js-profile-flame",
    "wallabyjs.console-ninja"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css-peek",
    "formulahendry.code-runner"
  ]
}
