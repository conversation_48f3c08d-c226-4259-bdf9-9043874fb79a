/**
 * Academic Structure Validation Schemas
 * 
 * Zod schemas for:
 * - Sessions (one active)
 * - Sections 
 * - Classes
 * - Subjects
 * - Designations
 */

import { z } from 'zod';

// Session Schema
export const SessionSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Session name is required').max(50, 'Name must be less than 50 characters'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  isActive: z.boolean().default(false),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
  sortOrder: z.number().int().default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Section Schema
export const SectionSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Section name is required').max(20, 'Name must be less than 20 characters'),
  code: z.string().min(1, 'Section code is required').max(5, 'Code must be less than 5 characters'),
  capacity: z.number().int().min(1, 'Capacity must be at least 1').max(100, 'Capacity cannot exceed 100'),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Class Schema
export const ClassSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Class name is required').max(30, 'Name must be less than 30 characters'),
  level: z.number().int().min(1, 'Level must be at least 1').max(20, 'Level cannot exceed 20'),
  ageRange: z.object({
    min: z.number().int().min(3, 'Minimum age must be at least 3'),
    max: z.number().int().max(25, 'Maximum age cannot exceed 25'),
  }),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Subject Schema
export const SubjectSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Subject name is required').max(50, 'Name must be less than 50 characters'),
  code: z.string().min(1, 'Subject code is required').max(10, 'Code must be less than 10 characters'),
  category: z.enum(['CORE', 'ELECTIVE', 'EXTRA_CURRICULAR', 'LANGUAGE']),
  credits: z.number().int().min(1, 'Credits must be at least 1').max(10, 'Credits cannot exceed 10').default(1),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Designation Schema
export const DesignationSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Designation title is required').max(50, 'Title must be less than 50 characters'),
  department: z.string().min(1, 'Department is required').max(30, 'Department must be less than 30 characters'),
  level: z.enum(['JUNIOR', 'SENIOR', 'HEAD', 'PRINCIPAL']),
  permissions: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Combined Academic Structure Schema
export const AcademicStructureSchema = z.object({
  sessions: z.array(SessionSchema),
  sections: z.array(SectionSchema),
  classes: z.array(ClassSchema),
  subjects: z.array(SubjectSchema),
  designations: z.array(DesignationSchema),
});

// Export types
export type Session = z.infer<typeof SessionSchema>;
export type Section = z.infer<typeof SectionSchema>;
export type Class = z.infer<typeof ClassSchema>;
export type Subject = z.infer<typeof SubjectSchema>;
export type Designation = z.infer<typeof DesignationSchema>;
export type AcademicStructure = z.infer<typeof AcademicStructureSchema>;

// Form schemas
export const CreateSessionSchema = SessionSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const CreateSectionSchema = SectionSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const CreateClassSchema = ClassSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const CreateSubjectSchema = SubjectSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const CreateDesignationSchema = DesignationSchema.omit({ id: true, createdAt: true, updatedAt: true });

export type CreateSession = z.infer<typeof CreateSessionSchema>;
export type CreateSection = z.infer<typeof CreateSectionSchema>;
export type CreateClass = z.infer<typeof CreateClassSchema>;
export type CreateSubject = z.infer<typeof CreateSubjectSchema>;
export type CreateDesignation = z.infer<typeof CreateDesignationSchema>;
