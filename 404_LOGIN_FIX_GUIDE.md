# 🚨 404 Login Error - Quick Fix Guide

## The Problem
Getting **404 on login** means the **endpoint path is wrong**, not a database issue. A DB problem would show **500** with SQL errors.

## Quick Diagnosis (Use `/test-auth` page)

### Step 1: Test Backend Connectivity
Click **"Test Backend Direct"** to verify:
- ✅ Backend is running on `http://localhost:8000`
- ✅ Basic endpoints respond (health, docs, root)
- ❌ If all fail → Backend not running or wrong port

### Step 2: Detect Correct Namespace
Click **"Detect Namespace"** to find the right API structure:
- ✅ **401 response** = Good endpoint, needs authentication
- ❌ **404 response** = Wrong endpoint path
- 🔍 Tests both: `/api/v1/auth/me` and `/api/v1/users/auth/me`

### Step 3: Test Login Endpoints
Click **"Test Login Endpoint"** to test actual login:
- Tests multiple credential formats: `{username}`, `{email}`, etc.
- Tests both namespaces: `auth` and `users`
- ✅ **200** = Working login found
- ✅ **401** = Good endpoint, wrong credentials
- ❌ **404** = Wrong endpoint path

## Common Issues & Fixes

### Issue 1: Wrong Namespace
**Symptom:** All auth endpoints return 404
**Fix:** Update `.env.local`:
```bash
# If backend uses /api/v1/auth/*
NEXT_PUBLIC_AUTH_NAMESPACE=auth

# If backend uses /api/v1/users/auth/*
NEXT_PUBLIC_AUTH_NAMESPACE=users
```

### Issue 2: Missing `/api/v1` Prefix
**Symptom:** 404 on all endpoints
**Fix:** Check your backend actually serves on `/api/v1/...`
- Visit: `http://localhost:8000/docs` (FastAPI docs)
- Verify endpoints start with `/api/v1/`

### Issue 3: Wrong Credentials Format
**Symptom:** 422 or 401 errors
**Fix:** Check what your backend expects:
```python
# Backend expects username
class LoginRequest(BaseModel):
    username: str
    password: str

# Send: {"username": "admin", "password": "admin123"}
```

### Issue 4: Backend Not Running
**Symptom:** Network errors, connection refused
**Fix:** Start your FastAPI backend:
```bash
cd your-backend-directory
uvicorn main:app --reload --port 8000
```

### Issue 5: CORS Issues
**Symptom:** CORS errors in browser console
**Fix:** Add CORS middleware to FastAPI:
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## Database Issues (If Login Works But User Not Found)

Only worry about DB after you get **401** (not 404) responses:

1. **Check Database Connection:**
   ```bash
   # Test DB connection
   psql ********************************/dbname
   ```

2. **Run Migrations:**
   ```bash
   # Alembic migrations
   alembic upgrade head
   ```

3. **Seed Admin User:**
   ```python
   # Create admin user script
   from your_app.models import User
   from your_app.auth import hash_password
   
   admin = User(
       username="admin",
       email="<EMAIL>", 
       password_hash=hash_password("admin123"),
       is_active=True
   )
   db.add(admin)
   db.commit()
   ```

## Test Results Interpretation

| Status | Meaning | Action |
|--------|---------|--------|
| **200** | ✅ Success | Login works! |
| **401** | ✅ Good endpoint, bad credentials | Check username/password |
| **404** | ❌ Wrong endpoint path | Fix namespace/URL |
| **422** | ❌ Wrong request format | Check credential fields |
| **500** | ❌ Server error | Check backend logs |
| **CORS** | ❌ Browser blocked | Add CORS middleware |

## Quick cURL Test (Bypass Frontend)

Test your backend directly:
```bash
# Test AUTH namespace
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Test USERS namespace  
curl -X POST http://localhost:8000/api/v1/users/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

**Expected Results:**
- **200 + token** → Backend works, DB works, credentials good
- **401** → Backend works, credentials wrong
- **404** → Wrong endpoint path
- **Connection refused** → Backend not running

## Final Checklist

- [ ] Backend running on port 8000
- [ ] FastAPI docs accessible at `http://localhost:8000/docs`
- [ ] Correct namespace detected via test page
- [ ] `.env.local` updated with correct namespace
- [ ] Login endpoint returns 401 (not 404) with test credentials
- [ ] Admin user exists in database
- [ ] CORS configured if needed

**Remember:** 404 = Path problem, 401 = Auth problem, 500 = Server/DB problem
