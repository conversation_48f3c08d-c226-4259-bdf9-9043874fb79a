# 🔧 Environment Configuration Examples

Based on your backend structure, use one of these configurations in your `.env.local`:

## Option 1: No API Prefix (Simple Backend)

If your FastAPI serves routes like:
- `POST /login`
- `GET /me`
- `POST /logout`

```bash
# .env.local
NEXT_PUBLIC_API_BASE=http://localhost:8000
NEXT_PUBLIC_AUTH_NAMESPACE=none
NEXT_PUBLIC_API_VERSION=v1
```

## Option 2: Auth Namespace

If your FastAPI serves routes like:
- `POST /api/v1/auth/login`
- `GET /api/v1/auth/me`
- `POST /api/v1/auth/logout`

```bash
# .env.local
NEXT_PUBLIC_API_BASE=http://localhost:8000
NEXT_PUBLIC_AUTH_NAMESPACE=auth
NEXT_PUBLIC_API_VERSION=v1
```

## Option 3: Users Namespace (Current Default)

If your FastAPI serves routes like:
- `POST /api/v1/users/auth/login`
- `GET /api/v1/users/auth/me`
- `POST /api/v1/users/auth/logout`

```bash
# .env.local
NEXT_PUBLIC_API_BASE=http://localhost:8000
NEXT_PUBLIC_AUTH_NAMESPACE=users
NEXT_PUBLIC_API_VERSION=v1
```

## Option 4: No Version Prefix

If your FastAPI serves routes like:
- `POST /api/auth/login` (no v1)
- `GET /api/auth/me`

```bash
# .env.local
NEXT_PUBLIC_API_BASE=http://localhost:8000
NEXT_PUBLIC_AUTH_NAMESPACE=auth
NEXT_PUBLIC_API_VERSION=""
```

## Option 5: Custom Port

If your backend runs on a different port:

```bash
# .env.local
NEXT_PUBLIC_API_BASE=http://localhost:8080
NEXT_PUBLIC_AUTH_NAMESPACE=users
NEXT_PUBLIC_API_VERSION=v1
```

## How to Find Your Configuration

1. **Check FastAPI docs**: http://localhost:8000/docs
2. **Look for login endpoint path**
3. **Use the test page**: `/test-auth` → "Detect Namespace"
4. **Update `.env.local` accordingly**
5. **Restart Next.js dev server**

## Quick Test Commands

Test your backend directly:

```bash
# Test Option 1 (no prefix)
curl -X POST http://localhost:8000/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Test Option 2 (auth namespace)
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Test Option 3 (users namespace)
curl -X POST http://localhost:8000/api/v1/users/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## After Updating Configuration

1. **Restart Next.js**: `npm run dev` or `yarn dev`
2. **Clear browser cache**: Hard refresh (Ctrl+Shift+R)
3. **Test login**: Go to `/login` and try logging in
4. **Check test page**: `/test-auth` should now show correct namespace

The enhanced detection will automatically find the right configuration for you! 🎯
