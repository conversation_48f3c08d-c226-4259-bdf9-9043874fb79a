"use client";

import React, { Suspense, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// Loading components for different contexts
export const PageLoadingSkeleton = () => (
  <div className="min-h-screen bg-gray-50 p-6">
    <div className="max-w-7xl mx-auto">
      <div className="animate-pulse">
        {/* Header skeleton */}
        <div className="mb-8">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        
        {/* Stats cards skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        
        {/* Content skeleton */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);

export const CardLoadingSkeleton = () => (
  <div className="bg-white rounded-lg shadow p-6">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded"></div>
        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>
  </div>
);

export const TableLoadingSkeleton = () => (
  <div className="bg-white rounded-lg shadow overflow-hidden">
    <div className="animate-pulse">
      {/* Table header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex space-x-4">
          <div className="h-4 bg-gray-200 rounded flex-1"></div>
          <div className="h-4 bg-gray-200 rounded flex-1"></div>
          <div className="h-4 bg-gray-200 rounded flex-1"></div>
          <div className="h-4 bg-gray-200 rounded w-20"></div>
        </div>
      </div>
      
      {/* Table rows */}
      <div className="divide-y divide-gray-200">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="px-6 py-4 flex space-x-4">
            <div className="h-4 bg-gray-200 rounded flex-1"></div>
            <div className="h-4 bg-gray-200 rounded flex-1"></div>
            <div className="h-4 bg-gray-200 rounded flex-1"></div>
            <div className="h-4 bg-gray-200 rounded w-20"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const FormLoadingSkeleton = () => (
  <div className="w-full max-w-2xl mx-auto">
    <div className="bg-white rounded-lg shadow p-6">
      <div className="animate-pulse">
        {/* Form header */}
        <div className="mb-6">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
        
        {/* Form fields */}
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
          
          {/* Form actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <div className="h-10 bg-gray-200 rounded w-20"></div>
            <div className="h-10 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Error fallback components
export const PageErrorFallback = ({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
    <div className="text-center max-w-md mx-auto">
      <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
        <span className="text-red-600 text-3xl">⚠️</span>
      </div>
      
      <h2 className="text-2xl font-bold text-gray-800 mb-4">
        Something went wrong!
      </h2>
      
      <p className="text-gray-600 mb-6">
        We encountered an unexpected error while loading this page.
      </p>
      
      {process.env.NODE_ENV === "development" && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
          <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
          <p className="text-sm text-red-700 font-mono break-all">
            {error.message}
          </p>
        </div>
      )}
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          onClick={resetErrorBoundary}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
        
        <button
          onClick={() => window.location.href = "/"}
          className="px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
        >
          Go Home
        </button>
      </div>
    </div>
  </div>
);

export const ComponentErrorFallback = ({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
    <div className="text-red-600 text-2xl mb-2">⚠️</div>
    <h3 className="text-lg font-semibold text-red-800 mb-2">
      Component Error
    </h3>
    <p className="text-red-700 text-sm mb-4">
      This component failed to load properly.
    </p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
    >
      Retry
    </button>
  </div>
);

// Suspense boundary wrapper component
interface SuspenseBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: React.ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  onError?: (error: Error, info: React.ErrorInfo) => void;
}

export const SuspenseBoundary: React.FC<SuspenseBoundaryProps> = ({
  children,
  fallback = <PageLoadingSkeleton />,
  errorFallback = PageErrorFallback,
  onError = () => {},
}) => {
  return (
    <ErrorBoundary
      FallbackComponent={errorFallback}
      onError={onError}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

// Specialized suspense boundaries for different contexts
export const PageSuspenseBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SuspenseBoundary
    fallback={<PageLoadingSkeleton />}
    errorFallback={PageErrorFallback}
  >
    {children}
  </SuspenseBoundary>
);

export const ComponentSuspenseBoundary: React.FC<{ 
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ children, fallback = <CardLoadingSkeleton /> }) => (
  <SuspenseBoundary
    fallback={fallback}
    errorFallback={ComponentErrorFallback}
  >
    {children}
  </SuspenseBoundary>
);

export const FormSuspenseBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SuspenseBoundary
    fallback={<FormLoadingSkeleton />}
    errorFallback={ComponentErrorFallback}
  >
    {children}
  </SuspenseBoundary>
);

export const TableSuspenseBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SuspenseBoundary
    fallback={<TableLoadingSkeleton />}
    errorFallback={ComponentErrorFallback}
  >
    {children}
  </SuspenseBoundary>
);
