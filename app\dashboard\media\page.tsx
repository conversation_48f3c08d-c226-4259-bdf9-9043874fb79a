'use client';

import {
  Download,
  FileImage,
  FileText,
  FileVideo,
  FolderOpen,
  Image,
  Plus,
  Search,
  Upload,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Mock data for demonstration
const mockMediaStats = {
  totalFiles: 1250,
  totalSize: '2.4 GB',
  images: 850,
  videos: 120,
  documents: 280,
};

const mockMediaFiles = [
  {
    id: 1,
    name: 'School Event 2024.jpg',
    type: 'image',
    size: '2.4 MB',
    uploadDate: '2024-01-15',
    category: 'Events',
    thumbnail: '/api/placeholder/150/150',
  },
  {
    id: 2,
    name: 'Student Handbook.pdf',
    type: 'document',
    size: '1.8 MB',
    uploadDate: '2024-01-14',
    category: 'Documents',
    thumbnail: null,
  },
  {
    id: 3,
    name: 'Graduation Ceremony.mp4',
    type: 'video',
    size: '45.2 MB',
    uploadDate: '2024-01-13',
    category: 'Events',
    thumbnail: '/api/placeholder/150/150',
  },
];

export default function MediaPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('All');
  const [categoryFilter, setCategoryFilter] = useState('All');

  const handleUploadMedia = () => {
    router.push('/dashboard/media/create');
  };
  const handleViewMedia = (mediaId: number) => {
    router.push(`/dashboard/media/${mediaId}`);
  };

  const handleEditMedia = (mediaId: number) => {
    router.push(`/dashboard/media/${mediaId}/edit`);
  };

  const handleDownloadMedia = (mediaId: number) => {
    // TODO: Download media file
    console.log('Download media:', mediaId);
  };

  const statsCards = [
    {
      title: 'Total Files',
      value: mockMediaStats.totalFiles.toString(),
      icon: FolderOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+15%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Size',
      value: mockMediaStats.totalSize,
      icon: Upload,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      title: 'Images',
      value: mockMediaStats.images.toString(),
      icon: Image,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'Documents',
      value: mockMediaStats.documents.toString(),
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
  ];

  const filteredFiles = mockMediaFiles.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'All' || file.type === typeFilter;
    const matchesCategory = categoryFilter === 'All' || file.category === categoryFilter;
    return matchesSearch && matchesType && matchesCategory;
  });

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return FileImage;
      case 'video':
        return FileVideo;
      case 'document':
        return FileText;
      default:
        return FileText;
    }
  };

  const getFileColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'text-green-600 bg-green-50';
      case 'video':
        return 'text-blue-600 bg-blue-50';
      case 'document':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <PageHeader
        title='Media Management'
        description='Manage school media files, photos, and documents'
        icon={Image}
        badge={{ label: 'Demo Data', variant: 'outline' }}
        actions={[
          {
            label: 'Upload Media',
            icon: Plus,
            onClick: handleUploadMedia,
          },
        ]}
      />

      {/* Stats Cards */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>{stat.title}</p>
                  <p className='text-2xl font-bold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search files...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Types</SelectItem>
                <SelectItem value='image'>Images</SelectItem>
                <SelectItem value='video'>Videos</SelectItem>
                <SelectItem value='document'>Documents</SelectItem>
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Select category' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='All'>All Categories</SelectItem>
                <SelectItem value='Events'>Events</SelectItem>
                <SelectItem value='Documents'>Documents</SelectItem>
                <SelectItem value='Academic'>Academic</SelectItem>
              </SelectContent>
            </Select>

            <div className='flex items-center text-sm text-muted-foreground'>
              Showing {filteredFiles.length} of {mockMediaFiles.length} files
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Media Files Grid */}
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
        {filteredFiles.map(file => {
          const FileIcon = getFileIcon(file.type);
          return (
            <Card key={file.id} className='hover:shadow-md transition-shadow'>
              <CardContent className='p-4'>
                <div className='aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center overflow-hidden'>
                  {file.thumbnail ? (
                    <img
                      src={file.thumbnail}
                      alt={file.name}
                      className='w-full h-full object-cover'
                    />
                  ) : (
                    <FileIcon className='w-12 h-12 text-gray-400' />
                  )}
                </div>

                <div className='space-y-2'>
                  <h3 className='font-semibold text-sm truncate' title={file.name}>
                    {file.name}
                  </h3>

                  <div className='flex items-center justify-between text-xs text-muted-foreground'>
                    <span>{file.size}</span>
                    <Badge variant='outline' className={`text-xs ${getFileColor(file.type)}`}>
                      {file.type}
                    </Badge>
                  </div>

                  <p className='text-xs text-muted-foreground'>
                    {file.category} • {file.uploadDate}
                  </p>

                  <div className='flex space-x-1 pt-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      className='flex-1 text-xs'
                      onClick={() => handleViewMedia(file.id)}
                    >
                      View
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      className='flex-1 text-xs'
                      onClick={() => handleEditMedia(file.id)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      className='flex-1 text-xs'
                      onClick={() => handleDownloadMedia(file.id)}
                    >
                      <Download className='w-3 h-3' />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredFiles.length === 0 && (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-16'>
            <div className='text-6xl mb-4'>🎬</div>
            <h3 className='text-lg font-semibold mb-2'>No Media Files Found</h3>
            <p className='text-muted-foreground text-center mb-4'>
              {searchTerm || typeFilter !== 'All' || categoryFilter !== 'All'
                ? 'No media files match your current filters.'
                : 'There are no media files in the system yet.'}
            </p>
            <Button onClick={handleUploadMedia}>
              <Plus className='mr-2 h-4 w-4' />
              Upload First File
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
