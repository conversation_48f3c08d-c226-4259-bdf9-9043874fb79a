import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import StudentsPage from '@/app/(dashboard)/students/page'
import { useQueryBase } from '@/hooks/useQueryBase'
import { mockStudents } from '@/lib/mockStudents'

// Mock the useQueryBase hook
jest.mock('@/hooks/useQueryBase')
const mockUseQueryBase = useQueryBase as jest.MockedFunction<typeof useQueryBase>

// Mock the API client
jest.mock('@/api/apiService', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}))

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('StudentList', () => {
  beforeEach(() => {
    // Mock successful data fetch
    mockUseQueryBase.mockReturnValue({
      data: mockStudents.slice(0, 5), // Return first 5 students
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders student list with data', async () => {
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Check if student names are displayed
    expect(screen.getByText(/emma wilson/i)).toBeInTheDocument()
    expect(screen.getByText(/james rodriguez/i)).toBeInTheDocument()
  })

  it('displays loading state with skeletons', () => {
    // Mock loading state
    mockUseQueryBase.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
    })

    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Check for loading skeletons
    expect(screen.getByTestId('students-loading')).toBeInTheDocument()
  })

  it('displays error state with retry option', () => {
    const mockRefetch = jest.fn()
    
    // Mock error state
    mockUseQueryBase.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch students'),
      refetch: mockRefetch,
      isRefetching: false,
    })

    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Check for error message and retry button
    expect(screen.getByText(/failed to fetch students/i)).toBeInTheDocument()
    
    const retryButton = screen.getByRole('button', { name: /try again/i })
    expect(retryButton).toBeInTheDocument()
    
    // Test retry functionality
    fireEvent.click(retryButton)
    expect(mockRefetch).toHaveBeenCalled()
  })

  it('filters students by search term', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/emma wilson/i)).toBeInTheDocument()
    })

    // Find and use search input
    const searchInput = screen.getByPlaceholderText(/search students/i)
    await user.type(searchInput, 'emma')

    // Check that only Emma Wilson is visible
    await waitFor(() => {
      expect(screen.getByText(/emma wilson/i)).toBeInTheDocument()
      expect(screen.queryByText(/james rodriguez/i)).not.toBeInTheDocument()
    })
  })

  it('filters students by grade level', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Find and use grade filter
    const gradeFilter = screen.getByRole('combobox', { name: /grade/i })
    await user.click(gradeFilter)
    
    // Select Grade 10
    const grade10Option = screen.getByRole('option', { name: /grade 10/i })
    await user.click(grade10Option)

    // Check that only Grade 10 students are visible
    await waitFor(() => {
      expect(screen.getByTestId('filtered-students')).toBeInTheDocument()
    })
  })

  it('filters students by enrollment status', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Find and use status filter
    const statusFilter = screen.getByRole('combobox', { name: /status/i })
    await user.click(statusFilter)
    
    // Select Active status
    const activeOption = screen.getByRole('option', { name: /active/i })
    await user.click(activeOption)

    // Check that only active students are visible
    await waitFor(() => {
      expect(screen.getByTestId('filtered-students')).toBeInTheDocument()
    })
  })

  it('displays student statistics correctly', async () => {
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for stats to load
    await waitFor(() => {
      expect(screen.getByText(/total students/i)).toBeInTheDocument()
    })

    // Check for stat cards
    expect(screen.getByText(/enrolled students/i)).toBeInTheDocument()
    expect(screen.getByText(/grade levels/i)).toBeInTheDocument()
  })

  it('toggles between table and card view', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Find view toggle buttons
    const cardViewButton = screen.getByRole('button', { name: /card view/i })
    const tableViewButton = screen.getByRole('button', { name: /table view/i })

    // Toggle to card view
    await user.click(cardViewButton)
    
    // Check that view changed
    await waitFor(() => {
      expect(screen.getByTestId('students-card-view')).toBeInTheDocument()
    })

    // Toggle back to table view
    await user.click(tableViewButton)
    
    // Check that view changed back
    await waitFor(() => {
      expect(screen.getByTestId('students-table-view')).toBeInTheDocument()
    })
  })

  it('handles student card actions', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for students to load
    await waitFor(() => {
      expect(screen.getByText(/emma wilson/i)).toBeInTheDocument()
    })

    // Find student card and actions
    const studentCard = screen.getByTestId('student-card-student_001')
    expect(studentCard).toBeInTheDocument()

    // Test view action
    const viewButton = screen.getByRole('button', { name: /view/i })
    await user.click(viewButton)

    // Verify view action (implementation dependent)
    // For now, just check that the button is clickable
    expect(viewButton).toBeInTheDocument()
  })

  it('opens add student dialog', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Find and click add student button
    const addButton = screen.getByRole('button', { name: /add student/i })
    await user.click(addButton)

    // Check that dialog opens
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText(/add new student/i)).toBeInTheDocument()
    })
  })

  it('handles bulk actions', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for students to load
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Select multiple students
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[1]) // First student checkbox
    await user.click(checkboxes[2]) // Second student checkbox

    // Check that bulk actions are available
    await waitFor(() => {
      expect(screen.getByText(/2 selected/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /bulk actions/i })).toBeInTheDocument()
    })
  })

  it('exports student data', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Find and click export button
    const exportButton = screen.getByRole('button', { name: /export/i })
    await user.click(exportButton)

    // Verify export functionality
    expect(exportButton).toBeInTheDocument()
  })

  it('handles pagination correctly', async () => {
    const user = userEvent.setup()
    
    // Mock more students for pagination
    mockUseQueryBase.mockReturnValue({
      data: mockStudents, // Return all students
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
    })

    render(
      <TestWrapper>
        <StudentsPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/students/i)).toBeInTheDocument()
    })

    // Check for pagination controls
    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).toBeInTheDocument()

    // Click next page
    await user.click(nextButton)

    // Verify pagination state change
    await waitFor(() => {
      expect(screen.getByText(/page 2/i)).toBeInTheDocument()
    })
  })
})
