/**
 * Validation Utilities Index
 * 
 * Centralized export for all validation utilities
 */

export {
  validateStudentCreate,
  validateStudentUpdate,
  validatePassword,
  validateDateOfBirth,
  validateCSVRow,
  isValidEmail,
  isValidPhoneNumber,
  checkForPotentialDuplicates,
  type ValidationResult,
  type UniqueFieldCheck,
} from './studentValidation';

// Re-export common validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  ALPHANUMERIC: /^[A-Za-z0-9]+$/,
  PASSWORD_UPPERCASE: /[A-Z]/,
  PASSWORD_DIGIT: /[0-9]/,
} as const;

// Common validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_DATE: 'Please enter a valid date',
  DATE_IN_FUTURE: 'Date cannot be in the future',
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
  PASSWORD_NO_UPPERCASE: 'Password must contain at least one uppercase letter',
  PASSWORD_NO_DIGIT: 'Password must contain at least one digit',
  ALPHANUMERIC_ONLY: 'Only letters and numbers are allowed',
} as const;
