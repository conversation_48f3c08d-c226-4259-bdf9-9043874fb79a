/**
 * Optimized Components Barrel Export
 * 
 * High-performance components with advanced optimization techniques:
 * - Memoization and re-render prevention
 * - Virtual scrolling for large datasets
 * - Debounced inputs and stable callbacks
 * - Performance monitoring integration
 */

// Optimized Data Components
export { OptimizedDataTable } from './OptimizedDataTable';

// Optimized Form Components
export {
  OptimizedTextField,
  OptimizedSelectField,
  OptimizedTextAreaField,
  ErrorMessage,
  FieldLabel,
} from './OptimizedFormField';

// Re-export performance hooks for convenience
export {
  useStableCallback,
  useDebouncedValue,
  useThrottledValue,
  useExpensiveComputation,
  usePrevious,
  useChanged,
  useStableReference,
  useRenderCount,
  useWhyDidYouUpdate,
  useIntersectionObserver,
  useVirtualList,
  useBatchedState,
} from '@/hooks/usePerformance';

// Re-export hydration hooks
export {
  useHydrateStore,
  useHydrateAuth,
  useIsClient,
  useHydrateLocalStorage,
  useHydrateTheme,
  useHydrateWindowSize,
  useHydrateMediaQuery,
  useClientOnly,
  useHydrateDate,
  useHydrateRandom,
} from '@/hooks/useHydrateStore';
