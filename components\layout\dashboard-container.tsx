/**
 * Dashboard Container Component
 * 
 * Standardized container for dashboard pages with consistent spacing and layout
 */

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface DashboardContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
}

const maxWidthVariants = {
  sm: 'max-w-screen-sm',
  md: 'max-w-screen-md',
  lg: 'max-w-screen-lg',
  xl: 'max-w-screen-xl',
  '2xl': 'max-w-screen-2xl',
  full: 'max-w-full',
};

const spacingVariants = {
  sm: 'space-y-4',
  md: 'space-y-6',
  lg: 'space-y-8',
  xl: 'space-y-12',
};

export function DashboardContainer({
  children,
  className,
  maxWidth = 'full',
  spacing = 'lg',
}: DashboardContainerProps) {
  return (
    <div className={cn(
      'mx-auto px-4 sm:px-6 lg:px-8',
      maxWidthVariants[maxWidth],
      spacingVariants[spacing],
      className
    )}>
      {children}
    </div>
  );
}
