/**
 * Student Validation Utilities
 * 
 * Comprehensive client-side validation to prevent 500 errors
 * and provide user-friendly error messages before API calls.
 */

import type { StudentCreate, StudentUpdate } from '@/types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface UniqueFieldCheck {
  field: string;
  value: string;
  exists: boolean;
}

/**
 * Validate student creation data before API call
 */
export function validateStudentCreate(data: StudentCreate): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required field validation
  if (!data.reg_no?.trim()) {
    errors.push('Registration number is required');
  } else if (!/^[A-Za-z0-9]+$/.test(data.reg_no)) {
    errors.push('Registration number must be alphanumeric (no spaces or special characters)');
  }

  if (!data.first_name?.trim()) {
    errors.push('First name is required');
  } else if (data.first_name.length > 100) {
    errors.push('First name must be less than 100 characters');
  }

  if (!data.last_name?.trim()) {
    errors.push('Last name is required');
  } else if (data.last_name.length > 100) {
    errors.push('Last name must be less than 100 characters');
  }

  if (!data.password?.trim()) {
    errors.push('Password is required');
  } else {
    const passwordErrors = validatePassword(data.password);
    errors.push(...passwordErrors);
  }

  if (!data.parent_id?.trim()) {
    errors.push('Parent ID is required');
  }

  if (!data.class_id?.trim()) {
    errors.push('Class is required');
  } else if (isNaN(parseInt(data.class_id))) {
    errors.push('Class ID must be a valid number');
  }

  if (!data.section_id?.trim()) {
    errors.push('Grade/Section is required');
  } else if (isNaN(parseInt(data.section_id))) {
    errors.push('Section ID must be a valid number');
  }

  // Optional field validation
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Please enter a valid email address');
  }

  if (data.dob) {
    const dobValidation = validateDateOfBirth(data.dob);
    if (!dobValidation.isValid) {
      errors.push(...dobValidation.errors);
    }
    warnings.push(...dobValidation.warnings);
  }

  if (data.guardian_phone && !isValidPhoneNumber(data.guardian_phone)) {
    warnings.push('Guardian phone number format may be invalid');
  }

  // Gender validation
  if (data.gender && !['male', 'female', 'other'].includes(data.gender)) {
    errors.push('Gender must be male, female, or other');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate student update data before API call
 */
export function validateStudentUpdate(data: StudentUpdate): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Only validate fields that are being updated
  if (data.reg_no !== undefined) {
    if (!data.reg_no?.trim()) {
      errors.push('Registration number cannot be empty');
    } else if (!/^[A-Za-z0-9]+$/.test(data.reg_no)) {
      errors.push('Registration number must be alphanumeric');
    }
  }

  if (data.first_name !== undefined) {
    if (!data.first_name?.trim()) {
      errors.push('First name cannot be empty');
    } else if (data.first_name.length > 100) {
      errors.push('First name must be less than 100 characters');
    }
  }

  if (data.last_name !== undefined) {
    if (!data.last_name?.trim()) {
      errors.push('Last name cannot be empty');
    } else if (data.last_name.length > 100) {
      errors.push('Last name must be less than 100 characters');
    }
  }

  if (data.email !== undefined && data.email && !isValidEmail(data.email)) {
    errors.push('Please enter a valid email address');
  }

  if (data.dob !== undefined && data.dob) {
    const dobValidation = validateDateOfBirth(data.dob);
    if (!dobValidation.isValid) {
      errors.push(...dobValidation.errors);
    }
    warnings.push(...dobValidation.warnings);
  }

  if (data.class_id !== undefined) {
    if (!data.class_id?.trim()) {
      errors.push('Class is required');
    } else if (isNaN(parseInt(data.class_id))) {
      errors.push('Class ID must be a valid number');
    }
  }

  if (data.section_id !== undefined) {
    if (!data.section_id?.trim()) {
      errors.push('Grade/Section is required');
    } else if (isNaN(parseInt(data.section_id))) {
      errors.push('Section ID must be a valid number');
    }
  }

  if (data.gender !== undefined && data.gender && !['male', 'female', 'other'].includes(data.gender)) {
    errors.push('Gender must be male, female, or other');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate password strength according to backend requirements
 */
export function validatePassword(password: string): string[] {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one digit');
  }

  return errors;
}

/**
 * Validate date of birth
 */
export function validateDateOfBirth(dob: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const dobDate = new Date(dob);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Check if date is valid
    if (isNaN(dobDate.getTime())) {
      errors.push('Invalid date format');
      return { isValid: false, errors, warnings };
    }

    // Check if date is in the future
    if (dobDate >= today) {
      errors.push('Date of birth must be in the past');
    }

    // Check if date is too far in the past (more than 100 years)
    const hundredYearsAgo = new Date();
    hundredYearsAgo.setFullYear(hundredYearsAgo.getFullYear() - 100);
    if (dobDate < hundredYearsAgo) {
      warnings.push('Date of birth is more than 100 years ago - please verify');
    }

    // Check if student is too young (less than 3 years old)
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
    if (dobDate > threeYearsAgo) {
      warnings.push('Student appears to be very young (under 3 years old)');
    }

  } catch (error) {
    errors.push('Invalid date format');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format (basic validation)
 */
export function isValidPhoneNumber(phone: string): boolean {
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '');
  
  // Check if it has a reasonable number of digits (7-15)
  return digitsOnly.length >= 7 && digitsOnly.length <= 15;
}

/**
 * Validate CSV row data before processing
 */
export function validateCSVRow(row: Record<string, string>, rowNumber: number): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields for CSV import
  const requiredFields = ['username', 'name', 'surname', 'admission_number', 'parent_id', 'class_id', 'grade_id', 'password'];
  
  for (const field of requiredFields) {
    if (!row[field]?.trim()) {
      errors.push(`Row ${rowNumber}: ${field} is required`);
    }
  }

  // Validate specific fields
  if (row.class_id && isNaN(parseInt(row.class_id))) {
    errors.push(`Row ${rowNumber}: class_id must be a valid number`);
  }

  if (row.grade_id && isNaN(parseInt(row.grade_id))) {
    errors.push(`Row ${rowNumber}: grade_id must be a valid number`);
  }

  if (row.email && !isValidEmail(row.email)) {
    errors.push(`Row ${rowNumber}: invalid email format`);
  }

  if (row.date_of_birth) {
    const dobValidation = validateDateOfBirth(row.date_of_birth);
    if (!dobValidation.isValid) {
      errors.push(`Row ${rowNumber}: ${dobValidation.errors.join(', ')}`);
    }
  }

  if (row.sex && !['MALE', 'FEMALE', 'OTHER'].includes(row.sex.toUpperCase())) {
    errors.push(`Row ${rowNumber}: sex must be MALE, FEMALE, or OTHER`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Check for potential duplicate values that might cause conflicts
 */
export function checkForPotentialDuplicates(data: StudentCreate | StudentUpdate): string[] {
  const warnings: string[] = [];

  // These checks would ideally be done against the actual database
  // For now, we provide warnings about fields that must be unique
  
  if ('reg_no' in data && data.reg_no) {
    // In a real implementation, you would check against existing students
    warnings.push('Please ensure the registration number is unique');
  }

  if (data.email) {
    warnings.push('Please ensure the email address is unique');
  }

  return warnings;
}
