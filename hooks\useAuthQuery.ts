/**
 * Authentication React Query Hooks
 * 
 * Stable query keys and proper cache management
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import * as authService from '@/services/auth';
import type {
  LoginPayload,
  ChangePasswordPayload,
  UpdateMePayload,
  RegisterPayload,
  UseLoginOptions,
  UseLogoutOptions,
  UseMeOptions,
} from '@/types/auth';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const authKeys = {
  all: ['auth'] as const,
  me: () => [...authKeys.all, 'me'] as const,
} as const;

// ============================================================================
// AUTH HOOKS
// ============================================================================

/**
 * Login mutation
 */
export function useLogin(options?: UseLoginOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authService.login,
    onSuccess: async (data) => {
      // Set cookie via session API
      await fetch('/api/session/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: data.access_token }),
      });
      
      // Invalidate me query to refetch user data
      queryClient.invalidateQueries({ queryKey: authKeys.me() });
      
      options?.onSuccess?.(data);
    },
    onError: options?.onError,
  });
}

/**
 * Logout mutation
 */
export function useLogout(options?: UseLogoutOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authService.logout,
    onSuccess: async () => {
      // Clear cookie via session API
      await fetch('/api/session/logout', { method: 'POST' });
      
      // Clear all auth-related queries
      queryClient.removeQueries({ queryKey: authKeys.all });
      
      options?.onSuccess?.();
    },
    onError: options?.onError,
  });
}

/**
 * Get current user query
 */
export function useMe(options?: UseMeOptions) {
  return useQuery({
    queryKey: authKeys.me(),
    queryFn: authService.me,
    staleTime: options?.staleTime ?? 10_000, // 10 seconds
    retry: options?.retry ?? 0, // Don't retry on 401
    enabled: options?.enabled,
  });
}

/**
 * Update current user mutation
 */
export function useUpdateMe() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authService.updateMe,
    onSuccess: (updatedUser) => {
      // Update the me query cache
      queryClient.setQueryData(authKeys.me(), updatedUser);
    },
  });
}

/**
 * Change password mutation
 */
export function useChangePassword() {
  return useMutation({
    mutationFn: authService.changePassword,
  });
}

/**
 * Register mutation (only available in users namespace)
 */
export function useRegister(options?: UseLoginOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authService.register,
    onSuccess: async (data) => {
      // Set cookie via session API
      await fetch('/api/session/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: data.access_token }),
      });
      
      // Invalidate me query to refetch user data
      queryClient.invalidateQueries({ queryKey: authKeys.me() });
      
      options?.onSuccess?.(data);
    },
    onError: options?.onError,
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Check if user is authenticated
 */
export function useIsAuthenticated() {
  const { data: user, isLoading } = useMe({ enabled: true });
  return {
    isAuthenticated: !!user,
    isLoading,
    user,
  };
}

/**
 * Get current user with loading state
 */
export function useCurrentUser() {
  return useMe({ enabled: true });
}
