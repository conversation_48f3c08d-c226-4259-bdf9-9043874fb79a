/**
 * Authentication Service
 * 
 * Handles all authentication-related API calls:
 * - Login/Logout
 * - Token refresh
 * - Password management
 * - Profile management
 */

import { apiUtils } from '../apiClient';
import type { 
  User, 
  LoginCredentials, 
  ChangePasswordData, 
  ProfileUpdateData 
} from '../../types';

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenResponse {
  token: string;
  expiresIn: number;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ResetPasswordConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export class AuthService {
  private static readonly BASE_URL = '/auth';

  /**
   * Authenticate user with credentials
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiUtils.post<AuthResponse>(`${this.BASE_URL}/login`, credentials);
  }

  /**
   * Logout user and invalidate token
   */
  static async logout(): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/logout`);
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return apiUtils.post<RefreshTokenResponse>(`${this.BASE_URL}/refresh`, {
      refreshToken,
    });
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<User> {
    return apiUtils.get<User>(`${this.BASE_URL}/profile`);
  }

  /**
   * Update user profile
   */
  static async updateProfile(data: ProfileUpdateData): Promise<User> {
    return apiUtils.patch<User>(`${this.BASE_URL}/profile`, data);
  }

  /**
   * Change user password
   */
  static async changePassword(data: ChangePasswordData): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/change-password`, data);
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(data: ResetPasswordRequest): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/reset-password`, data);
  }

  /**
   * Confirm password reset with token
   */
  static async confirmPasswordReset(data: ResetPasswordConfirm): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/reset-password/confirm`, data);
  }

  /**
   * Verify email address
   */
  static async verifyEmail(token: string): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/verify-email`, { token });
  }

  /**
   * Resend email verification
   */
  static async resendEmailVerification(): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/resend-verification`);
  }

  /**
   * Enable two-factor authentication
   */
  static async enableTwoFactor(): Promise<{ qrCode: string; secret: string }> {
    return apiUtils.post<{ qrCode: string; secret: string }>(`${this.BASE_URL}/2fa/enable`);
  }

  /**
   * Confirm two-factor authentication setup
   */
  static async confirmTwoFactor(code: string): Promise<{ backupCodes: string[] }> {
    return apiUtils.post<{ backupCodes: string[] }>(`${this.BASE_URL}/2fa/confirm`, { code });
  }

  /**
   * Disable two-factor authentication
   */
  static async disableTwoFactor(code: string): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/2fa/disable`, { code });
  }

  /**
   * Verify two-factor authentication code
   */
  static async verifyTwoFactor(code: string): Promise<AuthResponse> {
    return apiUtils.post<AuthResponse>(`${this.BASE_URL}/2fa/verify`, { code });
  }

  /**
   * Get user sessions
   */
  static async getSessions(): Promise<Array<{
    id: string;
    device: string;
    location: string;
    lastActive: string;
    current: boolean;
  }>> {
    return apiUtils.get<Array<{
      id: string;
      device: string;
      location: string;
      lastActive: string;
      current: boolean;
    }>>(`${this.BASE_URL}/sessions`);
  }

  /**
   * Revoke user session
   */
  static async revokeSession(sessionId: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/sessions/${sessionId}`);
  }

  /**
   * Revoke all other sessions
   */
  static async revokeAllOtherSessions(): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/sessions/revoke-all`);
  }
}

// Export default instance
export const authService = AuthService;
