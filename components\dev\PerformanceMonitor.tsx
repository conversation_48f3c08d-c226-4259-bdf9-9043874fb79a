"use client";

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Activity,
    AlertTriangle,
    CheckCircle,
    Clock,
    Cpu,
    Monitor,
    TrendingUp,
    Zap
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage: number;
  componentCount: number;
  reRenderReasons: string[];
}

interface ComponentPerformance {
  name: string;
  renderCount: number;
  lastRenderDuration: number;
  averageRenderDuration: number;
  isOptimized: boolean;
}

/**
 * Performance Monitor Component (Development Only)
 *
 * Provides real-time performance monitoring for React components:
 * - Render count tracking
 * - Memory usage monitoring
 * - Component performance analysis
 * - Re-render reason detection
 * - Performance recommendations
 */
export function PerformanceMonitor() {
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    memoryUsage: 0,
    componentCount: 0,
    reRenderReasons: [],
  });

  const [componentPerformance, setComponentPerformance] = useState<ComponentPerformance[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    if (isVisible) {
      intervalRef.current = setInterval(() => {
        updateMetrics();
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isVisible]);

  const updateMetrics = () => {
    // Get memory usage (if available)
    const memoryInfo = (performance as any).memory;
    const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0;

    // Get performance entries
    const navigationEntries = performance.getEntriesByType('navigation');
    const paintEntries = performance.getEntriesByType('paint');

    // Mock component performance data (in real app, this would come from actual monitoring)
    const mockComponentPerformance: ComponentPerformance[] = [
      {
        name: 'TeacherForm',
        renderCount: Math.floor(Math.random() * 10) + 1,
        lastRenderDuration: Math.random() * 5,
        averageRenderDuration: Math.random() * 3,
        isOptimized: true,
      },
      {
        name: 'StudentList',
        renderCount: Math.floor(Math.random() * 20) + 5,
        lastRenderDuration: Math.random() * 10,
        averageRenderDuration: Math.random() * 7,
        isOptimized: false,
      },
      {
        name: 'Dashboard',
        renderCount: Math.floor(Math.random() * 5) + 1,
        lastRenderDuration: Math.random() * 15,
        averageRenderDuration: Math.random() * 12,
        isOptimized: true,
      },
    ];

    setMetrics({
      renderCount: Math.floor(Math.random() * 100) + 50,
      lastRenderTime: performance.now(),
      averageRenderTime: Math.random() * 5,
      memoryUsage,
      componentCount: mockComponentPerformance.length,
      reRenderReasons: [
        'Props changed',
        'State updated',
        'Context value changed',
        'Parent re-rendered',
      ],
    });

    setComponentPerformance(mockComponentPerformance);
  };

  const getPerformanceStatus = (renderDuration: number) => {
    if (renderDuration < 2) return { status: 'good', color: 'green', icon: CheckCircle };
    if (renderDuration < 5) return { status: 'warning', color: 'yellow', icon: AlertTriangle };
    return { status: 'poor', color: 'red', icon: AlertTriangle };
  };

  const formatMemory = (bytes: number) => {
    return `${bytes.toFixed(2)} MB`;
  };

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)}ms`;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 left-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-2 border-blue-200 hover:border-blue-300"
        >
          <Monitor className="w-4 h-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 w-96 max-h-96 overflow-hidden">
      <Card className="shadow-2xl border-2 border-blue-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg">Performance Monitor</CardTitle>
            </div>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
          <CardDescription>
            Real-time performance metrics and optimization insights
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-0">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 text-xs">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
              <TabsTrigger value="recommendations">Tips</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-3 mt-3">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="bg-blue-50 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <Zap className="w-3 h-3 text-blue-600" />
                    <span className="font-medium">Renders</span>
                  </div>
                  <div className="text-lg font-bold text-blue-700">
                    {metrics.renderCount}
                  </div>
                </div>

                <div className="bg-green-50 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <Cpu className="w-3 h-3 text-green-600" />
                    <span className="font-medium">Memory</span>
                  </div>
                  <div className="text-lg font-bold text-green-700">
                    {formatMemory(metrics.memoryUsage)}
                  </div>
                </div>

                <div className="bg-purple-50 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 text-purple-600" />
                    <span className="font-medium">Avg Render</span>
                  </div>
                  <div className="text-lg font-bold text-purple-700">
                    {formatTime(metrics.averageRenderTime)}
                  </div>
                </div>

                <div className="bg-orange-50 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="w-3 h-3 text-orange-600" />
                    <span className="font-medium">Components</span>
                  </div>
                  <div className="text-lg font-bold text-orange-700">
                    {metrics.componentCount}
                  </div>
                </div>
              </div>

              {/* Re-render Reasons */}
              <div>
                <h4 className="text-sm font-medium mb-2">Recent Re-render Triggers</h4>
                <div className="space-y-1">
                  {metrics.reRenderReasons.slice(0, 3).map((reason, index) => (
                    <div key={index} className="text-xs bg-gray-50 px-2 py-1 rounded">
                      {reason}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="components" className="space-y-2 mt-3">
              <div className="max-h-48 overflow-y-auto space-y-2">
                {componentPerformance.map((component, index) => {
                  const { status, color, icon: StatusIcon } = getPerformanceStatus(
                    component.averageRenderDuration
                  );

                  return (
                    <div key={index} className="bg-gray-50 p-2 rounded text-xs">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{component.name}</span>
                        <div className="flex items-center space-x-1">
                          <StatusIcon className={`w-3 h-3 text-${color}-600`} />
                          {component.isOptimized && (
                            <Badge variant="secondary" className="text-xs px-1 py-0">
                              Optimized
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                        <div>Renders: {component.renderCount}</div>
                        <div>Avg: {formatTime(component.averageRenderDuration)}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="recommendations" className="space-y-2 mt-3">
              <div className="text-xs space-y-2">
                <div className="bg-green-50 p-2 rounded border border-green-200">
                  <div className="font-medium text-green-800 mb-1">✅ Good Practices</div>
                  <ul className="text-green-700 space-y-1">
                    <li>• Using React.memo for expensive components</li>
                    <li>• Implementing lazy loading</li>
                    <li>• Proper dependency arrays in hooks</li>
                  </ul>
                </div>

                <div className="bg-yellow-50 p-2 rounded border border-yellow-200">
                  <div className="font-medium text-yellow-800 mb-1">⚠️ Optimization Tips</div>
                  <ul className="text-yellow-700 space-y-1">
                    <li>• Consider virtualizing large lists</li>
                    <li>• Debounce expensive operations</li>
                    <li>• Split large components</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-2 rounded border border-blue-200">
                  <div className="font-medium text-blue-800 mb-1">💡 Tools Available</div>
                  <ul className="text-blue-700 space-y-1">
                    <li>• usePerformance hooks</li>
                    <li>• OptimizedDataTable</li>
                    <li>• Lazy loading components</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// Default export for lazy loading
export default PerformanceMonitor;
