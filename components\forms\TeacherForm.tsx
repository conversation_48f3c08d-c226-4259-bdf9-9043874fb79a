"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Loader2, Save, X } from "lucide-react";
import { useForm } from "react-hook-form";

// Schema imports - demonstrating strict type usage
import {
    TeacherCreateSchema,
    TeacherUpdateSchema,
    type TeacherCreate,
    type TeacherUpdate
} from "@/schemas/zodSchemas";

// UI Components
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormSection
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

// Types for the component props - strict typing
interface TeacherFormProps {
  mode: 'create' | 'edit';
  initialData?: TeacherUpdate;
  onSubmit: (data: TeacherCreate | TeacherUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Department options - could come from API or constants
const DEPARTMENTS = [
  "Mathematics",
  "Science",
  "English",
  "History",
  "Physical Education",
  "Art",
  "Music",
  "Computer Science"
] as const;

// Subject options - demonstrating type-safe options
const SUBJECTS = [
  "Mathematics",
  "Physics",
  "Chemistry",
  "Biology",
  "English Literature",
  "History",
  "Geography",
  "Physical Education",
  "Art",
  "Music",
  "Computer Science",
  "Economics"
] as const;

const STATUS_OPTIONS = [
  { value: "ACTIVE", label: "Active" },
  { value: "INACTIVE", label: "Inactive" }
] as const;

/**
 * TeacherForm Component
 *
 * Demonstrates strict type usage with Zod + React Hook Form:
 * - Uses zodResolver for validation
 * - Types inferred from Zod schemas
 * - Proper error handling and display
 * - Real-time validation
 * - Type-safe form submission
 */
export function TeacherForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false
}: TeacherFormProps) {

  // Schema selection based on mode - demonstrating conditional schema usage
  const schema = mode === 'create' ? TeacherCreateSchema : TeacherUpdateSchema;

  // Form initialization with strict typing
  const form = useForm<TeacherCreate | TeacherUpdate>({
    resolver: zodResolver(schema),
    defaultValues: mode === 'create'
      ? {
          name: "",
          subject: "",
          email: "",
          department: "",
          phone: "",
          status: "ACTIVE",
          hire_date: new Date().toISOString().split('T')[0],
        }
      : initialData || {},
    mode: "onChange", // Real-time validation
  });

  // Type-safe form submission
  const handleSubmit = async (data: TeacherCreate | TeacherUpdate) => {
    try {
      // Validate data against schema before submission
      const validatedData = schema.parse(data);
      await onSubmit(validatedData);
    } catch (error) {
      console.error("Form validation error:", error);
      // Handle validation errors if needed
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Add New Teacher' : 'Edit Teacher'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Fill in the details to add a new teacher to the system.'
            : 'Update the teacher information below.'
          }
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">

            {/* Basic Information Section */}
            <FormSection
              title="Basic Information"
              description="Essential teacher details"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                {/* Name Field - Required */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter teacher's full name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field - Optional but validated */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone Field - Optional but validated */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+****************"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status Field - Enum validation */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Professional Information Section */}
            <FormSection
              title="Professional Information"
              description="Teaching credentials and assignment details"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                {/* Subject Field - Required */}
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Subject *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select primary subject" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {SUBJECTS.map((subject) => (
                            <SelectItem key={subject} value={subject}>
                              {subject}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Department Field - Optional */}
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value || ''}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DEPARTMENTS.map((dept) => (
                            <SelectItem key={dept} value={dept}>
                              {dept}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Hire Date Field - Optional */}
                <FormField
                  control={form.control}
                  name="hire_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hire Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>

              <Button
                type="submit"
                disabled={isLoading || !form.formState.isValid}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {mode === 'create' ? 'Create Teacher' : 'Update Teacher'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Export type for external usage
export type { TeacherFormProps };

// Default export for lazy loading
export default TeacherForm;
