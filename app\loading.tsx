/**
 * Global Loading Component
 * 
 * This loading component is shown while pages are loading
 * Provides a consistent loading experience across the app
 */
export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        {/* School logo/icon */}
        <div className="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
          <span className="text-white font-bold text-2xl">🎓</span>
        </div>
        
        {/* Loading spinner */}
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        
        {/* Loading text */}
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          School Management System
        </h2>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
