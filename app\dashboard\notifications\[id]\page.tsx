import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Bell, Calendar, Clock, Edit, MessageSquare, User } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Mock notification data
const mockNotifications = [
  {
    id: 1,
    title: 'Parent-Teacher Conference Reminder',
    message: 'Reminder: Parent-teacher conferences are scheduled for March 20th. Please confirm your attendance.',
    type: 'Reminder',
    priority: 'High',
    status: 'Unread',
    sender: 'Administration',
    recipient: 'All Parents',
    date: '2024-03-01',
    time: '09:00 AM',
    readBy: 45,
    totalRecipients: 120,
    createdAt: '2024-03-01T09:00:00Z',
    updatedAt: '2024-03-01T09:00:00Z',
  },
  {
    id: 2,
    title: 'School Closure Due to Weather',
    message: 'Due to severe weather conditions, the school will be closed tomorrow, March 2nd. All classes are cancelled.',
    type: 'Alert',
    priority: 'Critical',
    status: 'Read',
    sender: 'Principal Office',
    recipient: 'All Students & Parents',
    date: '2024-03-01',
    time: '06:30 AM',
    readBy: 98,
    totalRecipients: 150,
    createdAt: '2024-03-01T06:30:00Z',
    updatedAt: '2024-03-01T06:30:00Z',
  },
];

interface NotificationDetailPageProps {
  params: {
    id: string;
  };
}

export default function NotificationDetailPage({ params }: NotificationDetailPageProps) {
  const notification = mockNotifications.find(n => n.id === parseInt(params.id));

  if (!notification) {
    notFound();
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Alert': return 'bg-red-100 text-red-800';
      case 'Reminder': return 'bg-blue-100 text-blue-800';
      case 'Announcement': return 'bg-purple-100 text-purple-800';
      case 'Information': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/notifications'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Notifications
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <Bell className='w-8 h-8 text-blue-600' />
              Notification Details
            </h1>
            <p className='text-gray-600 mt-1'>View notification information and delivery status</p>
          </div>
        </div>
        <Link href={`/dashboard/notifications/${notification.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Notification
          </Button>
        </Link>
      </div>

      {/* Notification Details */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div className='space-y-2'>
                  <CardTitle className='text-2xl'>{notification.title}</CardTitle>
                  <div className='flex gap-2'>
                    <Badge className={getTypeColor(notification.type)}>
                      {notification.type}
                    </Badge>
                    <Badge className={getPriorityColor(notification.priority)}>
                      {notification.priority}
                    </Badge>
                    <Badge variant={notification.status === 'Unread' ? 'default' : 'outline'}>
                      {notification.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div>
                <h3 className='font-semibold mb-2'>Message</h3>
                <p className='text-gray-700 leading-relaxed'>{notification.message}</p>
              </div>

              <Separator />

              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Date:</span> {notification.date}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Time:</span> {notification.time}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <User className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Sender:</span> {notification.sender}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <MessageSquare className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Recipients:</span> {notification.recipient}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* Delivery Status */}
          <Card>
            <CardHeader>
              <CardTitle>Delivery Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='text-center'>
                  <div className='text-3xl font-bold text-blue-600'>
                    {notification.readBy}/{notification.totalRecipients}
                  </div>
                  <p className='text-sm text-muted-foreground'>Recipients Read</p>
                </div>
                <div className='w-full bg-gray-200 rounded-full h-2'>
                  <div
                    className='bg-blue-600 h-2 rounded-full'
                    style={{
                      width: `${(notification.readBy / notification.totalRecipients) * 100}%`,
                    }}
                  ></div>
                </div>
                <div className='text-center text-sm text-muted-foreground'>
                  {Math.round((notification.readBy / notification.totalRecipients) * 100)}% Read Rate
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Created</p>
                <p className='text-sm'>{new Date(notification.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Last Updated</p>
                <p className='text-sm'>{new Date(notification.updatedAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Notification ID</p>
                <p className='text-sm font-mono'>{notification.id}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
