'use client';

import { useLogin, useLogout, useMe } from '@/hooks/useAuthQuery';
import { useToggleUserActivation, useUsers } from '@/hooks/useUsersQuery';
import { validateNamespaceConfig } from '@/lib/auth-config';
import { useRef, useState } from 'react';

interface LogEntry {
  timestamp: string;
  endpoint: string;
  method: string;
  status?: number;
  headers?: Record<string, string>;
  body?: any;
  error?: string;
}

export default function TestAuthPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [namespaceResult, setNamespaceResult] = useState<any>(null);
  const [testUserId, setTestUserId] = useState('');
  const logsEndRef = useRef<HTMLDivElement>(null);

  // React Query hooks
  const loginMutation = useLogin();
  const logoutMutation = useLogout();
  const { data: currentUser, isLoading: userLoading, error: userError } = useMe();
  const { data: usersList, isLoading: usersLoading } = useUsers();
  const toggleUserMutation = useToggleUserActivation();

  const addLog = (entry: Omit<LogEntry, 'timestamp'>) => {
    const newEntry: LogEntry = {
      ...entry,
      timestamp: new Date().toISOString(),
    };
    setLogs(prev => [...prev, newEntry]);

    // Auto-scroll to bottom
    setTimeout(() => {
      logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const handleDetectNamespace = async () => {
    try {
      addLog({
        endpoint: 'Namespace Detection',
        method: 'GET',
        body: 'Starting namespace detection...',
      });

      const validation = await validateNamespaceConfig();
      setNamespaceResult(validation);

      addLog({
        endpoint: 'Namespace Detection',
        method: 'GET',
        status: 200,
        body: {
          detected_namespace: validation.detectedNamespace,
          current_namespace: validation.currentNamespace,
          is_valid: validation.isValid,
          suggestion: validation.suggestion,
          test_results: validation.details,
        },
      });

      // Log each test result separately for clarity
      validation.details.forEach((detail, index) => {
        addLog({
          endpoint: `Test ${index + 1}: ${detail.namespace}`,
          method: 'GET',
          status: typeof detail.status === 'number' ? detail.status : undefined,
          body: {
            url: detail.url,
            status: detail.status,
            response: detail.response,
          },
          error: detail.status === 'NETWORK_ERROR' ? detail.response : undefined,
        });
      });
    } catch (error) {
      addLog({
        endpoint: 'Namespace Detection',
        method: 'GET',
        error: String(error),
      });
    }
  };

  const handleDirectBackendTest = async () => {
    const apiBase = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';
    const testEndpoints = [
      `${apiBase}/api/v1/auth/login`,
      `${apiBase}/api/v1/users/auth/login`,
      `${apiBase}/api/v1/auth/me`,
      `${apiBase}/api/v1/users/auth/me`,
      `${apiBase}/health`,
      `${apiBase}/docs`,
      `${apiBase}/`,
    ];

    addLog({
      endpoint: 'Direct Backend Test',
      method: 'GET',
      body: `Testing direct backend connectivity to ${apiBase}`,
    });

    for (const url of testEndpoints) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            Accept: 'application/json',
          },
        });

        let responseData: any = null;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }

        addLog({
          endpoint: `Direct: ${url}`,
          method: 'GET',
          status: response.status,
          body: {
            url,
            status: response.status,
            headers: Object.fromEntries(response.headers.entries()),
            response: responseData,
          },
        });
      } catch (error) {
        addLog({
          endpoint: `Direct: ${url}`,
          method: 'GET',
          error: String(error),
          body: { url, error: String(error) },
        });
      }
    }
  };

  const handleTestLoginEndpoint = async () => {
    const apiBase = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';
    const loginEndpoints = [`${apiBase}/api/v1/auth/login`, `${apiBase}/api/v1/users/auth/login`];

    const testCredentials = [
      { username: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'admin123' },
      { username: 'admin', password: 'admin123' },
      { email: 'admin', password: 'admin123' },
    ];

    addLog({
      endpoint: 'Test Login Endpoints',
      method: 'POST',
      body: 'Testing login endpoints with different credential formats',
    });

    for (const url of loginEndpoints) {
      for (const credentials of testCredentials) {
        try {
          addLog({
            endpoint: `Login Test: ${url}`,
            method: 'POST',
            body: { url, credentials },
          });

          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
            body: JSON.stringify(credentials),
          });

          let responseData: any = null;
          try {
            responseData = await response.json();
          } catch (e) {
            responseData = await response.text();
          }

          addLog({
            endpoint: `Login Result: ${url}`,
            method: 'POST',
            status: response.status,
            body: {
              url,
              credentials,
              status: response.status,
              response: responseData,
              success: response.status === 200,
            },
          });

          // If we get a successful login, break
          if (response.status === 200) {
            addLog({
              endpoint: 'Login Success!',
              method: 'POST',
              status: 200,
              body: `✅ Found working login: ${url} with ${JSON.stringify(credentials)}`,
            });
            return;
          }
        } catch (error) {
          addLog({
            endpoint: `Login Error: ${url}`,
            method: 'POST',
            error: String(error),
            body: { url, credentials, error: String(error) },
          });
        }
      }
    }
  };

  const handleLogin = async () => {
    try {
      addLog({
        endpoint: 'Login',
        method: 'POST',
        body: { username: '<EMAIL>', password: 'admin123' },
      });

      await loginMutation.mutateAsync({
        username: '<EMAIL>',
        password: 'admin123',
      });

      addLog({
        endpoint: 'Login',
        method: 'POST',
        status: 200,
        body: 'Login successful',
      });
    } catch (error: any) {
      addLog({
        endpoint: 'Login',
        method: 'POST',
        status: error.response?.status || 500,
        error: error.message,
        body: error.response?.data,
      });
    }
  };

  const handleLogout = async () => {
    try {
      addLog({
        endpoint: 'Logout',
        method: 'POST',
        body: 'Logging out...',
      });

      await logoutMutation.mutateAsync();

      addLog({
        endpoint: 'Logout',
        method: 'POST',
        status: 200,
        body: 'Logout successful',
      });
    } catch (error: any) {
      addLog({
        endpoint: 'Logout',
        method: 'POST',
        status: error.response?.status || 500,
        error: error.message,
      });
    }
  };

  const handleGetMe = () => {
    addLog({
      endpoint: 'Get Me',
      method: 'GET',
      status: userError ? 401 : 200,
      body: userError ? userError : currentUser,
      error: userError ? String(userError) : undefined,
    });
  };

  const handleListUsers = () => {
    addLog({
      endpoint: 'List Users',
      method: 'GET',
      status: usersLoading ? undefined : 200,
      body: usersLoading ? 'Loading...' : usersList,
    });
  };

  const handleToggleUser = async () => {
    if (!testUserId) {
      addLog({
        endpoint: 'Toggle User',
        method: 'PATCH',
        error: 'User ID is required',
      });
      return;
    }

    try {
      addLog({
        endpoint: 'Toggle User',
        method: 'PATCH',
        body: { userId: testUserId, action: 'toggle' },
      });

      await toggleUserMutation.mutateAsync({
        userId: testUserId,
        isActive: true, // This will be determined by the hook
      });

      addLog({
        endpoint: 'Toggle User',
        method: 'PATCH',
        status: 200,
        body: 'User activation toggled successfully',
      });
    } catch (error: any) {
      addLog({
        endpoint: 'Toggle User',
        method: 'PATCH',
        status: error.response?.status || 500,
        error: error.message,
      });
    }
  };

  return (
    <div className='min-h-screen bg-gray-50 p-8'>
      <div className='max-w-6xl mx-auto'>
        <h1 className='text-3xl font-bold mb-8'>🧪 Auth + Users API Test Page</h1>

        {/* Quick Diagnostic Instructions */}
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'>
          <h2 className='text-lg font-semibold text-blue-900 mb-2'>🚨 Getting 404 on Login?</h2>
          <div className='text-blue-800 text-sm space-y-2'>
            <p>
              <strong>Step 1:</strong> Click "Test Backend Direct" to check if your backend is
              running
            </p>
            <p>
              <strong>Step 2:</strong> Click "Detect Namespace" to find the correct API namespace
            </p>
            <p>
              <strong>Step 3:</strong> Click "Test Login Endpoint" to test actual login with
              different formats
            </p>
            <p>
              <strong>Expected:</strong> 401 = good endpoint, wrong credentials | 404 = wrong
              endpoint path
            </p>
          </div>
        </div>

        {/* Namespace Detection */}
        <div className='bg-white rounded-lg shadow p-6 mb-6'>
          <h2 className='text-xl font-semibold mb-4'>🔍 Namespace Detection</h2>
          <div className='flex gap-4 mb-4'>
            <button
              onClick={handleDetectNamespace}
              className='px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700'
            >
              Detect Namespace
            </button>
            <button
              onClick={handleDirectBackendTest}
              className='px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700'
            >
              Test Backend Direct
            </button>
            <button
              onClick={handleTestLoginEndpoint}
              className='px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700'
            >
              Test Login Endpoint
            </button>
          </div>

          {namespaceResult && (
            <div className='space-y-4'>
              <div
                className={`p-4 rounded ${
                  namespaceResult.isValid
                    ? 'bg-green-100 border border-green-400'
                    : 'bg-red-100 border border-red-400'
                }`}
              >
                <p>
                  <strong>Current:</strong> {namespaceResult.currentNamespace}
                </p>
                <p>
                  <strong>Detected:</strong> {namespaceResult.detectedNamespace || 'None'}
                </p>
                <p>
                  <strong>Valid:</strong> {namespaceResult.isValid ? '✅' : '❌'}
                </p>
                {namespaceResult.suggestion && (
                  <p>
                    <strong>Suggestion:</strong> {namespaceResult.suggestion}
                  </p>
                )}
              </div>

              {namespaceResult.details && (
                <div className='bg-gray-50 border border-gray-200 rounded p-4'>
                  <h4 className='font-medium mb-2'>Test Results:</h4>
                  <div className='space-y-2'>
                    {namespaceResult.details.map((detail, index) => (
                      <div key={index} className='text-sm'>
                        <div className='flex items-center gap-2'>
                          <span className='font-medium'>{detail.namespace}:</span>
                          <span
                            className={`px-2 py-1 rounded text-xs ${
                              detail.status === 401
                                ? 'bg-green-100 text-green-800'
                                : detail.status === 404
                                ? 'bg-red-100 text-red-800'
                                : detail.status === 'NETWORK_ERROR'
                                ? 'bg-orange-100 text-orange-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {detail.status}
                          </span>
                        </div>
                        <div className='text-gray-600 text-xs ml-2'>{detail.url}</div>
                        {detail.response && typeof detail.response === 'object' && (
                          <div className='text-gray-500 text-xs ml-2 font-mono'>
                            {JSON.stringify(detail.response, null, 2)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Auth Operations */}
        <div className='bg-white rounded-lg shadow p-6 mb-6'>
          <h2 className='text-xl font-semibold mb-4'>🔐 Authentication</h2>
          <div className='flex gap-4 mb-4'>
            <button
              onClick={handleLogin}
              disabled={loginMutation.isPending}
              className='px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50'
            >
              {loginMutation.isPending ? 'Logging in...' : 'Login'}
            </button>
            <button
              onClick={handleLogout}
              disabled={logoutMutation.isPending}
              className='px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50'
            >
              {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
            </button>
            <button
              onClick={handleGetMe}
              className='px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700'
            >
              Get Me
            </button>
          </div>

          {currentUser && (
            <div className='p-4 bg-green-100 border border-green-400 rounded'>
              <p>
                <strong>Logged in as:</strong> {currentUser.username} ({currentUser.email})
              </p>
              <p>
                <strong>Active:</strong> {currentUser.is_active ? '✅' : '❌'}
              </p>
            </div>
          )}
        </div>

        {/* Users Operations */}
        <div className='bg-white rounded-lg shadow p-6 mb-6'>
          <h2 className='text-xl font-semibold mb-4'>👥 Users Management</h2>
          <div className='flex gap-4 mb-4'>
            <button
              onClick={handleListUsers}
              className='px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700'
            >
              List Users
            </button>
            <div className='flex gap-2'>
              <input
                type='text'
                placeholder='User ID'
                value={testUserId}
                onChange={e => setTestUserId(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded'
              />
              <button
                onClick={handleToggleUser}
                disabled={toggleUserMutation.isPending}
                className='px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50'
              >
                {toggleUserMutation.isPending ? 'Toggling...' : 'Toggle User'}
              </button>
            </div>
          </div>
        </div>

        {/* Console Logs */}
        <div className='bg-white rounded-lg shadow p-6'>
          <div className='flex justify-between items-center mb-4'>
            <h2 className='text-xl font-semibold'>📋 Console Logs</h2>
            <button
              onClick={clearLogs}
              className='px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700'
            >
              Clear Results
            </button>
          </div>

          <div className='bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto'>
            {logs.length === 0 ? (
              <p>No logs yet. Click buttons above to test API endpoints.</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className='mb-2 border-b border-gray-700 pb-2'>
                  <div className='text-yellow-400'>
                    [{log.timestamp}] {log.method} {log.endpoint}
                  </div>
                  {log.status && (
                    <div className={log.status >= 400 ? 'text-red-400' : 'text-green-400'}>
                      Status: {log.status}
                    </div>
                  )}
                  {log.headers && (
                    <div className='text-blue-400'>
                      Headers: {JSON.stringify(log.headers, null, 2)}
                    </div>
                  )}
                  {log.body && (
                    <div className='text-white'>
                      Body:{' '}
                      {typeof log.body === 'string' ? log.body : JSON.stringify(log.body, null, 2)}
                    </div>
                  )}
                  {log.error && <div className='text-red-400'>Error: {log.error}</div>}
                </div>
              ))
            )}
            <div ref={logsEndRef} />
          </div>
        </div>
      </div>
    </div>
  );
}
