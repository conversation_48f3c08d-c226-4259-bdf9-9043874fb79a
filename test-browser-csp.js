/**
 * Browser-Based CSP Verification Test
 * 
 * Run this in your browser console at http://localhost:3000/dashboard/students
 * This tests the actual CSP and API connectivity in the browser environment.
 */

async function testBrowserCsp() {
  console.log('🧪 Testing CSP Fixes in Browser...\n');

  try {
    // Test 1: Check if we can make a request to the proxied API
    console.log('1️⃣ Testing Next.js API proxy (rewrites)...');
    const response = await fetch('/api/students/');
    
    if (response.ok) {
      console.log('✅ Next.js API proxy working - CSP allows same-origin requests');
      const data = await response.json();
      console.log('📊 Response structure:', Object.keys(data));
      if (data.items) {
        console.log(`📚 Found ${data.items.length} students`);
      }
    } else {
      console.log('⚠️ API proxy responded with status:', response.status);
      const errorData = await response.text();
      console.log('📝 Error details:', errorData);
    }
  } catch (error) {
    console.log('❌ API proxy test failed:', error.message);
    console.log('🔍 This usually means rewrites are not working or backend is down');
  }

  try {
    // Test 2: Test student creation endpoint
    console.log('\n2️⃣ Testing student creation endpoint...');
    const testStudentData = {
      admission_number: "TEST001",
      name: "Test",
      surname: "Student",
      sex: "male",
      class_id: 1,
      grade_id: 1,
      password: "TestPassword123",
      parent_id: "test-parent-id"
    };

    const createResponse = await fetch('/api/students/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testStudentData),
    });

    if (createResponse.ok) {
      console.log('✅ Student creation endpoint accessible via proxy');
      const result = await createResponse.json();
      console.log('📝 Created student:', result);
    } else {
      console.log('⚠️ Student creation endpoint responded with status:', createResponse.status);
      const errorData = await createResponse.text();
      console.log('📝 Error details:', errorData);
    }
  } catch (error) {
    console.log('❌ Student creation endpoint error:', error.message);
  }

  try {
    // Test 3: Test direct fetch to verify CSP is working
    console.log('\n3️⃣ Testing direct fetch to verify CSP...');
    const directResponse = await fetch('/api/students/', { 
      method: 'GET' 
    });
    
    console.log('✅ Direct fetch successful - CSP is not blocking');
    console.log('📊 Status:', directResponse.status);
    
  } catch (error) {
    console.log('❌ Direct fetch failed:', error.message);
  }

  // Test 4: Check current CSP configuration
  console.log('\n4️⃣ Checking CSP Configuration...');
  const metaElement = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  if (metaElement) {
    console.log('⚠️ Found meta CSP tag:', metaElement.content);
    console.log('💡 Remove this tag - it may override Next.js CSP');
  } else {
    console.log('✅ No meta CSP tag found - using Next.js default');
  }

  console.log('\n🎯 Browser CSP Verification Complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. Check Network tab shows requests to /api/students/ (not localhost:8000)');
  console.log('2. Verify no CSP violations in console');
  console.log('3. Test student creation in the UI');
  console.log('4. Check that toast messages appear on success/error');
}

// Export for browser console use
if (typeof window !== 'undefined') {
  window.testBrowserCsp = testBrowserCsp;
  console.log('🧪 CSP Test ready! Run: testBrowserCsp()');
}

// Run automatically if in browser
if (typeof window !== 'undefined') {
  testBrowserCsp();
}
