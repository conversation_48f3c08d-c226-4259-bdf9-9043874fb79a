'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, FileText, Save } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock results data
const mockResults = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    examName: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'Grade 10',
    marksObtained: 85,
    totalMarks: 100,
    teacher: 'Dr. <PERSON>',
    remarks: 'Excellent performance in algebra and geometry sections.',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    examName: 'Physics Final',
    subject: 'Physics',
    grade: 'Grade 11',
    marksObtained: 92,
    totalMarks: 100,
    teacher: 'Prof. <PERSON>',
    remarks: 'Outstanding understanding of physics concepts.',
  },
];

export default function EditResultPage() {
  const { id } = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const record = mockResults.find(r => r.id === Number(id));
    if (record) {
      setFormData(record);
    }
  }, [id]);

  if (!formData) {
    return <p className='p-6'>Result not found</p>;
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated result:', formData);
      router.push(`/dashboard/results/${id}`);
    } catch (error) {
      console.error('Error updating result:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History'];
  const grades = ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
  const teachers = ['Dr. Sarah Johnson', 'Prof. Michael Chen', 'Ms. Emily Davis', 'Mr. John Smith'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/results/${id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Result
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <FileText className='w-8 h-8 text-blue-600' />
            Edit Result
          </h1>
          <p className='text-gray-600 mt-1'>Update exam result details</p>
        </div>
      </div>

      <div className='space-y-8'>
        <Card>
          <CardHeader>
            <CardTitle>Result Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='studentName'>Student Name *</Label>
                <Input
                  id='studentName'
                  value={formData.studentName}
                  onChange={e => handleChange('studentName', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='studentId'>Student ID *</Label>
                <Input
                  id='studentId'
                  value={formData.studentId}
                  onChange={e => handleChange('studentId', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='examName'>Exam Name *</Label>
              <Input
                id='examName'
                value={formData.examName}
                onChange={e => handleChange('examName', e.target.value)}
                required
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='subject'>Subject *</Label>
                <Select value={formData.subject} onValueChange={value => handleChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select subject' />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='grade'>Grade *</Label>
                <Select value={formData.grade} onValueChange={value => handleChange('grade', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select grade' />
                  </SelectTrigger>
                  <SelectContent>
                    {grades.map(grade => (
                      <SelectItem key={grade} value={grade}>
                        {grade}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='marksObtained'>Marks Obtained *</Label>
                <Input
                  id='marksObtained'
                  type='number'
                  min='0'
                  value={formData.marksObtained}
                  onChange={e => handleChange('marksObtained', parseInt(e.target.value))}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='totalMarks'>Total Marks *</Label>
                <Input
                  id='totalMarks'
                  type='number'
                  min='1'
                  value={formData.totalMarks}
                  onChange={e => handleChange('totalMarks', parseInt(e.target.value))}
                  required
                />
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='teacher'>Teacher *</Label>
              <Select value={formData.teacher} onValueChange={value => handleChange('teacher', value)}>
                <SelectTrigger>
                  <SelectValue placeholder='Select teacher' />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map(teacher => (
                    <SelectItem key={teacher} value={teacher}>
                      {teacher}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='remarks'>Remarks</Label>
              <Textarea
                id='remarks'
                placeholder='Enter teacher remarks...'
                value={formData.remarks}
                onChange={e => handleChange('remarks', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/results/${id}`}>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Updating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Update Result
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
