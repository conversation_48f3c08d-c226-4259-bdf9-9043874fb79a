/**
 * Teacher API Service
 *
 * Centralized API functions for teacher-related operations with:
 * - Real API integration
 * - Dummy data fallback for development
 * - Proper TypeScript typing
 * - Error handling
 * - Filtering and pagination
 */

import {
  createMockTeacher,
  deleteMockTeacher,
  filterMockTeachers,
  getMockTeacherById,
  mockTeachers,
  paginateMockTeachers,
  updateMockTeacher,
} from '@/lib/mockTeachers';
import { Teacher, TeacherCreate, TeacherUpdate } from '@/schemas/zodSchemas';
import type { PaginatedResponse } from '@/types';
import { apiUtils } from '../apiClient';

// Configuration
const USE_DUMMY_DATA = process.env.NEXT_PUBLIC_USE_DUMMY_DATA === 'true' || true;

// API endpoints
const ENDPOINTS = {
  teachers: '/teachers',
  teacher: (id: string) => `/teachers/${id}`,
} as const;

// Types for API parameters
export interface TeacherFilters {
  search?: string;
  department?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TeacherStats {
  total: number;
  active: number;
  inactive: number;
  departments: number;
  averageExperience: number;
}

// Teacher Service Functions
export const teacherService = {
  // Get all teachers with filtering and pagination
  async getTeachers(filters: TeacherFilters = {}): Promise<PaginatedResponse<Teacher>> {
    if (USE_DUMMY_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Apply filters to mock data
      const filterParams: {
        search?: string;
        department?: string;
        status?: string;
        subject?: string;
      } = {};

      if (filters.search) {
        filterParams.search = filters.search;
      }
      if (filters.department) {
        filterParams.department = filters.department;
      }
      if (filters.status) {
        filterParams.status = filters.status;
      }

      const filteredTeachers = filterMockTeachers(filterParams);

      // Apply pagination
      const paginatedTeachers = paginateMockTeachers(
        filteredTeachers,
        filters.page || 1,
        filters.pageSize || 12
      );

      return paginatedTeachers;
    }

    // Real API call
    const teachersResponse = await apiClient.get<PaginatedResponse<Teacher>>(ENDPOINTS.teachers, {
      params: filters,
    });

    return teachersResponse.data;
  },

  // Get single teacher by ID
  async getTeacher(id: string): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 500));

      const teacher = getMockTeacherById(id);
      if (!teacher) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return teacher;
    }

    const teacherResponse = await apiClient.get<Teacher>(ENDPOINTS.teacher(id));
    return teacherResponse.data;
  },

  // Create new teacher
  async createTeacher(teacherData: TeacherCreate): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newTeacher = createMockTeacher(teacherData);
      return newTeacher;
    }

    const createResponse = await apiClient.post<Teacher>(ENDPOINTS.teachers, teacherData);
    return createResponse.data;
  },

  // Update existing teacher
  async updateTeacher(id: string, teacherData: TeacherUpdate): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Filter out undefined values to satisfy exactOptionalPropertyTypes
      const filteredData: Partial<Teacher> = {};
      Object.entries(teacherData).forEach(([key, value]) => {
        if (value !== undefined) {
          (filteredData as any)[key] = value;
        }
      });

      const updatedTeacher = updateMockTeacher(id, filteredData);
      if (!updatedTeacher) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return updatedTeacher;
    }

    const updateResponse = await apiClient.put<Teacher>(ENDPOINTS.teacher(id), teacherData);
    return updateResponse.data;
  },

  // Delete teacher
  async deleteTeacher(id: string): Promise<{ success: boolean }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 800));

      const deleteSuccess = deleteMockTeacher(id);
      if (!deleteSuccess) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return { success: true };
    }

    await apiClient.delete(ENDPOINTS.teacher(id));
    return { success: true };
  },

  // Get teacher statistics
  async getTeacherStats(): Promise<TeacherStats> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const allTeachers = mockTeachers;
      const activeTeachers = allTeachers.filter(teacher => teacher.status === 'ACTIVE');
      const departmentCount = new Set(allTeachers.map(teacher => teacher.department)).size;

      // Calculate average experience (years since hire date)
      const currentYear = new Date().getFullYear();
      const totalExperience = allTeachers.reduce((sum, teacher) => {
        if (!teacher.hire_date) return sum;
        const hireYear = new Date(teacher.hire_date).getFullYear();
        return sum + (currentYear - hireYear);
      }, 0);

      return {
        total: allTeachers.length,
        active: activeTeachers.length,
        inactive: allTeachers.length - activeTeachers.length,
        departments: departmentCount,
        averageExperience: Math.round(totalExperience / allTeachers.length),
      };
    }

    const statsResponse = await apiClient.get<TeacherStats>('/teachers/stats');
    return statsResponse.data;
  },

  // Bulk operations
  async bulkUpdateTeachers(
    updates: Array<{ id: string; data: TeacherUpdate }>
  ): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const updatedTeachers = updates.map(({ id, data: teacherUpdateData }) => {
        // Filter out undefined values to satisfy exactOptionalPropertyTypes
        const filteredData: Partial<Teacher> = {};
        Object.entries(teacherUpdateData).forEach(([key, value]) => {
          if (value !== undefined) {
            (filteredData as any)[key] = value;
          }
        });

        const updatedTeacher = updateMockTeacher(id, filteredData);
        if (!updatedTeacher) {
          throw new Error(`Teacher with ID ${id} not found`);
        }
        return updatedTeacher;
      });

      return updatedTeachers;
    }

    const bulkUpdateResponse = await apiClient.put<Teacher[]>('/teachers/bulk', { updates });
    return bulkUpdateResponse.data;
  },

  async bulkDeleteTeachers(ids: string[]): Promise<{ success: boolean; deleted: number }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1500));

      let deletedCount = 0;
      ids.forEach(teacherId => {
        if (deleteMockTeacher(teacherId)) {
          deletedCount++;
        }
      });

      return { success: true, deleted: deletedCount };
    }

    const bulkDeleteResponse = await apiClient.delete<{ success: boolean; deleted: number }>(
      '/teachers/bulk',
      {
        data: { ids },
      }
    );
    return bulkDeleteResponse.data;
  },

  // Search teachers
  async searchTeachers(query: string, limit = 10): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const filteredTeachers = filterMockTeachers(query ? { search: query } : {});
      return filteredTeachers.slice(0, limit);
    }

    const searchResponse = await apiClient.get<Teacher[]>('/teachers/search', {
      params: { q: query, limit },
    });
    return searchResponse.data;
  },

  // Get teachers by department
  async getTeachersByDepartment(department: string): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 400));

      return filterMockTeachers(department ? { department } : {});
    }

    const departmentResponse = await apiClient.get<Teacher[]>('/teachers/by-department', {
      params: { department },
    });
    return departmentResponse.data;
  },
};

// Export individual functions for convenience
export const {
  getTeachers,
  getTeacher,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  getTeacherStats,
  bulkUpdateTeachers,
  bulkDeleteTeachers,
  searchTeachers,
  getTeachersByDepartment,
} = teacherService;
