'use client';

/**
 * Create Result Page - Professional Result Entry Form
 *
 * Features:
 * - Comprehensive result entry form
 * - Student and exam selection
 * - Grade calculation and validation
 * - Responsive design
 * - Loading states and error handling
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, BarChart3, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for dropdowns
const mockStudents = [
  { id: '1', name: '<PERSON>', studentId: 'STU001', grade: 'Grade 10' },
  { id: '2', name: '<PERSON>', studentId: 'STU002', grade: 'Grade 10' },
  { id: '3', name: '<PERSON>', studentId: 'STU003', grade: 'Grade 11' },
  { id: '4', name: '<PERSON>', studentId: 'STU004', grade: 'Grade 11' },
  { id: '5', name: 'Eva Brown', studentId: 'STU005', grade: 'Grade 12' },
];

const mockExams = [
  { id: '1', title: 'Mathematics Midterm', subject: 'Mathematics', maxScore: 100 },
  { id: '2', title: 'Physics Final Exam', subject: 'Physics', maxScore: 100 },
  { id: '3', title: 'English Literature Essay', subject: 'English', maxScore: 100 },
  { id: '4', title: 'Chemistry Lab Report', subject: 'Chemistry', maxScore: 100 },
  { id: '5', title: 'Biology Quiz', subject: 'Biology', maxScore: 50 },
];

const mockTeachers = [
  { id: '1', name: 'Dr. Sarah Johnson', subject: 'Mathematics' },
  { id: '2', name: 'Prof. Michael Chen', subject: 'Physics' },
  { id: '3', name: 'Ms. Emily Davis', subject: 'English Literature' },
  { id: '4', name: 'Dr. Robert Wilson', subject: 'Chemistry' },
  { id: '5', name: 'Ms. Lisa Anderson', subject: 'Biology' },
];

const resultStatuses = [
  { id: 'published', name: 'Published' },
  { id: 'draft', name: 'Draft' },
  { id: 'under-review', name: 'Under Review' },
];

interface ResultFormData {
  student: string;
  exam: string;
  teacher: string;
  score: string;
  maxScore: string;
  status: string;
  examDate: string;
  comments: string;
}

export default function CreateResultPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ResultFormData>({
    student: '',
    exam: '',
    teacher: '',
    score: '',
    maxScore: '100',
    status: 'draft',
    examDate: '',
    comments: '',
  });

  const handleInputChange = (field: keyof ResultFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const calculateGrade = (score: number, maxScore: number): string => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 97) return 'A+';
    if (percentage >= 93) return 'A';
    if (percentage >= 90) return 'A-';
    if (percentage >= 87) return 'B+';
    if (percentage >= 83) return 'B';
    if (percentage >= 80) return 'B-';
    if (percentage >= 77) return 'C+';
    if (percentage >= 73) return 'C';
    if (percentage >= 70) return 'C-';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  const getCalculatedValues = () => {
    const score = parseFloat(formData.score) || 0;
    const maxScore = parseFloat(formData.maxScore) || 100;
    const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
    const grade = calculateGrade(score, maxScore);
    return { percentage, grade };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const { percentage, grade } = getCalculatedValues();
      console.log('Creating result:', { ...formData, percentage, grade });
      
      // Redirect to results list on success
      router.push('/dashboard/results');
    } catch (error) {
      console.error('Error creating result:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const { percentage, grade } = getCalculatedValues();

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href='/dashboard/results'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Results
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <BarChart3 className='w-8 h-8 text-blue-600' />
            Add New Result
          </h1>
          <p className='text-gray-600 mt-1'>Enter student exam result and grade</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-8'>
        {/* Student & Exam Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Student & Exam Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='student'>Student *</Label>
                <Select value={formData.student} onValueChange={value => handleInputChange('student', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select student' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockStudents.map(student => (
                      <SelectItem key={student.id} value={student.id}>
                        {student.name} ({student.studentId}) - {student.grade}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='exam'>Exam/Assessment *</Label>
                <Select value={formData.exam} onValueChange={value => handleInputChange('exam', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select exam' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockExams.map(exam => (
                      <SelectItem key={exam.id} value={exam.id}>
                        {exam.title} - {exam.subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='teacher'>Teacher *</Label>
                <Select value={formData.teacher} onValueChange={value => handleInputChange('teacher', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select teacher' />
                  </SelectTrigger>
                  <SelectContent>
                    {mockTeachers.map(teacher => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        {teacher.name} - {teacher.subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='examDate'>Exam Date *</Label>
                <Input
                  id='examDate'
                  type='date'
                  value={formData.examDate}
                  onChange={e => handleInputChange('examDate', e.target.value)}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Score & Grade */}
        <Card>
          <CardHeader>
            <CardTitle>Score & Grade</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='score'>Score Obtained *</Label>
                <Input
                  id='score'
                  type='number'
                  min='0'
                  step='0.5'
                  placeholder='e.g., 85'
                  value={formData.score}
                  onChange={e => handleInputChange('score', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='maxScore'>Maximum Score *</Label>
                <Input
                  id='maxScore'
                  type='number'
                  min='1'
                  placeholder='e.g., 100'
                  value={formData.maxScore}
                  onChange={e => handleInputChange('maxScore', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='status'>Status</Label>
                <Select value={formData.status} onValueChange={value => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    {resultStatuses.map(status => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Calculated Values Display */}
            {formData.score && formData.maxScore && (
              <div className='bg-gray-50 rounded-lg p-4'>
                <h4 className='font-medium text-gray-900 mb-3'>Calculated Results</h4>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <p className='text-sm text-gray-600'>Percentage</p>
                    <p className='text-2xl font-bold text-blue-600'>{percentage}%</p>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>Letter Grade</p>
                    <p className='text-2xl font-bold text-green-600'>{grade}</p>
                  </div>
                </div>
              </div>
            )}

            <div className='space-y-2'>
              <Label htmlFor='comments'>Comments (Optional)</Label>
              <Textarea
                id='comments'
                placeholder='Additional comments about the result...'
                value={formData.comments}
                onChange={e => handleInputChange('comments', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href='/dashboard/results'>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Saving...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Save Result
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
