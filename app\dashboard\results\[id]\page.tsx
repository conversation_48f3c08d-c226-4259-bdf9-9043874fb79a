import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Award, BarChart3, BookOpen, Calendar, Edit, FileText, User } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Mock results data
const mockResults = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    examName: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'Grade 10',
    marksObtained: 85,
    totalMarks: 100,
    percentage: 85,
    gradePoint: 'A',
    status: 'Pass',
    examDate: '2024-03-15',
    publishedDate: '2024-03-20',
    teacher: 'Dr. <PERSON>',
    remarks: 'Excellent performance in algebra and geometry sections.',
    createdAt: '2024-03-20T10:00:00Z',
    updatedAt: '2024-03-20T10:00:00Z',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    examName: 'Physics Final',
    subject: 'Physics',
    grade: 'Grade 11',
    marksObtained: 92,
    totalMarks: 100,
    percentage: 92,
    gradePoint: 'A+',
    status: 'Pass',
    examDate: '2024-03-18',
    publishedDate: '2024-03-22',
    teacher: 'Prof. Michael Chen',
    remarks: 'Outstanding understanding of physics concepts.',
    createdAt: '2024-03-22T14:30:00Z',
    updatedAt: '2024-03-22T14:30:00Z',
  },
];

interface ResultDetailPageProps {
  params: {
    id: string;
  };
}

export default function ResultDetailPage({ params }: ResultDetailPageProps) {
  const result = mockResults.find(r => r.id === parseInt(params.id));

  if (!result) {
    notFound();
  }

  const getGradeColor = (gradePoint: string) => {
    switch (gradePoint) {
      case 'A+': return 'bg-green-100 text-green-800';
      case 'A': return 'bg-green-100 text-green-800';
      case 'B+': return 'bg-blue-100 text-blue-800';
      case 'B': return 'bg-blue-100 text-blue-800';
      case 'C+': return 'bg-yellow-100 text-yellow-800';
      case 'C': return 'bg-yellow-100 text-yellow-800';
      case 'D': return 'bg-orange-100 text-orange-800';
      case 'F': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pass': return 'bg-green-100 text-green-800';
      case 'Fail': return 'bg-red-100 text-red-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/results'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Results
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <FileText className='w-8 h-8 text-blue-600' />
              Result Details
            </h1>
            <p className='text-gray-600 mt-1'>View detailed exam result information</p>
          </div>
        </div>
        <Link href={`/dashboard/results/${result.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Result
          </Button>
        </Link>
      </div>

      {/* Result Details */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div className='space-y-2'>
                  <CardTitle className='text-2xl'>{result.examName}</CardTitle>
                  <div className='flex gap-2'>
                    <Badge variant='outline'>{result.subject}</Badge>
                    <Badge variant='outline'>{result.grade}</Badge>
                    <Badge className={getGradeColor(result.gradePoint)}>
                      Grade {result.gradePoint}
                    </Badge>
                    <Badge className={getStatusColor(result.status)}>
                      {result.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Student Information */}
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-6'>
                <div className='flex items-center gap-2'>
                  <User className='w-5 h-5 text-blue-600' />
                  <div>
                    <p className='font-medium'>Student</p>
                    <p className='text-sm text-muted-foreground'>{result.studentName}</p>
                    <p className='text-xs text-muted-foreground'>{result.studentId}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <BookOpen className='w-5 h-5 text-green-600' />
                  <div>
                    <p className='font-medium'>Teacher</p>
                    <p className='text-sm text-muted-foreground'>{result.teacher}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Score Information */}
              <div className='space-y-4'>
                <h3 className='font-semibold flex items-center gap-2'>
                  <BarChart3 className='w-4 h-4' />
                  Score Breakdown
                </h3>
                
                <div className='grid grid-cols-1 sm:grid-cols-3 gap-4'>
                  <div className='text-center p-4 bg-blue-50 rounded-lg'>
                    <div className='text-2xl font-bold text-blue-600'>
                      {result.marksObtained}
                    </div>
                    <p className='text-sm text-muted-foreground'>Marks Obtained</p>
                  </div>
                  <div className='text-center p-4 bg-gray-50 rounded-lg'>
                    <div className='text-2xl font-bold text-gray-600'>
                      {result.totalMarks}
                    </div>
                    <p className='text-sm text-muted-foreground'>Total Marks</p>
                  </div>
                  <div className='text-center p-4 bg-green-50 rounded-lg'>
                    <div className='text-2xl font-bold text-green-600'>
                      {result.percentage}%
                    </div>
                    <p className='text-sm text-muted-foreground'>Percentage</p>
                  </div>
                </div>

                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span>Performance</span>
                    <span>{result.percentage}%</span>
                  </div>
                  <Progress value={result.percentage} className='h-2' />
                </div>
              </div>

              <Separator />

              {/* Dates */}
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-4 h-4 text-purple-600' />
                  <div>
                    <p className='font-medium'>Exam Date</p>
                    <p className='text-sm text-muted-foreground'>{result.examDate}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-4 h-4 text-orange-600' />
                  <div>
                    <p className='font-medium'>Published Date</p>
                    <p className='text-sm text-muted-foreground'>{result.publishedDate}</p>
                  </div>
                </div>
              </div>

              {/* Remarks */}
              {result.remarks && (
                <>
                  <Separator />
                  <div>
                    <h3 className='font-semibold mb-2'>Teacher's Remarks</h3>
                    <p className='text-gray-700 leading-relaxed'>
                      {result.remarks}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* Grade Summary */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Award className='w-5 h-5' />
                Grade Summary
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center'>
                <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${getGradeColor(result.gradePoint).replace('text-', 'text-').replace('bg-', 'bg-')}`}>
                  {result.gradePoint}
                </div>
                <p className='text-sm text-muted-foreground mt-2'>Final Grade</p>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>
                  {result.percentage}%
                </div>
                <p className='text-sm text-muted-foreground'>Score Percentage</p>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Created</p>
                <p className='text-sm'>{new Date(result.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Last Updated</p>
                <p className='text-sm'>{new Date(result.updatedAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Result ID</p>
                <p className='text-sm font-mono'>{result.id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <Button className='w-full' variant='outline'>
                Generate Certificate
              </Button>
              <Button className='w-full' variant='outline'>
                Send to Parent
              </Button>
              <Button className='w-full' variant='outline'>
                Print Result
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
