'use client';

/**
 * Fees Report Page - Comprehensive Fee Collection Data Export
 *
 * Features:
 * - Fee collection reports with payment status
 * - CSV, PDF, and Excel export
 * - Financial analytics and summaries
 * - Payment tracking
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bulkExport, EXPORT_CONFIGS, formatCurrencyForExport, formatDateForExport } from '@/lib/export-utils';
import { CreditCard, DollarSign, Download, FileSpreadsheet, FileText, Search, TrendingUp } from 'lucide-react';
import { useState } from 'react';

// Mock fees data for reports
const mockFeesData = [
  {
    id: '1',
    studentName: '<PERSON> <PERSON>',
    studentId: 'STU001',
    class: '10-A',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-03-15',
    paidDate: '2024-03-10',
    status: 'Paid',
    paymentMethod: 'Bank Transfer',
    receiptNumber: 'RCP001',
  },
  {
    id: '2',
    studentName: 'Bob Smith',
    studentId: 'STU002',
    class: '10-B',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-03-15',
    paidDate: '',
    status: 'Pending',
    paymentMethod: '',
    receiptNumber: '',
  },
  {
    id: '3',
    studentName: 'Carol Davis',
    studentId: 'STU003',
    class: '11-A',
    feeType: 'Lab Fee',
    amount: 200,
    dueDate: '2024-03-20',
    paidDate: '2024-03-18',
    status: 'Paid',
    paymentMethod: 'Cash',
    receiptNumber: 'RCP002',
  },
  {
    id: '4',
    studentName: 'David Wilson',
    studentId: 'STU004',
    class: '11-B',
    feeType: 'Library Fee',
    amount: 100,
    dueDate: '2024-02-28',
    paidDate: '',
    status: 'Overdue',
    paymentMethod: '',
    receiptNumber: '',
  },
  {
    id: '5',
    studentName: 'Eva Brown',
    studentId: 'STU005',
    class: '12-A',
    feeType: 'Examination Fee',
    amount: 300,
    dueDate: '2024-04-01',
    paidDate: '2024-03-25',
    status: 'Paid',
    paymentMethod: 'Online',
    receiptNumber: 'RCP003',
  },
];

const classes = ['All Classes', '9-A', '9-B', '10-A', '10-B', '11-A', '11-B', '12-A', '12-B'];
const feeTypes = ['All Types', 'Tuition Fee', 'Lab Fee', 'Library Fee', 'Examination Fee', 'Transport Fee'];
const statuses = ['All Status', 'Paid', 'Pending', 'Overdue', 'Partial'];

export default function FeesReportPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('All Classes');
  const [selectedFeeType, setSelectedFeeType] = useState('All Types');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [isExporting, setIsExporting] = useState(false);

  // Filter fees based on search and filters
  const filteredFees = mockFeesData.filter(fee => {
    const matchesSearch = fee.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === 'All Classes' || fee.class === selectedClass;
    const matchesFeeType = selectedFeeType === 'All Types' || fee.feeType === selectedFeeType;
    const matchesStatus = selectedStatus === 'All Status' || fee.status === selectedStatus;
    
    return matchesSearch && matchesClass && matchesFeeType && matchesStatus;
  });

  // Calculate fee statistics
  const feeStats = {
    totalAmount: filteredFees.reduce((sum, fee) => sum + fee.amount, 0),
    paidAmount: filteredFees.filter(f => f.status === 'Paid').reduce((sum, fee) => sum + fee.amount, 0),
    pendingAmount: filteredFees.filter(f => f.status === 'Pending').reduce((sum, fee) => sum + fee.amount, 0),
    overdueAmount: filteredFees.filter(f => f.status === 'Overdue').reduce((sum, fee) => sum + fee.amount, 0),
    collectionRate: filteredFees.length > 0 
      ? Math.round((filteredFees.filter(f => f.status === 'Paid').length / filteredFees.length) * 100)
      : 0,
  };

  // Export handlers
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    
    try {
      const config = {
        ...EXPORT_CONFIGS.fees,
        filename: `${EXPORT_CONFIGS.fees.filename}-${new Date().toISOString().split('T')[0]}`,
        transformations: {
          amount: formatCurrencyForExport,
          dueDate: formatDateForExport,
          paidDate: (date: string) => date ? formatDateForExport(date) : 'Not Paid',
        }
      };
      
      await bulkExport(filteredFees, format, config);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Partial':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Fee Collection Report'
        description='Generate and export comprehensive fee collection data reports'
        icon={CreditCard}
        badge={{ label: `${filteredFees.length} records`, variant: 'outline' }}
      />

      {/* Fee Statistics */}
      <div className='grid grid-cols-2 lg:grid-cols-5 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-blue-600'>{formatCurrencyForExport(feeStats.totalAmount)}</p>
              <p className='text-sm text-muted-foreground'>Total Amount</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-green-600'>{formatCurrencyForExport(feeStats.paidAmount)}</p>
              <p className='text-sm text-muted-foreground'>Collected</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-yellow-600'>{formatCurrencyForExport(feeStats.pendingAmount)}</p>
              <p className='text-sm text-muted-foreground'>Pending</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-red-600'>{formatCurrencyForExport(feeStats.overdueAmount)}</p>
              <p className='text-sm text-muted-foreground'>Overdue</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-purple-600'>{feeStats.collectionRate}%</p>
              <p className='text-sm text-muted-foreground'>Collection Rate</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <h3 className='text-lg font-semibold mb-2'>Export Options</h3>
              <p className='text-sm text-muted-foreground'>
                Download fee collection data in your preferred format
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => handleExport('csv')}
                disabled={isExporting || filteredFees.length === 0}
              >
                <FileText className='w-4 h-4 mr-2' />
                Export CSV
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('excel')}
                disabled={isExporting || filteredFees.length === 0}
              >
                <FileSpreadsheet className='w-4 h-4 mr-2' />
                Export Excel
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('pdf')}
                disabled={isExporting || filteredFees.length === 0}
              >
                <Download className='w-4 h-4 mr-2' />
                Export PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Class Filter */}
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger>
                <SelectValue placeholder='Select class' />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls} value={cls}>
                    {cls}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Fee Type Filter */}
            <Select value={selectedFeeType} onValueChange={setSelectedFeeType}>
              <SelectTrigger>
                <SelectValue placeholder='Select fee type' />
              </SelectTrigger>
              <SelectContent>
                {feeTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statuses.map(status => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className='mt-4 text-sm text-muted-foreground'>
            Showing {filteredFees.length} fee records
          </div>
        </CardContent>
      </Card>

      {/* Fee Records */}
      <div className='space-y-4'>
        {filteredFees.map(fee => (
          <Card key={fee.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    <DollarSign className='w-5 h-5 text-green-600' />
                    <h3 className='text-lg font-semibold'>{fee.studentName}</h3>
                    <Badge className={getStatusColor(fee.status)}>
                      {fee.status}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-muted-foreground'>
                    <p>ID: {fee.studentId}</p>
                    <p>Class: {fee.class}</p>
                    <p>Fee Type: {fee.feeType}</p>
                    <p>Amount: {formatCurrencyForExport(fee.amount)}</p>
                    <p>Due Date: {formatDateForExport(fee.dueDate)}</p>
                    <p>Paid Date: {fee.paidDate ? formatDateForExport(fee.paidDate) : 'Not Paid'}</p>
                    {fee.paymentMethod && <p>Payment: {fee.paymentMethod}</p>}
                    {fee.receiptNumber && <p>Receipt: {fee.receiptNumber}</p>}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredFees.length === 0 && (
          <div className='text-center py-12'>
            <CreditCard className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No fee records found</h3>
            <p className='text-gray-500'>
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
