/**
 * Enhanced Teachers Page - Complete State Management + API Integration
 *
 * Features:
 * - React Query hooks for data fetching
 * - Zustand auth store integration
 * - API client with dummy data fallback
 * - Professional UI with shadcn/ui components
 * - Real-time search and filtering
 * - Optimistic updates
 * - Error handling and loading states
 * - Responsive design
 */
'use client';

import { Award, Building, Clock, Grid3X3, List, Plus, Search, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState, useMemo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Dummy teacher data
const mockTeachers = [
  {
    id: 1,
    name: '<PERSON>',
    department: 'Mathematics',
    subject: 'Algebra',
    email: '<EMAIL>',
    status: 'ACTIVE',
    experience: 5,
  },
  {
    id: 2,
    name: 'Emily Johnson',
    department: 'Science',
    subject: 'Physics',
    email: '<EMAIL>',
    status: 'ACTIVE',
    experience: 8,
  },
  {
    id: 3,
    name: 'Michael Brown',
    department: 'English',
    subject: 'Literature',
    email: '<EMAIL>',
    status: 'INACTIVE',
    experience: 3,
  },
  {
    id: 4,
    name: 'Sophia Davis',
    department: 'Computer Science',
    subject: 'Programming',
    email: '<EMAIL>',
    status: 'ACTIVE',
    experience: 6,
  },
];

// Extract unique departments
const departments = ['all', ...Array.from(new Set(mockTeachers.map(t => t.department)))];

export default function TeachersPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  // Filtered data
  const filteredTeachers = useMemo(() => {
    return mockTeachers.filter(teacher => {
      const matchesSearch =
        teacher.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        teacher.email.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDept = selectedDepartment === 'all' || teacher.department === selectedDepartment;
      const matchesStatus = selectedStatus === 'all' || teacher.status === selectedStatus;
      return matchesSearch && matchesDept && matchesStatus;
    });
  }, [searchTerm, selectedDepartment, selectedStatus]);

  // Stats
  const stats = {
    total: mockTeachers.length,
    active: mockTeachers.filter(t => t.status === 'ACTIVE').length,
    departments: new Set(mockTeachers.map(t => t.department)).size,
    averageExperience: (
      mockTeachers.reduce((sum, t) => sum + t.experience, 0) / mockTeachers.length
    ).toFixed(1),
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Teachers</h1>
          <p className='text-muted-foreground'>
            Manage and view all teachers in the system
            <Badge variant='outline' className='ml-2'>
              Demo Mode
            </Badge>
          </p>
        </div>
        <Button
          className='w-full sm:w-auto bg-blue-600 hover:bg-blue-700'
          onClick={() => router.push('/dashboard/teachers/create')}
        >
          <Plus className='mr-2 h-4 w-4' /> Add Teacher
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className='p-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <Input
              placeholder='Search teachers...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger>
              <SelectValue placeholder='Select department' />
            </SelectTrigger>
            <SelectContent>
              {departments.map(dept => (
                <SelectItem key={dept} value={dept}>
                  {dept === 'all' ? 'All Departments' : dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger>
              <SelectValue placeholder='Select status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value='ACTIVE'>Active</SelectItem>
              <SelectItem value='INACTIVE'>Inactive</SelectItem>
            </SelectContent>
          </Select>
          <div className='flex gap-2'>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size='icon'
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className='h-4 w-4' />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size='icon'
              onClick={() => setViewMode('table')}
            >
              <List className='h-4 w-4' />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        <StatsCard title='Total' value={stats.total} icon={Users} color='blue' />
        <StatsCard title='Active' value={stats.active} icon={Award} color='green' />
        <StatsCard title='Departments' value={stats.departments} icon={Building} color='purple' />
        <StatsCard
          title='Avg. Experience'
          value={`${stats.averageExperience}y`}
          icon={Clock}
          color='orange'
        />
      </div>

      {/* Content */}
      <Tabs value={viewMode} onValueChange={value => setViewMode(value as 'grid' | 'table')}>
        <TabsList className='grid grid-cols-2 w-full lg:w-[400px]'>
          <TabsTrigger value='grid'>Grid View</TabsTrigger>
          <TabsTrigger value='table'>Table View</TabsTrigger>
        </TabsList>

        {/* Grid View */}
        <TabsContent value='grid' className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
          {filteredTeachers.map(teacher => (
            <TeacherCard key={teacher.id} teacher={teacher} />
          ))}
        </TabsContent>

        {/* Table View */}
        <TabsContent value='table'>
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher</TableHead>
                  <TableHead className='hidden sm:table-cell'>Department</TableHead>
                  <TableHead className='hidden md:table-cell'>Subject</TableHead>
                  <TableHead className='hidden lg:table-cell'>Email</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTeachers.map(teacher => (
                  <TableRow key={teacher.id}>
                    <TableCell>
                      <div className='flex items-center gap-3'>
                        <Avatar>
                          <AvatarImage
                            src={`https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(
                              teacher.name
                            )}`}
                          />
                          <AvatarFallback>
                            {teacher.name
                              ?.split(' ')
                              .map(n => n[0])
                              .join('') ?? '?'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className='font-medium'>{teacher.name}</div>
                          <div className='text-sm text-muted-foreground sm:hidden'>
                            {teacher.department}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className='hidden sm:table-cell'>{teacher.department}</TableCell>
                    <TableCell className='hidden md:table-cell'>{teacher.subject}</TableCell>
                    <TableCell className='hidden lg:table-cell'>{teacher.email}</TableCell>
                    <TableCell>
                      <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
                        {teacher.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Teacher Card
function TeacherCard({ teacher }: { teacher: any }) {
  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardHeader>
        <div className='flex items-center gap-3'>
          <Avatar>
            <AvatarImage
              src={`https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(
                teacher.name
              )}`}
            />
            <AvatarFallback>
              {teacher.name
                ?.split(' ')
                .map(n => n[0])
                .join('') ?? '?'}
            </AvatarFallback>
          </Avatar>
          <div>
            <CardTitle>{teacher.name}</CardTitle>
            <CardDescription>{teacher.subject}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className='text-sm space-y-2'>
        <div>Department: {teacher.department}</div>
        <div>Email: {teacher.email}</div>
        <div>
          Status:{' '}
          <Badge variant={teacher.status === 'ACTIVE' ? 'success' : 'destructive'}>
            {teacher.status}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}

// Stats Card
function StatsCard({
  title,
  value,
  icon: Icon,
  color,
}: {
  title: string;
  value: string | number;
  icon: any;
  color: string;
}) {
  const colorClasses: Record<string, string> = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600',
    orange: 'bg-orange-100 text-orange-600',
  };
  return (
    <Card>
      <CardContent className='p-6 flex items-center gap-3'>
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className='h-4 w-4' />
        </div>
        <div>
          <p className='text-sm text-muted-foreground'>{title}</p>
          <p className='text-2xl font-bold'>{value}</p>
        </div>
      </CardContent>
    </Card>
  );
}
