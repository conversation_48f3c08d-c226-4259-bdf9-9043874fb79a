'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateGradeErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateGradeError({ error, reset }: CreateGradeErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Create Grade Record"
      moduleIcon="🎓"
      backHref="/dashboard/grade"
    />
  );
}
