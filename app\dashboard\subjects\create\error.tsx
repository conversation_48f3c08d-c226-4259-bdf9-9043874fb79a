'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateSubjectErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateSubjectError({ error, reset }: CreateSubjectErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Create Subject"
      moduleIcon="📚"
      backHref="/dashboard/subjects"
    />
  );
}
