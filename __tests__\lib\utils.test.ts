import { cn } from '@/lib/utils'

describe('cn utility function', () => {
  it('merges class names correctly', () => {
    const result = cn('class1', 'class2', 'class3')
    expect(result).toBe('class1 class2 class3')
  })

  it('handles conditional classes', () => {
    const result = cn('base-class', true && 'conditional-class', false && 'hidden-class')
    expect(result).toBe('base-class conditional-class')
  })

  it('handles undefined and null values', () => {
    const result = cn('base-class', undefined, null, 'valid-class')
    expect(result).toBe('base-class valid-class')
  })

  it('handles empty strings', () => {
    const result = cn('base-class', '', 'valid-class')
    expect(result).toBe('base-class valid-class')
  })

  it('merges Tailwind classes correctly', () => {
    // Test that tailwind-merge works correctly
    const result = cn('p-4', 'p-2') // p-2 should override p-4
    expect(result).toBe('p-2')
  })

  it('handles complex Tailwind class merging', () => {
    const result = cn(
      'bg-red-500 text-white p-4',
      'bg-blue-500 p-2' // bg-blue-500 and p-2 should override
    )
    expect(result).toBe('text-white bg-blue-500 p-2')
  })

  it('handles array inputs', () => {
    const result = cn(['class1', 'class2'], 'class3')
    expect(result).toBe('class1 class2 class3')
  })

  it('handles object inputs', () => {
    const result = cn({
      'class1': true,
      'class2': false,
      'class3': true,
    })
    expect(result).toBe('class1 class3')
  })

  it('handles mixed input types', () => {
    const result = cn(
      'base-class',
      ['array-class1', 'array-class2'],
      {
        'object-class1': true,
        'object-class2': false,
      },
      'final-class'
    )
    expect(result).toBe('base-class array-class1 array-class2 object-class1 final-class')
  })

  it('handles responsive Tailwind classes', () => {
    const result = cn('text-sm md:text-base lg:text-lg')
    expect(result).toBe('text-sm md:text-base lg:text-lg')
  })

  it('handles hover and focus states', () => {
    const result = cn('bg-blue-500 hover:bg-blue-600 focus:bg-blue-700')
    expect(result).toBe('bg-blue-500 hover:bg-blue-600 focus:bg-blue-700')
  })

  it('handles dark mode classes', () => {
    const result = cn('bg-white dark:bg-gray-900 text-black dark:text-white')
    expect(result).toBe('bg-white dark:bg-gray-900 text-black dark:text-white')
  })

  it('returns empty string for no inputs', () => {
    const result = cn()
    expect(result).toBe('')
  })

  it('returns empty string for all falsy inputs', () => {
    const result = cn(false, null, undefined, '', 0)
    expect(result).toBe('')
  })
})

// Test date utilities if they exist
describe('Date utilities', () => {
  // These tests would depend on your actual date utility functions
  // Example structure:

  // it('formats date correctly', () => {
  //   const date = new Date('2023-12-25')
  //   const result = formatDate(date, 'MMM dd, yyyy')
  //   expect(result).toBe('Dec 25, 2023')
  // })

  // it('handles invalid dates gracefully', () => {
  //   const result = formatDate('invalid-date')
  //   expect(result).toBe('Invalid Date')
  // })

  // it('calculates relative time correctly', () => {
  //   const now = new Date()
  //   const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  //   const result = getRelativeTime(yesterday)
  //   expect(result).toBe('1 day ago')
  // })
})

// Test validation utilities if they exist
describe('Validation utilities', () => {
  // These tests would depend on your actual validation functions
  // Example structure:

  // it('validates email correctly', () => {
  //   expect(isValidEmail('<EMAIL>')).toBe(true)
  //   expect(isValidEmail('invalid-email')).toBe(false)
  // })

  // it('validates phone number correctly', () => {
  //   expect(isValidPhone('+1234567890')).toBe(true)
  //   expect(isValidPhone('invalid-phone')).toBe(false)
  // })
})

// Test formatting utilities if they exist
describe('Formatting utilities', () => {
  // These tests would depend on your actual formatting functions
  // Example structure:

  // it('formats currency correctly', () => {
  //   expect(formatCurrency(1234.56)).toBe('$1,234.56')
  //   expect(formatCurrency(0)).toBe('$0.00')
  // })

  // it('formats percentage correctly', () => {
  //   expect(formatPercentage(0.1234)).toBe('12.34%')
  //   expect(formatPercentage(1)).toBe('100.00%')
  // })

  // it('truncates text correctly', () => {
  //   const longText = 'This is a very long text that should be truncated'
  //   expect(truncateText(longText, 20)).toBe('This is a very long...')
  // })
})
