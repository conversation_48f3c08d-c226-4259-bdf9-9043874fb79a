/**
 * Users Admin Services
 * 
 * Thin service layer for user management operations.
 * Returns backend types as-is, no transformations.
 */

import { api } from '@/api/apiClient';
import { USERS_ENDPOINTS } from '@/lib/auth-config';
import type {
  User,
  UserListParams,
  UserListResponse,
  AdminUpdateUserPayload,
  UserActivationResponse,
  UserStatsResponse,
} from '@/types/auth';

/**
 * List users with optional filtering and pagination
 */
export async function listUsers(params?: UserListParams): Promise<UserListResponse> {
  const searchParams = new URLSearchParams();
  
  if (params?.search) {
    searchParams.append('search', params.search);
  }
  if (params?.page !== undefined) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.size !== undefined) {
    searchParams.append('size', params.size.toString());
  }
  if (params?.is_active !== undefined) {
    searchParams.append('is_active', params.is_active.toString());
  }
  if (params?.role) {
    searchParams.append('role', params.role);
  }
  
  const queryString = searchParams.toString();
  const url = queryString ? `${USERS_ENDPOINTS.list}?${queryString}` : USERS_ENDPOINTS.list;
  
  const response = await api.get<UserListResponse>(url);
  return response.data;
}

/**
 * Get single user by ID
 */
export async function getUser(id: string): Promise<User> {
  const response = await api.get<User>(USERS_ENDPOINTS.get(id));
  return response.data;
}

/**
 * Update user by ID (admin only)
 */
export async function updateUser(id: string, data: AdminUpdateUserPayload): Promise<User> {
  const response = await api.put<User>(USERS_ENDPOINTS.update(id), data);
  return response.data;
}

/**
 * Deactivate user by ID
 */
export async function deactivateUser(id: string): Promise<UserActivationResponse> {
  const response = await api.patch<UserActivationResponse>(USERS_ENDPOINTS.deactivate(id));
  return response.data;
}

/**
 * Activate user by ID
 */
export async function activateUser(id: string): Promise<UserActivationResponse> {
  const response = await api.patch<UserActivationResponse>(USERS_ENDPOINTS.activate(id));
  return response.data;
}

/**
 * Get user statistics (admin only)
 */
export async function getUserStats(): Promise<UserStatsResponse> {
  const response = await api.get<UserStatsResponse>(USERS_ENDPOINTS.stats);
  return response.data;
}
