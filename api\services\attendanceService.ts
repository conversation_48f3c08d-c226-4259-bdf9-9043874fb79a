/**
 * Attendance Service
 * 
 * Handles all attendance-related API calls
 */

import { apiUtils } from '../apiClient';
import type { 
  Attendance, 
  AttendanceFilters, 
  AttendanceStats,
  AttendanceSummary,
  PaginatedResponse 
} from '../../types';

export interface CreateAttendanceData {
  student_id: string;
  class: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  check_in_time?: string;
  check_out_time?: string;
  teacher_id: string;
  notes?: string;
  late_minutes?: number;
}

export interface UpdateAttendanceData extends Partial<CreateAttendanceData> {}

export interface BulkAttendanceData {
  class: string;
  date: string;
  teacher_id: string;
  records: Array<{
    student_id: string;
    status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
    check_in_time?: string;
    notes?: string;
  }>;
}

export class AttendanceService {
  private static readonly BASE_URL = '/attendance';

  static async getAttendance(query: AttendanceFilters = {}): Promise<PaginatedResponse<Attendance>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<PaginatedResponse<Attendance>>(url);
  }

  static async getAttendanceRecord(id: string): Promise<Attendance> {
    return apiUtils.get<Attendance>(`${this.BASE_URL}/${id}`);
  }

  static async createAttendance(data: CreateAttendanceData): Promise<Attendance> {
    return apiUtils.post<Attendance>(this.BASE_URL, data);
  }

  static async updateAttendance(id: string, data: UpdateAttendanceData): Promise<Attendance> {
    return apiUtils.patch<Attendance>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteAttendance(id: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/${id}`);
  }

  static async getAttendanceStats(): Promise<AttendanceStats> {
    return apiUtils.get<AttendanceStats>(`${this.BASE_URL}/stats`);
  }

  static async bulkCreateAttendance(data: BulkAttendanceData): Promise<{
    created: number;
    failed: number;
    errors: Array<{ studentId: string; error: string }>;
  }> {
    return apiUtils.post<{
      created: number;
      failed: number;
      errors: Array<{ studentId: string; error: string }>;
    }>(`${this.BASE_URL}/bulk`, data);
  }

  static async getDailySummary(date: string): Promise<AttendanceSummary> {
    return apiUtils.get<AttendanceSummary>(`${this.BASE_URL}/summary/daily?date=${date}`);
  }

  static async getClassSummary(className: string, dateFrom?: string, dateTo?: string): Promise<{
    class: string;
    totalDays: number;
    averageAttendance: number;
    students: Array<{
      studentId: string;
      studentName: string;
      totalDays: number;
      presentDays: number;
      absentDays: number;
      lateDays: number;
      attendanceRate: number;
    }>;
  }> {
    const params = new URLSearchParams();
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const url = `${this.BASE_URL}/summary/class/${className}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<{
      class: string;
      totalDays: number;
      averageAttendance: number;
      students: Array<{
        studentId: string;
        studentName: string;
        totalDays: number;
        presentDays: number;
        absentDays: number;
        lateDays: number;
        attendanceRate: number;
      }>;
    }>(url);
  }

  static async getStudentSummary(studentId: string, dateFrom?: string, dateTo?: string): Promise<{
    studentId: string;
    studentName: string;
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    excusedDays: number;
    attendanceRate: number;
    monthlyBreakdown: Array<{
      month: string;
      totalDays: number;
      presentDays: number;
      attendanceRate: number;
    }>;
  }> {
    const params = new URLSearchParams();
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const url = `${this.BASE_URL}/summary/student/${studentId}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<{
      studentId: string;
      studentName: string;
      totalDays: number;
      presentDays: number;
      absentDays: number;
      lateDays: number;
      excusedDays: number;
      attendanceRate: number;
      monthlyBreakdown: Array<{
        month: string;
        totalDays: number;
        presentDays: number;
        attendanceRate: number;
      }>;
    }>(url);
  }
}

export const attendanceService = AttendanceService;
