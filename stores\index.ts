/**
 * Store Index - Centralized State Management
 *
 * Provides centralized access to all stores with:
 * - Store composition patterns
 * - Cross-store communication
 * - Unified store initialization
 * - Store cleanup and reset utilities
 * - Performance monitoring
 * - Development tools integration
 */

// Store imports
export {
  useAuthIsAuthenticated,
  useAuthIsLoading,
  useAuthStore,
  useAuthToken,
  useAuthUser,
} from '@/lib/authStore';
export {
  useNotificationActions,
  useNotificationSelectors,
  useNotificationStore,
  type Notification,
  type NotificationCategory,
  type NotificationFilters,
  type NotificationPreferences,
  type NotificationPriority,
  type NotificationState,
  type NotificationType,
} from './notificationStore';
export {
  useStudentActions,
  useStudentSelectors,
  useStudentStore,
  type BulkOperation,
  type StudentFilters,
  type StudentPagination,
  type StudentSort,
  type StudentState,
} from './studentStore';

// Middleware exports
export {
  composeMiddleware,
  createStoreWithMiddleware,
  developmentMiddleware,
  encryptedPersistenceMiddleware,
  errorHandlingMiddleware,
  loggingMiddleware,
  performanceMiddleware,
  productionMiddleware,
  realtimeSyncMiddleware,
  validationMiddleware,
} from './middleware';

// Store composition utilities
import { useAuthStore } from '@/lib/authStore';
import { useNotificationStore, type NotificationCategory } from './notificationStore';
import { useStudentStore } from './studentStore';

/**
 * Store Composition Hook
 *
 * Provides access to multiple stores with optimized selectors
 * and cross-store communication patterns
 */
export const useStoreComposition = () => {
  // Auth state
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);
  const authError = useAuthStore(state => state.error);

  // Student state
  const studentsLoading = useStudentStore(state => state.isLoading);
  const studentsError = useStudentStore(state => state.error);
  const studentCount = useStudentStore(state => state.getTotalCount());

  // Notification state
  const unreadCount = useNotificationStore(state => state.unreadCount);
  const notificationsError = useNotificationStore(state => state.error);
  const isConnected = useNotificationStore(state => state.isConnected);

  // Computed states
  const hasErrors = !!(authError || studentsError || notificationsError);
  const isLoading = studentsLoading;
  const isReady = isAuthenticated && !isLoading && !hasErrors;

  return {
    // Auth
    isAuthenticated,
    user,
    authError,

    // Students
    studentsLoading,
    studentsError,
    studentCount,

    // Notifications
    unreadCount,
    notificationsError,
    isConnected,

    // Computed
    hasErrors,
    isLoading,
    isReady,
  };
};

/**
 * Store Actions Composition
 *
 * Provides unified access to all store actions
 */
export const useStoreActions = () => {
  // Auth actions
  const login = useAuthStore(state => state.login);
  const logout = useAuthStore(state => state.logout);
  const refreshAuth = useAuthStore(state => state.refreshAuth);

  // Student actions
  const fetchStudents = useStudentStore(state => state.fetchStudents);
  const createStudent = useStudentStore(state => state.createStudent);
  const updateStudent = useStudentStore(state => state.updateStudentData);
  const deleteStudent = useStudentStore(state => state.deleteStudent);

  // Notification actions
  const fetchNotifications = useNotificationStore(state => state.fetchNotifications);
  const markAsRead = useNotificationStore(state => state.markAsRead);
  const dismissNotification = useNotificationStore(state => state.dismissNotification);
  const connectNotifications = useNotificationStore(state => state.connect);

  return {
    // Auth
    login,
    logout,
    refreshAuth,

    // Students
    fetchStudents,
    createStudent,
    updateStudent,
    deleteStudent,

    // Notifications
    fetchNotifications,
    markAsRead,
    dismissNotification,
    connectNotifications,
  };
};

/**
 * Store Initialization Hook
 *
 * Handles initialization of all stores in the correct order
 */
export const useStoreInitialization = () => {
  const initializeAuth = useAuthStore(state => state.initialize);
  const fetchStudents = useStudentStore(state => state.fetchStudents);
  const fetchNotifications = useNotificationStore(state => state.fetchNotifications);
  const connectNotifications = useNotificationStore(state => state.connect);

  const initializeStores = async () => {
    try {
      // 1. Initialize auth first
      initializeAuth();

      // 2. Wait for auth to complete
      const isAuthenticated = useAuthStore.getState().isAuthenticated;

      if (isAuthenticated) {
        // 3. Initialize other stores in parallel
        await Promise.all([fetchStudents(), fetchNotifications()]);

        // 4. Connect real-time features
        connectNotifications();
      }
    } catch (error) {
      console.error('Store initialization error:', error);
    }
  };

  return { initializeStores };
};

/**
 * Store Reset Utilities
 *
 * Provides utilities to reset stores individually or collectively
 */
export const useStoreReset = () => {
  const clearStudents = useStudentStore(state => state.clearStudents);
  const clearNotifications = useNotificationStore(state => state.clearNotifications);
  const logout = useAuthStore(state => state.logout);

  const resetAllStores = () => {
    // Clear all stores
    clearStudents();
    clearNotifications();

    // Logout will clear auth store
    logout('manual');
  };

  const resetUserData = () => {
    // Clear user-specific data but keep auth
    clearStudents();
    clearNotifications();
  };

  return {
    resetAllStores,
    resetUserData,
    clearStudents,
    clearNotifications,
  };
};

/**
 * Cross-Store Communication Patterns
 *
 * Handles communication between different stores
 */
export const useStoreCommunication = () => {
  const addNotification = useNotificationStore(state => state.addNotification);
  const showToast = useNotificationStore(state => state.showToast);

  // Student-related notifications
  const notifyStudentCreated = (studentName: string) => {
    const notification = {
      id: `student_created_${Date.now()}`,
      title: 'Student Created',
      message: `${studentName} has been successfully added to the system.`,
      type: 'success' as const,
      priority: 'medium' as const,
      category: 'academic' as const,
      createdAt: new Date().toISOString(),
      persistent: false,
      actionable: false,
      autoClose: true,
      closeDelay: 5000,
    };

    addNotification(notification);
    showToast(notification);
  };

  const notifyStudentUpdated = (studentName: string) => {
    const notification = {
      id: `student_updated_${Date.now()}`,
      title: 'Student Updated',
      message: `${studentName}'s information has been updated.`,
      type: 'info' as const,
      priority: 'low' as const,
      category: 'academic' as const,
      createdAt: new Date().toISOString(),
      persistent: false,
      actionable: false,
      autoClose: true,
      closeDelay: 3000,
    };

    addNotification(notification);
    showToast(notification);
  };

  const notifyStudentDeleted = (studentName: string) => {
    const notification = {
      id: `student_deleted_${Date.now()}`,
      title: 'Student Removed',
      message: `${studentName} has been removed from the system.`,
      type: 'warning' as const,
      priority: 'medium' as const,
      category: 'academic' as const,
      createdAt: new Date().toISOString(),
      persistent: true,
      actionable: false,
      autoClose: false,
    };

    addNotification(notification);
    showToast(notification);
  };

  // Error notifications
  const notifyError = (
    title: string,
    message: string,
    category: NotificationCategory = 'system'
  ) => {
    const notification = {
      id: `error_${Date.now()}`,
      title,
      message,
      type: 'error' as const,
      priority: 'high' as const,
      category,
      createdAt: new Date().toISOString(),
      persistent: true,
      actionable: false,
      autoClose: false,
    };

    addNotification(notification);
    showToast(notification);
  };

  return {
    notifyStudentCreated,
    notifyStudentUpdated,
    notifyStudentDeleted,
    notifyError,
  };
};

/**
 * Store Performance Monitor
 *
 * Monitors store performance and provides insights
 */
export const useStorePerformance = () => {
  const getStoreStats = () => {
    const authState = useAuthStore.getState();
    const studentState = useStudentStore.getState();
    const notificationState = useNotificationStore.getState();

    return {
      auth: {
        isAuthenticated: authState.isAuthenticated,
        hasUser: !!authState.user,
        hasError: !!authState.error,
      },
      students: {
        count: Object.keys(studentState.students).length,
        selectedCount: studentState.selectedStudentIds.length,
        isLoading: studentState.isLoading,
        hasError: !!studentState.error,
        cacheValid: studentState.isCacheValid(),
      },
      notifications: {
        count: Object.keys(notificationState.notifications).length,
        unreadCount: notificationState.unreadCount,
        activeToasts: notificationState.activeToasts.length,
        isConnected: notificationState.isConnected,
        hasError: !!notificationState.error,
      },
    };
  };

  const logStoreStats = () => {
    if (process.env.NODE_ENV === 'development') {
      console.table(getStoreStats());
    }
  };

  return {
    getStoreStats,
    logStoreStats,
  };
};

/**
 * Store Development Tools
 *
 * Development utilities for debugging and testing stores
 */
export const useStoreDevelopmentTools = () => {
  const populateTestData = () => {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Test data population is only available in development mode');
      return;
    }

    // Populate test students
    const studentStore = useStudentStore.getState();
    const testStudents = [
      {
        id: 'test_1',
        student_id: 'TEST001',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        class_name: 'Test Class A',
        grade_level: '10th Grade',
        status: 'ACTIVE' as const,
        enrollment_date: '2024-01-15',
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
      },
      // Add more test students...
    ];

    studentStore.setStudents(testStudents);

    // Populate test notifications
    const notificationStore = useNotificationStore.getState();
    const testNotifications = [
      {
        id: 'test_notif_1',
        title: 'Test Notification',
        message: 'This is a test notification for development.',
        type: 'info' as const,
        priority: 'medium' as const,
        category: 'system' as const,
        createdAt: new Date().toISOString(),
        persistent: true,
        actionable: false,
        autoClose: false,
      },
    ];

    testNotifications.forEach(notification => {
      notificationStore.addNotification(notification);
    });

    console.log('Test data populated successfully');
  };

  const clearAllData = () => {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Data clearing is only available in development mode');
      return;
    }

    const { resetAllStores } = useStoreReset();
    resetAllStores();

    console.log('All store data cleared');
  };

  return {
    populateTestData,
    clearAllData,
  };
};

// Export types for external use - removed duplicate NotificationCategory
// (NotificationCategory is already exported in the main export block above)
