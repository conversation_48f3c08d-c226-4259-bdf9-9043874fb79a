/**
 * Authentication Layout
 * 
 * Layout for authentication pages (login, register, forgot password)
 * Provides a clean, centered layout for auth forms
 */
export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* School logo/branding */}
        <div className="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-2xl">🎓</span>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          School Management System
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {children}
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-8 text-center text-sm text-gray-600">
        <p>© 2024 School Management System. All rights reserved.</p>
      </div>
    </div>
  );
}
