'use client';

import { <PERSON><PERSON><PERSON><PERSON>, GraduationCap, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Mock data for demonstration
const mockClasses = [
  {
    id: 1,
    name: 'Grade 10 - Mathematics',
    subject: 'Mathematics',
    teacher: 'Dr. <PERSON>',
    students: 28,
    schedule: 'Mon, Wed, Fri - 9:00 AM',
    room: 'Room 101',
    status: 'Active',
    semester: 'Fall 2024',
    description: 'Advanced mathematics course covering algebra, geometry, and trigonometry for grade 10 students.',
    startDate: '2024-09-01',
    endDate: '2024-12-15',
    credits: 3,
    prerequisites: 'Grade 9 Mathematics',
  },
  {
    id: 2,
    name: 'Grade 9 - Science',
    subject: 'Science',
    teacher: 'Prof. <PERSON>',
    students: 25,
    schedule: 'Tue, Thu - 10:30 AM',
    room: 'Lab 201',
    status: 'Active',
    semester: 'Fall 2024',
    description: 'Comprehensive science course covering physics, chemistry, and biology fundamentals.',
    startDate: '2024-09-01',
    endDate: '2024-12-15',
    credits: 4,
    prerequisites: 'Grade 8 Science',
  },
];

interface ClassFormData {
  name: string;
  subject: string;
  teacher: string;
  room: string;
  schedule: string;
  semester: string;
  status: string;
  credits: string;
  description: string;
  prerequisites: string;
  startDate: string;
  endDate: string;
}

export default function EditClassPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState<ClassFormData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const classItem = mockClasses.find(c => c.id === Number(params.id));
    if (classItem) {
      setFormData({
        name: classItem.name,
        subject: classItem.subject,
        teacher: classItem.teacher,
        room: classItem.room,
        schedule: classItem.schedule,
        semester: classItem.semester,
        status: classItem.status,
        credits: classItem.credits.toString(),
        description: classItem.description,
        prerequisites: classItem.prerequisites,
        startDate: classItem.startDate,
        endDate: classItem.endDate,
      });
    }
  }, [params.id]);

  if (!formData) {
    return <p className='p-6'>Class not found</p>;
  }

  const handleChange = (field: keyof ClassFormData, value: string) => {
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated class:', formData);
      router.push(`/dashboard/classes/${params.id}`);
    } catch (error) {
      console.error('Error updating class:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const subjects = ['Mathematics', 'Science', 'English', 'History', 'Physics', 'Chemistry', 'Biology'];
  const statuses = ['Active', 'Completed', 'Cancelled'];
  const semesters = ['Fall 2024', 'Spring 2024', 'Summer 2024'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/classes/${params.id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Class
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <GraduationCap className='w-8 h-8 text-blue-600' />
            Edit Class
          </h1>
          <p className='text-gray-600 mt-1'>Update class information</p>
        </div>
      </div>

      <div className='space-y-6'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Update the basic class details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label htmlFor='name'>Class Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='subject'>Subject</Label>
                <Select value={formData.subject} onValueChange={(value) => handleChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor='teacher'>Teacher</Label>
                <Input
                  id='teacher'
                  value={formData.teacher}
                  onChange={(e) => handleChange('teacher', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='room'>Room</Label>
                <Input
                  id='room'
                  value={formData.room}
                  onChange={(e) => handleChange('room', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Schedule & Status */}
        <Card>
          <CardHeader>
            <CardTitle>Schedule & Status</CardTitle>
            <CardDescription>Update schedule and status information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label htmlFor='schedule'>Schedule</Label>
                <Input
                  id='schedule'
                  value={formData.schedule}
                  onChange={(e) => handleChange('schedule', e.target.value)}
                  placeholder='e.g., Mon, Wed, Fri - 9:00 AM'
                />
              </div>
              <div>
                <Label htmlFor='semester'>Semester</Label>
                <Select value={formData.semester} onValueChange={(value) => handleChange('semester', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {semesters.map(semester => (
                      <SelectItem key={semester} value={semester}>{semester}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor='status'>Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map(status => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor='credits'>Credits</Label>
                <Input
                  id='credits'
                  type='number'
                  value={formData.credits}
                  onChange={(e) => handleChange('credits', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Duration */}
        <Card>
          <CardHeader>
            <CardTitle>Duration</CardTitle>
            <CardDescription>Set the class start and end dates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label htmlFor='startDate'>Start Date</Label>
                <Input
                  id='startDate'
                  type='date'
                  value={formData.startDate}
                  onChange={(e) => handleChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='endDate'>End Date</Label>
                <Input
                  id='endDate'
                  type='date'
                  value={formData.endDate}
                  onChange={(e) => handleChange('endDate', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Description & Prerequisites */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>Add description and prerequisites</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  rows={4}
                />
              </div>
              <div>
                <Label htmlFor='prerequisites'>Prerequisites</Label>
                <Input
                  id='prerequisites'
                  value={formData.prerequisites}
                  onChange={(e) => handleChange('prerequisites', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/classes/${params.id}`}>
            <Button variant='outline'>Cancel</Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className='w-4 h-4 mr-2' />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
}
