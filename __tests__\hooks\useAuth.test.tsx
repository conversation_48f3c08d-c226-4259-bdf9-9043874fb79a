import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useAuth } from '@/hooks/useAuth'
import { useAuthStore } from '@/stores/authStore'

// Mock the auth store
jest.mock('@/stores/authStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock API client
jest.mock('@/api/apiService', () => ({
  apiClient: {
    post: jest.fn(),
    get: jest.fn(),
    defaults: {
      headers: {
        common: {},
      },
    },
  },
}))

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useAuth', () => {
  const mockLogin = jest.fn()
  const mockLogout = jest.fn()
  const mockRegister = jest.fn()
  const mockClearError = jest.fn()
  const mockSetUser = jest.fn()
  const mockSetToken = jest.fn()

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()

    // Mock default auth store state
    mockUseAuthStore.mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })
  })

  it('returns initial auth state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeNull()
  })

  it('returns authenticated state when user is logged in', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'STUDENT' as const,
    }

    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.user).toEqual(mockUser)
    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.isAdmin).toBe(false)
    expect(result.current.isTeacher).toBe(false)
    expect(result.current.isStudent).toBe(true)
  })

  it('correctly identifies admin role', () => {
    const mockAdminUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'ADMIN' as const,
    }

    mockUseAuthStore.mockReturnValue({
      user: mockAdminUser,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.isAdmin).toBe(true)
    expect(result.current.isTeacher).toBe(false)
    expect(result.current.isStudent).toBe(false)
  })

  it('correctly identifies teacher role', () => {
    const mockTeacherUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Teacher User',
      role: 'TEACHER' as const,
    }

    mockUseAuthStore.mockReturnValue({
      user: mockTeacherUser,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.isAdmin).toBe(false)
    expect(result.current.isTeacher).toBe(true)
    expect(result.current.isStudent).toBe(false)
  })

  it('handles login function call', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: false,
    }

    await act(async () => {
      await result.current.login(loginData)
    })

    expect(mockLogin).toHaveBeenCalledWith(loginData)
  })

  it('handles logout function call', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    await act(async () => {
      await result.current.logout()
    })

    expect(mockLogout).toHaveBeenCalled()
  })

  it('handles register function call', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    const registerData = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User',
      role: 'STUDENT' as const,
    }

    await act(async () => {
      await result.current.register(registerData)
    })

    expect(mockRegister).toHaveBeenCalledWith(registerData)
  })

  it('handles error clearing', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    act(() => {
      result.current.clearError()
    })

    expect(mockClearError).toHaveBeenCalled()
  })

  it('returns loading state during authentication', () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.isLoading).toBe(true)
  })

  it('returns error state when authentication fails', () => {
    const errorMessage = 'Invalid credentials'

    mockUseAuthStore.mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: errorMessage,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    expect(result.current.error).toBe(errorMessage)
  })

  it('handles role-based permissions correctly', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'TEACHER' as const,
    }

    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    // Teacher should have access to teacher features but not admin features
    expect(result.current.isTeacher).toBe(true)
    expect(result.current.isAdmin).toBe(false)
    expect(result.current.isStudent).toBe(false)
  })

  it('handles undefined user role gracefully', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      // role is undefined
    }

    mockUseAuthStore.mockReturnValue({
      user: mockUser as any,
      token: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      login: mockLogin,
      logout: mockLogout,
      register: mockRegister,
      clearError: mockClearError,
      setUser: mockSetUser,
      setToken: mockSetToken,
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: TestWrapper,
    })

    // All role checks should return false for undefined role
    expect(result.current.isAdmin).toBe(false)
    expect(result.current.isTeacher).toBe(false)
    expect(result.current.isStudent).toBe(false)
  })
})
