import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Calendar, Download, Edit, File, FileImage, FileText, FileVideo, Folder, User } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Mock media data
const mockMedia = [
  {
    id: 1,
    name: 'School_Event_2024.jpg',
    type: 'Image',
    size: '2.4 MB',
    format: 'JPEG',
    uploadedBy: 'Admin',
    uploadDate: '2024-03-01',
    category: 'Events',
    status: 'Active',
    url: '/uploads/school_event_2024.jpg',
    description: 'Annual school event photos',
    dimensions: '1920x1080',
    createdAt: '2024-03-01T10:00:00Z',
    updatedAt: '2024-03-01T10:00:00Z',
  },
  {
    id: 2,
    name: 'Student_Handbook_2024.pdf',
    type: 'Document',
    size: '1.8 MB',
    format: 'PDF',
    uploadedBy: 'Principal',
    uploadDate: '2024-02-28',
    category: 'Documents',
    status: 'Active',
    url: '/uploads/student_handbook_2024.pdf',
    description: 'Official student handbook',
    pages: 45,
    createdAt: '2024-02-28T14:30:00Z',
    updatedAt: '2024-02-28T14:30:00Z',
  },
];

interface MediaDetailPageProps {
  params: {
    id: string;
  };
}

export default function MediaDetailPage({ params }: MediaDetailPageProps) {
  const media = mockMedia.find(m => m.id === parseInt(params.id));

  if (!media) {
    notFound();
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Image': return <FileImage className='w-8 h-8 text-green-600' />;
      case 'Video': return <FileVideo className='w-8 h-8 text-blue-600' />;
      case 'Document': return <FileText className='w-8 h-8 text-red-600' />;
      default: return <File className='w-8 h-8 text-gray-600' />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Image': return 'bg-green-100 text-green-800';
      case 'Video': return 'bg-blue-100 text-blue-800';
      case 'Document': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/media'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Media
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <Folder className='w-8 h-8 text-blue-600' />
              Media Details
            </h1>
            <p className='text-gray-600 mt-1'>View media file information and properties</p>
          </div>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline'>
            <Download className='w-4 h-4 mr-2' />
            Download
          </Button>
          <Link href={`/dashboard/media/${media.id}/edit`}>
            <Button>
              <Edit className='w-4 h-4 mr-2' />
              Edit Media
            </Button>
          </Link>
        </div>
      </div>

      {/* Media Details */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div className='space-y-2'>
                  <CardTitle className='text-2xl flex items-center gap-2'>
                    {getTypeIcon(media.type)}
                    {media.name}
                  </CardTitle>
                  <div className='flex gap-2'>
                    <Badge className={getTypeColor(media.type)}>
                      {media.type}
                    </Badge>
                    <Badge variant='outline'>
                      {media.format}
                    </Badge>
                    <Badge variant={media.status === 'Active' ? 'default' : 'secondary'}>
                      {media.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Preview Area */}
              <div className='border-2 border-dashed border-gray-200 rounded-lg p-8'>
                <div className='text-center'>
                  {getTypeIcon(media.type)}
                  <p className='mt-2 text-sm text-gray-500'>
                    {media.type} Preview
                  </p>
                  <p className='text-xs text-gray-400 mt-1'>
                    {media.format} • {media.size}
                  </p>
                </div>
              </div>

              <div>
                <h3 className='font-semibold mb-2'>Description</h3>
                <p className='text-gray-700 leading-relaxed'>
                  {media.description || 'No description provided.'}
                </p>
              </div>

              <Separator />

              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Uploaded:</span> {media.uploadDate}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <User className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>By:</span> {media.uploadedBy}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Folder className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Category:</span> {media.category}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <File className='w-4 h-4 text-gray-500' />
                  <span className='text-sm'>
                    <span className='font-medium'>Size:</span> {media.size}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* File Properties */}
          <Card>
            <CardHeader>
              <CardTitle>File Properties</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>File Name</p>
                <p className='text-sm font-mono'>{media.name}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Format</p>
                <p className='text-sm'>{media.format}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>File Size</p>
                <p className='text-sm'>{media.size}</p>
              </div>
              {media.type === 'Image' && 'dimensions' in media && (
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>Dimensions</p>
                  <p className='text-sm'>{media.dimensions}</p>
                </div>
              )}
              {media.type === 'Document' && 'pages' in media && (
                <div>
                  <p className='text-sm font-medium text-muted-foreground'>Pages</p>
                  <p className='text-sm'>{media.pages}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Created</p>
                <p className='text-sm'>{new Date(media.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Last Modified</p>
                <p className='text-sm'>{new Date(media.updatedAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Media ID</p>
                <p className='text-sm font-mono'>{media.id}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>URL</p>
                <p className='text-sm font-mono text-xs break-all'>{media.url}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
