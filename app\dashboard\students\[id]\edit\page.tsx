'use client';

/**
 * Student Edit Page
 *
 * Features:
 * - Comprehensive student edit form
 * - Form validation with error handling
 * - Auto-save functionality
 * - Image upload for profile picture
 * - Responsive design
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  GraduationCap,
  Mail,
  MapPin,
  Phone,
  Save,
  Upload,
  User,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface StudentFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  date_of_birth: string;
  address: string;
  class_name: string;
  grade_level: string;
  status: string;
  parent_name: string;
  parent_email: string;
  parent_phone: string;
  emergency_contact: string;
  profile_picture?: string;
}

// Mock student data - replace with actual API call
const mockStudent: StudentFormData = {
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  phone: '+****************',
  date_of_birth: '2005-03-15',
  address: '123 Main St, City, State 12345',
  class_name: 'Grade 10A',
  grade_level: '10th Grade',
  status: 'ACTIVE',
  parent_name: 'Jane Doe',
  parent_email: '<EMAIL>',
  parent_phone: '+****************',
  emergency_contact: 'Bob Doe - +****************',
};

export default function StudentEditPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState<StudentFormData>(mockStudent);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Load student data
  useEffect(() => {
    const loadStudent = async () => {
      setIsLoading(true);
      try {
        // TODO: Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setFormData(mockStudent);
      } catch (err) {
        setError('Failed to load student data');
      } finally {
        setIsLoading(false);
      }
    };

    loadStudent();
  }, [params.id]);

  const handleInputChange = (field: keyof StudentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    if (error) setError(null);
  };

  const validateForm = (): string | null => {
    if (!formData.first_name.trim()) return 'First name is required';
    if (!formData.last_name.trim()) return 'Last name is required';
    if (!formData.email.trim()) return 'Email is required';
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) return 'Invalid email format';
    if (!formData.phone.trim()) return 'Phone number is required';
    if (!formData.date_of_birth) return 'Date of birth is required';
    if (!formData.class_name.trim()) return 'Class is required';
    if (!formData.grade_level.trim()) return 'Grade level is required';
    if (!formData.parent_name.trim()) return 'Parent name is required';
    if (!formData.parent_email.trim()) return 'Parent email is required';
    return null;
  };

  const handleSave = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Student updated successfully');
      setHasChanges(false);
      router.push(`/dashboard/students/${params.id}`);
    } catch (err) {
      setError('Failed to update student');
      toast.error('Failed to update student');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
        router.back();
      }
    } else {
      router.back();
    }
  };

  if (isLoading) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='animate-pulse space-y-8'>
            <div className='h-8 bg-gray-200 rounded w-1/4'></div>
            <div className='h-64 bg-gray-200 rounded'></div>
            <div className='h-96 bg-gray-200 rounded'></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button variant='outline' size='sm' onClick={handleCancel}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Cancel
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Edit Student</h1>
            <p className='text-gray-600'>Update student information and details</p>
          </div>
        </div>

        <Button onClick={handleSave} disabled={isSaving || !hasChanges}>
          {isSaving ? (
            <div className='flex items-center space-x-2'>
              <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
              <span>Saving...</span>
            </div>
          ) : (
            <div className='flex items-center space-x-2'>
              <Save className='w-4 h-4' />
              <span>Save Changes</span>
            </div>
          )}
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <div className='max-w-4xl mx-auto space-y-8'>
        {/* Profile Picture */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Picture</CardTitle>
            <CardDescription>Upload a profile picture for the student</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='flex items-center space-x-6'>
              <Avatar className='w-24 h-24'>
                <AvatarImage src={formData.profile_picture} />
                <AvatarFallback className='text-2xl bg-gradient-to-br from-blue-500 to-purple-600 text-white'>
                  {formData.first_name[0]}
                  {formData.last_name[0]}
                </AvatarFallback>
              </Avatar>
              <div>
                <Button variant='outline'>
                  <Upload className='w-4 h-4 mr-2' />
                  Upload Photo
                </Button>
                <p className='text-sm text-gray-600 mt-2'>
                  Recommended: Square image, at least 200x200px
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <User className='w-5 h-5' />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='first_name'>First Name *</Label>
                <Input
                  id='first_name'
                  value={formData.first_name}
                  onChange={e => handleInputChange('first_name', e.target.value)}
                  placeholder='Enter first name'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='last_name'>Last Name *</Label>
                <Input
                  id='last_name'
                  value={formData.last_name}
                  onChange={e => handleInputChange('last_name', e.target.value)}
                  placeholder='Enter last name'
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='email'>Email *</Label>
                <div className='relative'>
                  <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='email'
                    type='email'
                    value={formData.email}
                    onChange={e => handleInputChange('email', e.target.value)}
                    placeholder='<EMAIL>'
                    className='pl-10'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='phone'>Phone Number *</Label>
                <div className='relative'>
                  <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='phone'
                    type='tel'
                    value={formData.phone}
                    onChange={e => handleInputChange('phone', e.target.value)}
                    placeholder='+****************'
                    className='pl-10'
                  />
                </div>
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='date_of_birth'>Date of Birth *</Label>
                <div className='relative'>
                  <Calendar className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='date_of_birth'
                    type='date'
                    value={formData.date_of_birth}
                    onChange={e => handleInputChange('date_of_birth', e.target.value)}
                    className='pl-10'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='status'>Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={value => handleInputChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='ACTIVE'>Active</SelectItem>
                    <SelectItem value='INACTIVE'>Inactive</SelectItem>
                    <SelectItem value='GRADUATED'>Graduated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='address'>Address</Label>
              <div className='relative'>
                <MapPin className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                <Textarea
                  id='address'
                  value={formData.address}
                  onChange={e => handleInputChange('address', e.target.value)}
                  placeholder='Enter full address'
                  className='pl-10'
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Academic Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <GraduationCap className='w-5 h-5' />
              Academic Information
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='class_name'>Class *</Label>
                <Input
                  id='class_name'
                  value={formData.class_name}
                  onChange={e => handleInputChange('class_name', e.target.value)}
                  placeholder='e.g., Grade 10A'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='grade_level'>Grade Level *</Label>
                <Select
                  value={formData.grade_level}
                  onValueChange={value => handleInputChange('grade_level', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='9th Grade'>9th Grade</SelectItem>
                    <SelectItem value='10th Grade'>10th Grade</SelectItem>
                    <SelectItem value='11th Grade'>11th Grade</SelectItem>
                    <SelectItem value='12th Grade'>12th Grade</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Parent/Guardian Information */}
        <Card>
          <CardHeader>
            <CardTitle>Parent/Guardian Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='parent_name'>Parent/Guardian Name *</Label>
                <Input
                  id='parent_name'
                  value={formData.parent_name}
                  onChange={e => handleInputChange('parent_name', e.target.value)}
                  placeholder='Enter parent/guardian name'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='parent_email'>Parent Email *</Label>
                <div className='relative'>
                  <Mail className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='parent_email'
                    type='email'
                    value={formData.parent_email}
                    onChange={e => handleInputChange('parent_email', e.target.value)}
                    placeholder='<EMAIL>'
                    className='pl-10'
                  />
                </div>
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='parent_phone'>Parent Phone</Label>
                <div className='relative'>
                  <Phone className='absolute left-3 top-3 h-4 w-4 text-gray-400' />
                  <Input
                    id='parent_phone'
                    type='tel'
                    value={formData.parent_phone}
                    onChange={e => handleInputChange('parent_phone', e.target.value)}
                    placeholder='+****************'
                    className='pl-10'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='emergency_contact'>Emergency Contact</Label>
                <Input
                  id='emergency_contact'
                  value={formData.emergency_contact}
                  onChange={e => handleInputChange('emergency_contact', e.target.value)}
                  placeholder='Name - Phone Number'
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className='flex items-center justify-end space-x-4 pt-6 border-t'>
          <Button variant='outline' onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving || !hasChanges}>
            {isSaving ? (
              <div className='flex items-center space-x-2'>
                <div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin' />
                <span>Saving...</span>
              </div>
            ) : (
              <div className='flex items-center space-x-2'>
                <Save className='w-4 h-4' />
                <span>Save Changes</span>
              </div>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
