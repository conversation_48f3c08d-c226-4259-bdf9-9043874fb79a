'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Users, Upload, UserPlus } from 'lucide-react';

// Import our components
import { StudentForm, StudentCSVImport } from '@/components/forms';
import { StudentService } from '@/api/services/studentService';
import type { Student, StudentCreate, StudentImportResponse } from '@/types';

/**
 * Test Page for Student Integration
 * 
 * This page provides comprehensive testing for:
 * - Student creation via form
 * - CSV import functionality
 * - Error handling and validation
 * - Success feedback and user experience
 */
export default function TestStudentsPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showImport, setShowImport] = useState(false);

  // Mock data for testing
  const mockClasses = [
    { id: '1', name: 'Class A' },
    { id: '2', name: 'Class B' },
    { id: '3', name: 'Class C' },
  ];

  const mockGrades = [
    { id: '1', name: 'Grade 1' },
    { id: '2', name: 'Grade 2' },
    { id: '3', name: 'Grade 3' },
  ];

  const mockParents = [
    { id: 'parent-1', name: 'John Doe (Parent)' },
    { id: 'parent-2', name: 'Jane Smith (Parent)' },
    { id: 'parent-3', name: 'Bob Johnson (Parent)' },
  ];

  // Handle student creation
  const handleCreateStudent = async (data: StudentCreate) => {
    setIsLoading(true);
    setMessage(null);

    try {
      const newStudent = await StudentService.createStudent(data);
      setStudents(prev => [newStudent, ...prev]);
      setMessage({ type: 'success', text: `Student ${newStudent.first_name} ${newStudent.last_name} created successfully!` });
      setShowForm(false);
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to create student' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle CSV import completion
  const handleImportComplete = (result: StudentImportResponse) => {
    const successMessage = `Import completed: ${result.created} created, ${result.updated} updated`;
    const errorMessage = result.errors.length > 0 ? `, ${result.errors.length} errors` : '';
    
    setMessage({ 
      type: result.errors.length === 0 ? 'success' : 'error', 
      text: successMessage + errorMessage 
    });
    
    // Refresh student list (in a real app, you'd fetch from API)
    if (result.created > 0 || result.updated > 0) {
      setMessage(prev => ({ 
        ...prev!, 
        text: prev!.text + '. Please refresh the page to see updated student list.' 
      }));
    }
    
    setShowImport(false);
  };

  // Load students (mock implementation)
  const loadStudents = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, you would call:
      // const response = await StudentService.getStudents();
      // setStudents(response.items);
      
      // For testing, we'll use mock data
      setStudents([
        {
          id: '1',
          reg_no: 'ADM2024001',
          first_name: 'John',
          last_name: 'Doe',
          gender: 'male',
          dob: '2010-05-15',
          class_id: '1',
          section_id: '1',
          guardian_name: 'Jane Doe',
          guardian_phone: '+1234567890',
          address: '123 Main St',
          email: '<EMAIL>',
          is_active: true,
          created_at: new Date().toISOString(),
        },
      ]);
      setMessage({ type: 'success', text: 'Students loaded successfully' });
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to load students' });
    } finally {
      setIsLoading(false);
    }
  };

  // Test API connectivity
  const testAPIConnectivity = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      // Test the proxy by making a simple API call
      const response = await fetch('/api/v1/students?page=1&size=1');
      
      if (response.ok) {
        setMessage({ type: 'success', text: 'API connectivity test passed! Proxy is working correctly.' });
      } else {
        setMessage({ type: 'error', text: `API test failed with status: ${response.status}` });
      }
    } catch (error: any) {
      if (error.message.includes('fetch')) {
        setMessage({ type: 'error', text: 'Network error - check if backend is running and proxy is configured' });
      } else {
        setMessage({ type: 'error', text: `API test failed: ${error.message}` });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Student Integration Test</h1>
          <p className="text-muted-foreground">
            Test student creation, CSV import, and API integration
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          Test Environment
        </Badge>
      </div>

      {/* Status Message */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="create">Create Student</TabsTrigger>
          <TabsTrigger value="import">CSV Import</TabsTrigger>
          <TabsTrigger value="list">Student List</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Status</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={testAPIConnectivity} 
                  disabled={isLoading}
                  className="w-full"
                >
                  Test API Connection
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Create Student</CardTitle>
                <UserPlus className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => setShowForm(true)} 
                  className="w-full"
                >
                  Open Form
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CSV Import</CardTitle>
                <Upload className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => setShowImport(true)} 
                  className="w-full"
                >
                  Import CSV
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Integration Checklist</CardTitle>
              <CardDescription>
                Verify all components are working correctly
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>✅ Backend CSV import endpoint implemented</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>✅ Frontend student form with backend schema alignment</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>✅ CSV import UI component with validation</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>✅ Client-side validation to prevent 500 errors</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>✅ API proxy configuration for CSP/CORS</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Create Student Tab */}
        <TabsContent value="create">
          {showForm ? (
            <StudentForm
              mode="create"
              onSubmit={handleCreateStudent}
              onCancel={() => setShowForm(false)}
              isLoading={isLoading}
              availableClasses={mockClasses}
              availableGrades={mockGrades}
              availableParents={mockParents}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Create New Student</CardTitle>
                <CardDescription>
                  Test the student creation form with backend schema alignment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowForm(true)}>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Open Student Form
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* CSV Import Tab */}
        <TabsContent value="import">
          {showImport ? (
            <StudentCSVImport
              onImportComplete={handleImportComplete}
              onCancel={() => setShowImport(false)}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>CSV Import</CardTitle>
                <CardDescription>
                  Test bulk student import with comprehensive error handling
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowImport(true)}>
                  <Upload className="w-4 h-4 mr-2" />
                  Open CSV Import
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Student List Tab */}
        <TabsContent value="list" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Student List</h3>
            <Button onClick={loadStudents} disabled={isLoading}>
              {isLoading ? 'Loading...' : 'Refresh List'}
            </Button>
          </div>

          <div className="grid gap-4">
            {students.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    No students found. Create a student or import from CSV to see them here.
                  </p>
                </CardContent>
              </Card>
            ) : (
              students.map((student) => (
                <Card key={student.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">
                          {student.first_name} {student.last_name}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {student.reg_no} • {student.email}
                        </p>
                      </div>
                      <Badge variant={student.is_active ? 'default' : 'secondary'}>
                        {student.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
