'use client';

import React, { useState } from 'react';
import { Settings, Globe, Calendar, DollarSign, Download, Zap, Database, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';

interface SystemSettings {
  timezone: string;
  locale: string;
  dateFormat: string;
  timeFormat: string;
  currency: string;
  currencySymbol: string;
  decimalPlaces: number;
  firstDayOfWeek: string;
  academicYearStart: string;
  performanceMode: boolean;
  debugMode: boolean;
  maintenanceMode: boolean;
  autoBackup: boolean;
  backupRetention: number;
}

// Mock data
const mockSettings: SystemSettings = {
  timezone: 'Asia/Kolkata',
  locale: 'en-IN',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '12',
  currency: 'INR',
  currencySymbol: '₹',
  decimalPlaces: 0,
  firstDayOfWeek: 'monday',
  academicYearStart: 'april',
  performanceMode: true,
  debugMode: false,
  maintenanceMode: false,
  autoBackup: true,
  backupRetention: 30,
};

const timezones = [
  { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
  { value: 'America/New_York', label: 'America/New_York (EST)' },
  { value: 'Europe/London', label: 'Europe/London (GMT)' },
  { value: 'Asia/Dubai', label: 'Asia/Dubai (GST)' },
  { value: 'Asia/Singapore', label: 'Asia/Singapore (SGT)' },
];

const locales = [
  { value: 'en-IN', label: 'English (India)' },
  { value: 'en-US', label: 'English (US)' },
  { value: 'en-GB', label: 'English (UK)' },
  { value: 'hi-IN', label: 'Hindi (India)' },
];

const dateFormats = [
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (31/12/2024)' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (12/31/2024)' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (2024-12-31)' },
  { value: 'DD-MM-YYYY', label: 'DD-MM-YYYY (31-12-2024)' },
];

const currencies = [
  { value: 'INR', label: 'Indian Rupee (₹)', symbol: '₹' },
  { value: 'USD', label: 'US Dollar ($)', symbol: '$' },
  { value: 'EUR', label: 'Euro (€)', symbol: '€' },
  { value: 'GBP', label: 'British Pound (£)', symbol: '£' },
];

interface SystemSettingsFormProps {
  onDataChange?: () => void;
}

/**
 * SystemSettingsForm Component
 * 
 * Features:
 * - Timezone, locale, date format configuration
 * - Currency and decimal settings
 * - Performance and development toggles
 * - Data export functionality
 * - System maintenance controls
 */
export function SystemSettingsForm({ onDataChange }: SystemSettingsFormProps) {
  const { toast } = useToast();
  const [settings, setSettings] = useState<SystemSettings>(mockSettings);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);

  // Handle setting change
  const handleSettingChange = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
    onDataChange?.();

    // Auto-update currency symbol when currency changes
    if (key === 'currency') {
      const currency = currencies.find(c => c.value === value);
      if (currency) {
        setSettings(prev => ({ ...prev, currencySymbol: currency.symbol }));
      }
    }
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasUnsavedChanges(false);
      
      toast({
        title: 'Settings saved',
        description: 'System settings have been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save system settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle data export
  const handleDataExport = async (format: 'csv' | 'json') => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      for (let i = 0; i <= 100; i += 10) {
        setExportProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Create and download file
      const data = format === 'json' 
        ? JSON.stringify(settings, null, 2)
        : 'Setting,Value\n' + Object.entries(settings).map(([k, v]) => `${k},${v}`).join('\n');
      
      const blob = new Blob([data], { type: format === 'json' ? 'application/json' : 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `system-settings.${format}`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: 'Export completed',
        description: `System settings exported as ${format.toUpperCase()} file.`,
      });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export system settings.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
              <Settings className="h-6 w-6 text-sky-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-slate-900">System Settings</h2>
              <p className="text-slate-600">Configure system-wide preferences and behavior</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {hasUnsavedChanges && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className="bg-gradient-to-r from-sky-600 to-violet-600"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Localization Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-sky-600" />
              Localization
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Timezone</Label>
              <Select
                value={settings.timezone}
                onValueChange={(value) => handleSettingChange('timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Language & Locale</Label>
              <Select
                value={settings.locale}
                onValueChange={(value) => handleSettingChange('locale', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {locales.map((locale) => (
                    <SelectItem key={locale.value} value={locale.value}>
                      {locale.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date Format</Label>
                <Select
                  value={settings.dateFormat}
                  onValueChange={(value) => handleSettingChange('dateFormat', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {dateFormats.map((format) => (
                      <SelectItem key={format.value} value={format.value}>
                        {format.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Time Format</Label>
                <Select
                  value={settings.timeFormat}
                  onValueChange={(value) => handleSettingChange('timeFormat', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12">12-hour (AM/PM)</SelectItem>
                    <SelectItem value="24">24-hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>First Day of Week</Label>
                <Select
                  value={settings.firstDayOfWeek}
                  onValueChange={(value) => handleSettingChange('firstDayOfWeek', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sunday">Sunday</SelectItem>
                    <SelectItem value="monday">Monday</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Academic Year Start</Label>
                <Select
                  value={settings.academicYearStart}
                  onValueChange={(value) => handleSettingChange('academicYearStart', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="january">January</SelectItem>
                    <SelectItem value="april">April</SelectItem>
                    <SelectItem value="june">June</SelectItem>
                    <SelectItem value="september">September</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Currency Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-sky-600" />
              Currency & Numbers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Currency</Label>
              <Select
                value={settings.currency}
                onValueChange={(value) => handleSettingChange('currency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Currency Symbol</Label>
                <Input
                  value={settings.currencySymbol}
                  onChange={(e) => handleSettingChange('currencySymbol', e.target.value)}
                  placeholder="₹"
                />
              </div>

              <div className="space-y-2">
                <Label>Decimal Places</Label>
                <Select
                  value={settings.decimalPlaces.toString()}
                  onValueChange={(value) => handleSettingChange('decimalPlaces', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0 (₹100)</SelectItem>
                    <SelectItem value="2">2 (₹100.00)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="p-3 bg-slate-50 rounded-lg">
              <p className="text-sm font-medium mb-1">Preview</p>
              <p className="text-lg">
                {settings.currencySymbol}1,234{settings.decimalPlaces > 0 ? '.00' : ''}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Performance & Development */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-sky-600" />
              Performance & Development
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Performance Mode</Label>
                <p className="text-sm text-slate-600">Enable optimizations for better performance</p>
              </div>
              <Switch
                checked={settings.performanceMode}
                onCheckedChange={(checked) => handleSettingChange('performanceMode', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Debug Mode</Label>
                <p className="text-sm text-slate-600">Show detailed error messages and logs</p>
              </div>
              <Switch
                checked={settings.debugMode}
                onCheckedChange={(checked) => handleSettingChange('debugMode', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Maintenance Mode</Label>
                <p className="text-sm text-slate-600">Temporarily disable user access</p>
              </div>
              <Switch
                checked={settings.maintenanceMode}
                onCheckedChange={(checked) => handleSettingChange('maintenanceMode', checked)}
              />
            </div>

            {settings.maintenanceMode && (
              <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <p className="text-sm text-amber-800">
                  ⚠️ Maintenance mode is enabled. Users will see a maintenance page.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-sky-600" />
              Data Management
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Auto Backup</Label>
                <p className="text-sm text-slate-600">Automatically backup data daily</p>
              </div>
              <Switch
                checked={settings.autoBackup}
                onCheckedChange={(checked) => handleSettingChange('autoBackup', checked)}
              />
            </div>

            {settings.autoBackup && (
              <div className="space-y-2">
                <Label>Backup Retention (days)</Label>
                <Input
                  type="number"
                  value={settings.backupRetention}
                  onChange={(e) => handleSettingChange('backupRetention', parseInt(e.target.value))}
                  min="1"
                  max="365"
                />
              </div>
            )}

            <div className="space-y-3">
              <Label>Export Data</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDataExport('csv')}
                  disabled={isExporting}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDataExport('json')}
                  disabled={isExporting}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export JSON
                </Button>
              </div>
              
              {isExporting && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Exporting data...</span>
                    <span>{exportProgress}%</span>
                  </div>
                  <Progress value={exportProgress} className="h-2" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
